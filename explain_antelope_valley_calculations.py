#!/usr/bin/env python3
"""
Explain exactly how PLF and gross generation were calculated for Antelope Valley
"""

import pandas as pd
import sys
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')

def explain_antelope_valley_calculations():
    """Explain the exact calculations used for Antelope Valley"""
    
    print("🔍 EXPLAINING ANTELOPE VALLEY PLF & GROSS GENERATION CALCULATIONS")
    print("=" * 80)
    
    # 1. Show the raw data available
    print("\n📊 STEP 1: RAW DATA AVAILABLE FROM USA DETAILS.XLSX")
    print("-" * 60)
    
    excel_file = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx'
    coal_sheets = ['Coal 2024', 'Coal 2023', 'Coal 2022', 'Coal 2021', 'Coal 2020']
    
    all_antelope_data = {}
    
    for sheet_name in coal_sheets:
        year = sheet_name.split(' ')[1]
        df = pd.read_excel(excel_file, sheet_name=sheet_name)
        
        # Find Antelope Valley data
        antelope_mask = df['Plant Name'].str.contains('Antelope Valley', na=False, case=False)
        antelope_data = df[antelope_mask]
        
        if not antelope_data.empty:
            print(f"\n📅 {year} - Found {len(antelope_data)} records:")
            
            year_records = []
            for idx, row in antelope_data.iterrows():
                record = {
                    'net_generation_mwh': row.get('Net Generation (Megawatthours)', 0),
                    'fuel_consumption': row.get('Electric Fuel Consumption Quantity', 0),
                    'coal_type': row.get('Type', 'Unknown'),
                    'emission_factor': row.get('Emission Factor', 0)
                }
                year_records.append(record)
                
                print(f"  Record {len(year_records)}:")
                print(f"    Net Generation: {record['net_generation_mwh']:,.0f} MWh")
                print(f"    Fuel Consumption: {record['fuel_consumption']:,.0f} units")
                print(f"    Coal Type: {record['coal_type']}")
                print(f"    Emission Factor: {record['emission_factor']}")
            
            all_antelope_data[year] = year_records
    
    # 2. Show the calculation parameters used
    print(f"\n🔧 STEP 2: CALCULATION PARAMETERS USED")
    print("-" * 50)
    
    unit_capacity_mw = 238.5  # Unit 2 capacity
    auxiliary_power_percent = 0.07  # 7% for subcritical technology
    
    print(f"Unit Capacity: {unit_capacity_mw} MW")
    print(f"Auxiliary Power %: {auxiliary_power_percent:.1%}")
    print(f"Technology: subcritical")
    print(f"Hours per year: 8760")
    print(f"PAF (Plant Availability Factor): 1.0 (assumed)")
    
    # 3. Show the exact calculations for each year
    print(f"\n🧮 STEP 3: EXACT CALCULATIONS BY YEAR")
    print("-" * 50)
    
    calculated_results = {}
    
    for year, year_data in all_antelope_data.items():
        print(f"\n📅 {year} CALCULATIONS:")
        
        # For unit-level calculation, use the first non-zero record
        # (This is a simplification - in practice we'd need better unit identification)
        unit_data = None
        for record in year_data:
            if record['net_generation_mwh'] > 0:
                unit_data = record
                break
        
        if unit_data and unit_data['net_generation_mwh'] > 0:
            net_generation_mwh = unit_data['net_generation_mwh']
            
            print(f"  📊 Input Data:")
            print(f"    Net Generation: {net_generation_mwh:,.0f} MWh")
            
            # Calculate gross generation from net generation
            # Formula: Gross = Net / (1 - auxiliary_power_percent)
            gross_generation_mwh = net_generation_mwh / (1 - auxiliary_power_percent)
            
            print(f"  🔧 Gross Generation Calculation:")
            print(f"    Formula: Gross = Net / (1 - Aux%)")
            print(f"    Gross = {net_generation_mwh:,.0f} / (1 - {auxiliary_power_percent:.3f})")
            print(f"    Gross = {net_generation_mwh:,.0f} / {1 - auxiliary_power_percent:.3f}")
            print(f"    Gross = {gross_generation_mwh:,.2f} MWh")
            
            # Calculate maximum possible generation for PLF
            # Formula: Max = Unit_Capacity_MW * 8760 * PAF
            paf = 1.0  # Plant Availability Factor assumed as 1.0
            max_generation_mwh = unit_capacity_mw * 8760 * paf
            
            print(f"  🔧 Maximum Generation Calculation:")
            print(f"    Formula: Max = Capacity × 8760 × PAF")
            print(f"    Max = {unit_capacity_mw} × 8760 × {paf}")
            print(f"    Max = {max_generation_mwh:,.0f} MWh")
            
            # Calculate PLF using Case 4: Unit Level
            # Formula: PLF = Gross_Unit_Generation / (Unit_Capacity_MW * 8760 * PAF)
            plf = gross_generation_mwh / max_generation_mwh
            
            print(f"  🔧 PLF Calculation (Case 4 - Unit Level):")
            print(f"    Formula: PLF = Gross / Max")
            print(f"    PLF = {gross_generation_mwh:,.2f} / {max_generation_mwh:,.0f}")
            print(f"    PLF = {plf:.3f} = {plf:.1%}")
            
            # Cap PLF at 100%
            plf_capped = min(plf, 1.0)
            print(f"    PLF (capped at 100%): {plf_capped:.3f} = {plf_capped:.1%}")
            
            calculated_results[year] = {
                'net_generation_mwh': net_generation_mwh,
                'gross_generation_mwh': gross_generation_mwh,
                'max_generation_mwh': max_generation_mwh,
                'plf': plf_capped,
                'auxiliary_power_percent': auxiliary_power_percent
            }
        else:
            print(f"  ❌ No valid data for {year}")
    
    # 4. Show the final JSON structure
    print(f"\n📄 STEP 4: FINAL JSON STRUCTURE CREATED")
    print("-" * 50)
    
    print("PLF Array:")
    for year, result in sorted(calculated_results.items()):
        print(f"  {year}: {result['plf']:.3f} ({result['plf']:.1%})")
    
    print("\nAuxiliary Power Array:")
    for year in sorted(calculated_results.keys()):
        print(f"  {year}: {auxiliary_power_percent:.1%}")
    
    print("\nGross Power Generation Array:")
    for year, result in sorted(calculated_results.items()):
        print(f"  {year}: {result['gross_generation_mwh']:.2f} MWh")
    
    # 5. Address the unit saving issue
    print(f"\n🚨 STEP 5: UNIT SAVING ISSUE ANALYSIS")
    print("-" * 50)
    
    print("ISSUE: Only Unit 2 JSON exists, but Antelope Valley has 2 units")
    print()
    print("ANALYSIS OF THE PROBLEM:")
    print("1. ✅ Data exists for both units in USA Details.xlsx")
    print("2. ❌ Current system only processes one unit per plant")
    print("3. ❌ Unit identification logic is incomplete")
    print()
    
    # Check how many records exist per year
    print("RECORDS PER YEAR IN USA DETAILS.XLSX:")
    for year, year_data in all_antelope_data.items():
        print(f"  {year}: {len(year_data)} records")
        for i, record in enumerate(year_data):
            status = "✅ Has data" if record['net_generation_mwh'] > 0 else "❌ Zero generation"
            print(f"    Record {i+1}: {record['net_generation_mwh']:,.0f} MWh ({status})")
    
    print(f"\n🔧 STEP 6: WHAT NEEDS TO BE FIXED")
    print("-" * 40)
    
    print("TO FIX THE UNIT SAVING ISSUE:")
    print("1. 🔄 Improve unit identification logic in USA Excel engine")
    print("2. 🔄 Process both units separately instead of just first non-zero record")
    print("3. 🔄 Ensure both Unit 1 and Unit 2 JSONs are created and saved")
    print("4. 🔄 Map the 2 records per year to the correct units")
    
    print(f"\n📋 SUMMARY OF CURRENT CALCULATIONS:")
    print("-" * 40)
    print("✅ PLF calculated using real net generation data from USA Details.xlsx")
    print("✅ Gross generation calculated using auxiliary power formula")
    print("✅ PLF Case 4 (Unit Level) formula implemented correctly")
    print("✅ Time series data for 2020-2024 created")
    print("❌ Only processing one unit instead of both units")
    print("❌ Need to fix unit identification and processing logic")

if __name__ == "__main__":
    explain_antelope_valley_calculations()
