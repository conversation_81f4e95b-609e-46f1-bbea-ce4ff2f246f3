#!/usr/bin/env python3
"""
Test unit detection for Antelope Valley to see why only Unit 2 is being saved
"""

import sys
import os
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')

def test_unit_detection_logic():
    """Test the unit detection logic that determines how many units a plant has"""
    print("🔍 TESTING UNIT DETECTION LOGIC FOR ANTELOPE VALLEY")
    print("=" * 60)
    
    # Simulate typical content that would be found for Antelope Valley
    antelope_valley_content = """
    Antelope Valley Station is a coal-fired power plant located in North Dakota, United States.
    The plant has 2 units with a total capacity of 954 MW.
    Unit 1: 477 MW capacity, commissioned in 1984
    Unit 2: 477 MW capacity, commissioned in 1985
    The plant uses lignite coal as its primary fuel source.
    Both units are subcritical technology.
    """
    
    # Import the unit detection function
    try:
        from backend.src.agent.graph import enhanced_unit_detection
        
        # Test the enhanced unit detection
        session_id = "test_session"
        detected_units = enhanced_unit_detection(antelope_valley_content, session_id)
        
        print(f"✅ Enhanced unit detection result: {detected_units}")
        print(f"📊 Number of units detected: {len(detected_units)}")
        
        if len(detected_units) == 2 and "1" in detected_units and "2" in detected_units:
            print("✅ CORRECT: Both Unit 1 and Unit 2 detected")
        else:
            print("❌ INCORRECT: Expected ['1', '2'] but got:", detected_units)
            
    except Exception as e:
        print(f"❌ Enhanced unit detection failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Also test the fallback unit extraction
    try:
        from backend.src.agent.graph import filter_operational_units
        
        # Test with a list of potential units
        potential_units = ["1", "2"]
        operational_units = filter_operational_units(antelope_valley_content, potential_units, session_id)
        
        print(f"✅ Operational unit filtering result: {operational_units}")
        
        if len(operational_units) == 2:
            print("✅ CORRECT: Both units passed operational filtering")
        else:
            print("❌ INCORRECT: Some units were filtered out:", operational_units)
            
    except Exception as e:
        print(f"❌ Operational unit filtering failed: {e}")
        import traceback
        traceback.print_exc()

def test_usa_excel_unit_info():
    """Test if USA Excel data contains unit information"""
    print("\n🔍 TESTING USA EXCEL DATA FOR UNIT INFORMATION")
    print("=" * 60)
    
    try:
        import pandas as pd
        
        # Check USA Details.xlsx for unit information
        usa_file = "/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx"
        
        # Check Coal 2024 sheet for Antelope Valley
        df_2024 = pd.read_excel(usa_file, sheet_name="Coal 2024")
        
        # Filter for Antelope Valley
        antelope_records = df_2024[df_2024['Plant Name'].str.contains('Antelope Valley', na=False)]
        
        print(f"📊 Found {len(antelope_records)} records for Antelope Valley in Coal 2024")
        
        if len(antelope_records) > 0:
            print("📋 Antelope Valley records:")
            for idx, record in antelope_records.iterrows():
                unit_id = record.get('Unit ID', 'Unknown')
                capacity = record.get('Nameplate Capacity (MW)', 'Unknown')
                fuel_type = record.get('Type', 'Unknown')
                print(f"   Record {idx}: Unit ID={unit_id}, Capacity={capacity} MW, Fuel={fuel_type}")
                
            # Check if we have distinct Unit IDs
            unit_ids = antelope_records['Unit ID'].unique()
            print(f"📊 Unique Unit IDs: {list(unit_ids)}")
            
            if len(unit_ids) >= 2:
                print("✅ CORRECT: Multiple units found in USA Excel data")
                print("💡 The system should detect both units from this data")
            else:
                print("❌ ISSUE: Only one unique Unit ID found")
        else:
            print("❌ No records found for Antelope Valley in USA Excel data")
            
    except Exception as e:
        print(f"❌ USA Excel analysis failed: {e}")
        import traceback
        traceback.print_exc()

def test_plant_data_units_id():
    """Test what units_id would be stored in plant_data"""
    print("\n🔍 TESTING PLANT DATA UNITS_ID FIELD")
    print("=" * 60)
    
    # Simulate what plant_data might look like for Antelope Valley
    simulated_plant_data = {
        "plant_name": "Antelope Valley Station",
        "capacity": 954,
        "units_id": [1, 2],  # This should contain both units
        "plant_type": "coal",
        "country": "United States"
    }
    
    print(f"📋 Simulated plant_data: {simulated_plant_data}")
    
    units_id = simulated_plant_data.get("units_id", [])
    if isinstance(units_id, list) and len(units_id) > 0:
        units_list = [str(unit) for unit in units_id]
        print(f"✅ Units list from plant_data: {units_list}")
        
        if len(units_list) == 2 and "1" in units_list and "2" in units_list:
            print("✅ CORRECT: Plant data contains both Unit 1 and Unit 2")
        else:
            print("❌ INCORRECT: Plant data missing units")
    else:
        print("❌ ISSUE: No units_id in plant_data or empty list")

if __name__ == "__main__":
    print("🚀 UNIT DETECTION TESTING FOR ANTELOPE VALLEY")
    print("=" * 80)
    
    # Test unit detection logic
    test_unit_detection_logic()
    
    # Test USA Excel unit information
    test_usa_excel_unit_info()
    
    # Test plant data units_id
    test_plant_data_units_id()
    
    print("\n✅ ALL UNIT DETECTION TESTS COMPLETED")
