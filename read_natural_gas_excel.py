#!/usr/bin/env python3
"""
Read and analyze the Natural Gas AGI - Fallback Cases.xlsx file
to understand formulas and assumptions for natural gas plants
"""

import pandas as pd
import sys
import os

def read_natural_gas_excel():
    """Read the Natural Gas Excel file and analyze its structure"""
    
    excel_file = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/Natural Gas AGI - Fallback Cases.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel file not found: {excel_file}")
        return
    
    print("🔥 READING NATURAL GAS AGI - FALLBACK CASES EXCEL FILE")
    print("=" * 80)
    
    try:
        # Read all sheets
        excel_data = pd.ExcelFile(excel_file)
        sheet_names = excel_data.sheet_names
        
        print(f"📋 Available sheets: {sheet_names}")
        print()
        
        # Analyze each sheet in detail
        for sheet_name in sheet_names:
            print(f"📄 SHEET: {sheet_name}")
            print("=" * 60)
            
            try:
                df = pd.read_excel(excel_file, sheet_name=sheet_name)
                
                print(f"📊 Shape: {df.shape} (rows x columns)")
                print(f"📋 Columns: {list(df.columns)}")
                print()
                
                # Show first few rows with better formatting
                print("🔍 FIRST 15 ROWS:")
                print("-" * 60)
                pd.set_option('display.max_columns', None)
                pd.set_option('display.width', None)
                pd.set_option('display.max_colwidth', 50)
                print(df.head(15).to_string(index=True))
                print()
                
                # Look for specific natural gas related content
                if sheet_name.lower() == 'assumptions':
                    print("🔧 ASSUMPTIONS SHEET ANALYSIS:")
                    print("-" * 40)
                    analyze_assumptions_sheet(df)
                
                elif 'generation' in sheet_name.lower() and 'plf' in sheet_name.lower():
                    print("⚡ GENERATION & PLF SHEET ANALYSIS:")
                    print("-" * 40)
                    analyze_generation_plf_sheet(df)
                
                elif 'heat rate' in sheet_name.lower() and 'efficiency' in sheet_name.lower():
                    print("🔥 HEAT RATE & EFFICIENCY SHEET ANALYSIS:")
                    print("-" * 40)
                    analyze_heat_rate_efficiency_sheet(df)
                
                # Look for formulas and calculations
                print("🧮 FORMULA AND CALCULATION ANALYSIS:")
                print("-" * 40)
                analyze_formulas_and_calculations(df, sheet_name)
                
                # Look for natural gas specific parameters
                print("🔥 NATURAL GAS SPECIFIC PARAMETERS:")
                print("-" * 40)
                analyze_natural_gas_parameters(df)
                
            except Exception as e:
                print(f"❌ Error reading sheet {sheet_name}: {e}")
                import traceback
                traceback.print_exc()
            
            print("=" * 80)
            print()
    
    except Exception as e:
        print(f"❌ Error reading Excel file: {e}")
        import traceback
        traceback.print_exc()

def analyze_assumptions_sheet(df):
    """Analyze the Assumptions sheet for natural gas parameters"""
    
    # Look for GCV values
    gcv_related = df[df.astype(str).apply(lambda x: x.str.contains('GCV|gcv|calorific|heating', case=False, na=False)).any(axis=1)]
    if not gcv_related.empty:
        print("🔥 GCV (Gross Calorific Value) Data:")
        print(gcv_related.to_string(index=False))
        print()
    
    # Look for emission factors
    emission_related = df[df.astype(str).apply(lambda x: x.str.contains('emission|co2|carbon', case=False, na=False)).any(axis=1)]
    if not emission_related.empty:
        print("💨 Emission Factor Data:")
        print(emission_related.to_string(index=False))
        print()
    
    # Look for efficiency values
    efficiency_related = df[df.astype(str).apply(lambda x: x.str.contains('efficiency|thermal', case=False, na=False)).any(axis=1)]
    if not efficiency_related.empty:
        print("⚡ Efficiency Data:")
        print(efficiency_related.to_string(index=False))
        print()
    
    # Look for auxiliary power
    aux_power_related = df[df.astype(str).apply(lambda x: x.str.contains('auxiliary|aux|parasitic', case=False, na=False)).any(axis=1)]
    if not aux_power_related.empty:
        print("🔌 Auxiliary Power Data:")
        print(aux_power_related.to_string(index=False))
        print()

def analyze_generation_plf_sheet(df):
    """Analyze the Generation & PLF sheet"""
    
    # Look for PLF formulas
    plf_related = df[df.astype(str).apply(lambda x: x.str.contains('PLF|plant load factor|load factor', case=False, na=False)).any(axis=1)]
    if not plf_related.empty:
        print("📊 PLF (Plant Load Factor) Data:")
        print(plf_related.to_string(index=False))
        print()
    
    # Look for generation calculations
    generation_related = df[df.astype(str).apply(lambda x: x.str.contains('generation|gross|net', case=False, na=False)).any(axis=1)]
    if not generation_related.empty:
        print("⚡ Generation Calculation Data:")
        print(generation_related.to_string(index=False))
        print()
    
    # Look for capacity factors
    capacity_related = df[df.astype(str).apply(lambda x: x.str.contains('capacity|MW|megawatt', case=False, na=False)).any(axis=1)]
    if not capacity_related.empty:
        print("🏭 Capacity Related Data:")
        print(capacity_related.to_string(index=False))
        print()

def analyze_heat_rate_efficiency_sheet(df):
    """Analyze the Heat Rate & Efficiency sheet"""
    
    # Look for heat rate formulas
    heat_rate_related = df[df.astype(str).apply(lambda x: x.str.contains('heat rate|kcal|kWh', case=False, na=False)).any(axis=1)]
    if not heat_rate_related.empty:
        print("🔥 Heat Rate Data:")
        print(heat_rate_related.to_string(index=False))
        print()
    
    # Look for efficiency calculations
    efficiency_related = df[df.astype(str).apply(lambda x: x.str.contains('efficiency|thermal|plant efficiency', case=False, na=False)).any(axis=1)]
    if not efficiency_related.empty:
        print("⚡ Efficiency Calculation Data:")
        print(efficiency_related.to_string(index=False))
        print()

def analyze_formulas_and_calculations(df, sheet_name):
    """Look for formulas and calculation methods"""
    
    # Check for any text that looks like formulas
    text_columns = df.select_dtypes(include=['object']).columns
    found_formulas = False
    
    for col in text_columns:
        if df[col].dtype == 'object':
            formula_like = df[col].dropna().astype(str)
            formula_like = formula_like[formula_like.str.contains('=|formula|calculation|method|case', case=False, na=False)]
            if not formula_like.empty:
                found_formulas = True
                print(f"🧮 Potential formulas in column '{col}':")
                for i, formula in enumerate(formula_like.head(10)):
                    print(f"  {i+1}. {formula}")
                print()
    
    if not found_formulas:
        print("No explicit formulas found in text columns")
        print()

def analyze_natural_gas_parameters(df):
    """Look for natural gas specific parameters"""
    
    # Natural gas related keywords
    gas_keywords = ['natural gas', 'gas', 'methane', 'lng', 'pipeline', 'ccgt', 'combined cycle', 'simple cycle', 'peaker']
    
    found_gas_content = False
    for keyword in gas_keywords:
        gas_related = df[df.astype(str).apply(lambda x: x.str.contains(keyword, case=False, na=False)).any(axis=1)]
        if not gas_related.empty:
            found_gas_content = True
            print(f"🔥 Content related to '{keyword}':")
            print(gas_related.to_string(index=False))
            print()
    
    if not found_gas_content:
        print("No explicit natural gas content found")
        print()
    
    # Look for technology types
    tech_keywords = ['ccgt', 'combined cycle', 'simple cycle', 'peaker', 'gas turbine', 'steam turbine']
    for keyword in tech_keywords:
        tech_related = df[df.astype(str).apply(lambda x: x.str.contains(keyword, case=False, na=False)).any(axis=1)]
        if not tech_related.empty:
            print(f"🏭 Technology related to '{keyword}':")
            print(tech_related.to_string(index=False))
            print()

if __name__ == "__main__":
    read_natural_gas_excel()
