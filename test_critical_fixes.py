#!/usr/bin/env python3
"""
Test Critical Fixes
===================

Tests the critical fixes for:
1. Fuel percentage logic (Lignite should be 1.0 from commencement, not 0.0)
2. Remaining useful life calculation (should not be null)
3. GCV consistency (Sub Bituminous should be 4900, not 6690)

This will verify that all the issues are resolved.
"""

import sys
import json
from datetime import datetime
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')

from excel_power_plant_tool import ExcelPowerPlantTool
from usa_excel_calculation_engine import USAExcelCalculationEngine

def test_fuel_percentage_fix():
    """Test that fuel percentages are correctly calculated"""
    print("\n🔥 TESTING FUEL PERCENTAGE FIX")
    print("=" * 60)
    
    try:
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx', 'test')
        
        # Test with Antelope Valley (has Lignite and Refined Coal)
        plant_name = "Antelope Valley"
        print(f"Testing plant: {plant_name}")
        
        unit_data = excel_tool.get_plant_data(plant_name)
        
        if unit_data:
            for unit in unit_data:
                unit_id = unit.get('unit_id', 'Unknown')
                fuel_types = unit.get('fuel_type', [])
                
                print(f"\n  Unit {unit_id}:")
                
                for fuel in fuel_types:
                    fuel_type = fuel.get('type', 'Unknown')
                    years_percentage = fuel.get('years_percentage', {})
                    
                    print(f"    {fuel_type}:")
                    
                    # Check key years
                    test_years = ['1986', '1990', '2000', '2020', '2021', '2024']
                    for year in test_years:
                        if year in years_percentage:
                            percentage = years_percentage[year]
                            print(f"      {year}: {percentage}")
                            
                            # Validation for Lignite
                            if fuel_type == 'Lignite':
                                if year in ['1986', '1990', '2000']:
                                    if percentage == 0.0:
                                        print(f"        ❌ ERROR: Lignite should be 1.0 in {year}, not 0.0")
                                    elif percentage == 1.0:
                                        print(f"        ✅ CORRECT: Lignite is 1.0 in {year}")
        
        print("✅ Fuel percentage test completed")
        return True
        
    except Exception as e:
        print(f"❌ Fuel percentage test failed: {e}")
        return False

def test_remaining_useful_life_fix():
    """Test that remaining useful life is calculated correctly"""
    print("\n⏰ TESTING REMAINING USEFUL LIFE FIX")
    print("=" * 60)
    
    try:
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx', 'test')
        
        # Test with multiple plants
        test_plants = ["Antelope Valley", "Dry Fork", "Laramie River"]
        
        for plant_name in test_plants:
            print(f"\nTesting plant: {plant_name}")
            
            unit_data = excel_tool.get_plant_data(plant_name)
            
            if unit_data:
                for unit in unit_data:
                    unit_id = unit.get('unit_id', 'Unknown')
                    commencement_date = unit.get('commencement_date', 'Unknown')
                    unit_lifetime = unit.get('unit_lifetime', 'Unknown')
                    remaining_life = unit.get('remaining_useful_life', 'Unknown')
                    
                    print(f"  Unit {unit_id}:")
                    print(f"    Commencement: {commencement_date}")
                    print(f"    Lifetime: {unit_lifetime} years")
                    print(f"    Remaining Life: {remaining_life}")
                    
                    if remaining_life is None or remaining_life == 'Unknown':
                        print(f"    ❌ ERROR: Remaining useful life is null/unknown")
                    else:
                        print(f"    ✅ CORRECT: Remaining useful life calculated")
        
        print("✅ Remaining useful life test completed")
        return True
        
    except Exception as e:
        print(f"❌ Remaining useful life test failed: {e}")
        return False

def test_gcv_consistency_fix():
    """Test that GCV values are consistent and correct"""
    print("\n🧮 TESTING GCV CONSISTENCY FIX")
    print("=" * 60)
    
    try:
        # Test USA Excel calculation engine GCV method
        usa_engine = USAExcelCalculationEngine(
            '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx',
            '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/current state calculations.xlsx'
        )
        
        # Test different coal types
        coal_types = [
            ('bituminous', 6690),
            ('sub-bituminous', 4900),
            ('lignite', 3350),
            ('anthracite', 7000)
        ]
        
        print("Testing GCV calculation method:")
        for coal_type, expected_gcv in coal_types:
            actual_gcv = usa_engine.get_gcv_for_coal_type(coal_type)
            print(f"  {coal_type}: Expected {expected_gcv}, Got {actual_gcv}")
            
            if actual_gcv == expected_gcv:
                print(f"    ✅ CORRECT")
            else:
                print(f"    ❌ ERROR: Wrong GCV value")
        
        # Test with actual plant data
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx', 'test')
        
        # Find a plant with Sub Bituminous coal
        test_plants = ["Dry Fork", "Laramie River"]
        
        for plant_name in test_plants:
            print(f"\nTesting plant: {plant_name}")
            
            unit_data = excel_tool.get_plant_data(plant_name)
            
            if unit_data:
                for unit in unit_data:
                    unit_id = unit.get('unit_id', 'Unknown')
                    fuel_types = unit.get('fuel_type', [])
                    unit_gcv = unit.get('gcv_coal', 'Unknown')
                    
                    print(f"  Unit {unit_id}:")
                    print(f"    Unit GCV: {unit_gcv}")
                    
                    for fuel in fuel_types:
                        fuel_type = fuel.get('type', 'Unknown')
                        fuel_gcv = fuel.get('gcv_coal', 'Unknown')
                        
                        print(f"    Fuel {fuel_type}: GCV = {fuel_gcv}")
                        
                        # Check Sub Bituminous specifically
                        if 'Sub Bituminous' in fuel_type:
                            if fuel_gcv == 4900:
                                print(f"      ✅ CORRECT: Sub Bituminous GCV is 4900")
                            elif fuel_gcv == 6690:
                                print(f"      ❌ ERROR: Sub Bituminous GCV is 6690 (should be 4900)")
                            else:
                                print(f"      ⚠️ UNEXPECTED: Sub Bituminous GCV is {fuel_gcv}")
        
        print("✅ GCV consistency test completed")
        return True
        
    except Exception as e:
        print(f"❌ GCV consistency test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all critical fix tests"""
    print("🚀 TESTING CRITICAL FIXES")
    print("=" * 80)
    print("Testing fixes for:")
    print("1. Fuel percentage logic (Lignite 1986-2019 should be 1.0, not 0.0)")
    print("2. Remaining useful life calculation (should not be null)")
    print("3. GCV consistency (Sub Bituminous should be 4900, not 6690)")
    
    tests = [
        ("Fuel Percentage Fix", test_fuel_percentage_fix),
        ("Remaining Useful Life Fix", test_remaining_useful_life_fix),
        ("GCV Consistency Fix", test_gcv_consistency_fix)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*80}")
        success = test_func()
        results[test_name] = success
    
    # Summary
    print(f"\n{'='*80}")
    print("🎯 CRITICAL FIXES TEST SUMMARY")
    print(f"{'='*80}")
    
    for test_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status}: {test_name}")
    
    total_passed = sum(results.values())
    total_tests = len(results)
    
    print(f"\nOverall: {total_passed}/{total_tests} critical fixes tested successfully")
    
    if total_passed == total_tests:
        print("🎉 ALL CRITICAL FIXES WORKING CORRECTLY!")
    else:
        print("⚠️ Some critical issues still need attention")
        print("\nRemaining issues to fix:")
        for test_name, success in results.items():
            if not success:
                print(f"  - {test_name}")

if __name__ == "__main__":
    main()
