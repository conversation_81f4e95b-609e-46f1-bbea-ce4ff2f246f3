#!/usr/bin/env python3

"""
Comprehensive calculation test for all specified power plants.
Tests every step: variables → calculations → results
"""

import sys
import os
import json

# Add paths for imports
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/backend/src/agent')

from excel_power_plant_tool import ExcelPowerPlantTool
from heat_rate_efficiency_calculator import HeatRateEfficiencyCalculator
from usa_excel_calculation_engine import create_usa_excel_calculation_engine

def test_comprehensive_calculations():
    """Test all calculations step-by-step for specified plants"""
    
    print("🧮 COMPREHENSIVE CALCULATION TEST")
    print("=" * 80)
    print("Testing: Variables → Calculations → Results")
    print("=" * 80)
    
    # Test plants list
    test_plants = [
        "Apache Generating Station",
        "Archer Daniels Midland Cedar Rapids",
        "Archer Daniels Midland Clinton", 
        "Archer Daniels Midland Columbus",
        "Archer Daniels Midland Decatur",
        "Archer Daniels Midland Des Moines",
        "Archer Daniels Midland Lincoln",
        "Archer Daniels Midland Mankato",
        "Argus Cogen Plant",
        "Belle River",
        "Big Bend",
        "Big Cajun 2",
        "Big Stone",
        "Biron Mill"
    ]
    
    # Initialize tools
    excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx', "comprehensive_test")
    heat_calc = HeatRateEfficiencyCalculator("comprehensive_test")
    usa_calc_engine = create_usa_excel_calculation_engine()
    
    all_results = {}
    
    for i, plant_name in enumerate(test_plants, 1):
        print(f"\n🏭 TEST {i:2d}: {plant_name}")
        print("=" * 70)
        
        try:
            # Get plant data
            results = excel_tool.get_plant_data(plant_name)
            
            if not results:
                print(f"❌ No results found for {plant_name}")
                all_results[plant_name] = {"status": "not_found"}
                continue
            
            print(f"✅ Found {len(results)} units for {plant_name}")
            
            plant_calculations = []
            
            # Test each unit
            for j, unit in enumerate(results, 1):
                print(f"\n📋 UNIT {j} DETAILED CALCULATIONS:")
                print("-" * 50)
                
                # Extract basic data
                unit_id = unit.get('unit_id', 'N/A')
                capacity = unit.get('capacity', 0)
                plant_name_result = unit.get('plant_name', 'N/A')
                
                print(f"🔧 BASIC DATA:")
                print(f"   Unit ID: {unit_id}")
                print(f"   Capacity: {capacity} MW")
                print(f"   Plant Name: {plant_name_result}")
                
                # Get fuel type and coal type
                fuel_types = unit.get('fuel_type', [])
                coal_type = fuel_types[0].get('type', 'Unknown') if fuel_types else 'Unknown'
                
                print(f"   Coal Type: {coal_type}")
                
                # STEP 1: Get GCV based on coal type
                print(f"\n🔬 STEP 1: GCV CALCULATION")
                gcv_value = heat_calc.get_gcv_for_coal_type(coal_type)
                print(f"   Coal Type: {coal_type}")
                print(f"   GCV: {gcv_value} kcal/kg")
                
                # STEP 2: Get auxiliary power based on capacity and technology
                print(f"\n⚡ STEP 2: AUXILIARY POWER CALCULATION")
                technology = 'subcritical'  # Default for coal plants
                aux_power_decimal = usa_calc_engine.get_auxiliary_power_percentage(capacity, technology)
                print(f"   Capacity: {capacity} MW")
                print(f"   Technology: {technology}")
                print(f"   Auxiliary Power: {aux_power_decimal:.3f} ({aux_power_decimal*100:.1f}%)")
                
                # STEP 3: Get generation and fuel consumption data
                print(f"\n📊 STEP 3: GENERATION & FUEL DATA")
                
                # Check if we have coal data for calculations
                has_coal_data = False
                net_generation_mwh = 0
                fuel_consumed_tons = 0

                # Get coal data from the unit's existing data (already calculated by Excel tool)
                # The Excel tool already processes and includes this data in the unit response

                # Try to extract from existing unit data first
                if hasattr(unit, 'get') and unit.get('heat_rate_kcal_per_kwh'):
                    # If heat rate exists, the tool already found coal data
                    # Try to get raw coal data using the Excel tool's method
                    try:
                        raw_coal_data = excel_tool._get_raw_coal_data_for_plant(plant_name_result)
                        if not raw_coal_data.empty:
                            # Filter to get data with generation > 0
                            coal_data_with_gen = raw_coal_data[raw_coal_data['Net Generation (Megawatthours)'] > 0]
                            if not coal_data_with_gen.empty:
                                # Get most recent year data
                                latest_data = coal_data_with_gen.loc[coal_data_with_gen['Year'].idxmax()]
                                net_generation_mwh = latest_data['Net Generation (Megawatthours)']
                                fuel_consumed_tons = latest_data.get('Total Fuel Consumed Tons', 0)
                                has_coal_data = True
                    except Exception as e:
                        print(f"   ⚠️ Could not get raw coal data: {e}")

                # Fallback: use dummy data for demonstration if no real data
                if not has_coal_data:
                    # Use typical values for demonstration
                    net_generation_mwh = capacity * 8760 * 0.5  # 50% capacity factor
                    fuel_consumed_tons = net_generation_mwh * 0.5  # Rough estimate
                    print(f"   ⚠️ Using estimated data for demonstration")
                
                print(f"   Net Generation: {net_generation_mwh:,.0f} MWh")
                print(f"   Fuel Consumed: {fuel_consumed_tons:,.1f} tons")
                print(f"   Has Coal Data: {'✅ Yes' if has_coal_data else '❌ No'}")
                
                # STEP 4: Calculate gross generation
                print(f"\n🔄 STEP 4: GROSS GENERATION CALCULATION")
                if has_coal_data and net_generation_mwh > 0:
                    gross_generation_mwh = net_generation_mwh / (1 - aux_power_decimal)
                    print(f"   Formula: Gross = Net / (1 - Aux)")
                    print(f"   Gross = {net_generation_mwh:,.0f} / (1 - {aux_power_decimal:.3f})")
                    print(f"   Gross = {net_generation_mwh:,.0f} / {1 - aux_power_decimal:.3f}")
                    print(f"   Gross Generation: {gross_generation_mwh:,.0f} MWh")
                else:
                    gross_generation_mwh = 0
                    print(f"   ❌ Cannot calculate - insufficient data")
                
                # STEP 5: Calculate heat rate
                print(f"\n🔥 STEP 5: HEAT RATE CALCULATION")
                if has_coal_data and gross_generation_mwh > 0 and fuel_consumed_tons > 0:
                    # Convert to proper units
                    fuel_consumed_kg = fuel_consumed_tons * 1000
                    gross_generation_kwh = gross_generation_mwh * 1000
                    
                    heat_rate = (fuel_consumed_kg * gcv_value) / gross_generation_kwh
                    
                    print(f"   Formula: Heat Rate = (Fuel_kg × GCV) / Gross_kWh")
                    print(f"   Heat Rate = ({fuel_consumed_tons:,.1f} × 1000 × {gcv_value}) / ({gross_generation_mwh:,.0f} × 1000)")
                    print(f"   Heat Rate = ({fuel_consumed_kg:,.0f} × {gcv_value}) / {gross_generation_kwh:,.0f}")
                    print(f"   Heat Rate = {fuel_consumed_kg * gcv_value:,.0f} / {gross_generation_kwh:,.0f}")
                    print(f"   Heat Rate: {heat_rate:.2f} kcal/kWh")
                else:
                    heat_rate = None
                    print(f"   ❌ Cannot calculate - insufficient data")
                
                # STEP 6: Calculate efficiency
                print(f"\n📈 STEP 6: EFFICIENCY CALCULATION")
                if has_coal_data and gross_generation_mwh > 0 and fuel_consumed_tons > 0:
                    # Formula: efficiency = (Gross_MWh * 860,420) / (GCV * Fuel_tons * 1000)
                    numerator = gross_generation_mwh * 860420
                    denominator = gcv_value * fuel_consumed_tons * 1000
                    efficiency = numerator / denominator
                    
                    print(f"   Formula: Efficiency = (Gross_MWh × 860,420) / (GCV × Fuel_tons × 1000)")
                    print(f"   Efficiency = ({gross_generation_mwh:,.0f} × 860,420) / ({gcv_value} × {fuel_consumed_tons:,.1f} × 1000)")
                    print(f"   Efficiency = {numerator:,.0f} / {denominator:,.0f}")
                    print(f"   Efficiency: {efficiency:.4f} ({efficiency*100:.2f}%)")
                else:
                    efficiency = None
                    print(f"   ❌ Cannot calculate - insufficient data")
                
                # STEP 7: Verify with Excel tool results
                print(f"\n✅ STEP 7: VERIFICATION WITH EXCEL TOOL")
                excel_heat_rate = unit.get('heat_rate_kcal_per_kwh', None)
                excel_efficiency = unit.get('coal_unit_efficiency', None)
                excel_aux_power = unit.get('auxiliary_power_consumed', [])
                
                print(f"   Excel Heat Rate: {excel_heat_rate}")
                print(f"   Excel Efficiency: {excel_efficiency}")
                print(f"   Excel Aux Power: {excel_aux_power[0].get('value', 'N/A') if excel_aux_power else 'N/A'}")
                
                # Compare results
                print(f"\n🎯 COMPARISON:")
                if heat_rate and excel_heat_rate:
                    hr_diff = abs(heat_rate - excel_heat_rate)
                    hr_match = hr_diff < 10  # 10 kcal/kWh tolerance
                    print(f"   Heat Rate: Calculated={heat_rate:.2f}, Excel={excel_heat_rate:.2f}, Diff={hr_diff:.2f} {'✅' if hr_match else '❌'}")
                
                if efficiency and excel_efficiency:
                    eff_diff = abs(efficiency - excel_efficiency)
                    eff_match = eff_diff < 0.01  # 1% tolerance
                    print(f"   Efficiency: Calculated={efficiency:.4f}, Excel={excel_efficiency:.4f}, Diff={eff_diff:.4f} {'✅' if eff_match else '❌'}")
                
                if excel_aux_power:
                    aux_excel = excel_aux_power[0].get('value', 0)
                    aux_diff = abs(aux_power_decimal - aux_excel)
                    aux_match = aux_diff < 0.01  # 1% tolerance
                    print(f"   Aux Power: Calculated={aux_power_decimal:.3f}, Excel={aux_excel:.3f}, Diff={aux_diff:.3f} {'✅' if aux_match else '❌'}")
                
                # Store unit calculation results
                unit_calc = {
                    "unit_id": unit_id,
                    "capacity_mw": capacity,
                    "coal_type": coal_type,
                    "gcv_kcal_per_kg": gcv_value,
                    "auxiliary_power_decimal": aux_power_decimal,
                    "net_generation_mwh": net_generation_mwh,
                    "fuel_consumed_tons": fuel_consumed_tons,
                    "gross_generation_mwh": gross_generation_mwh,
                    "calculated_heat_rate": heat_rate,
                    "calculated_efficiency": efficiency,
                    "excel_heat_rate": excel_heat_rate,
                    "excel_efficiency": excel_efficiency,
                    "has_coal_data": has_coal_data
                }
                
                plant_calculations.append(unit_calc)
            
            all_results[plant_name] = {
                "status": "found",
                "total_units": len(results),
                "calculations": plant_calculations
            }
            
        except Exception as e:
            print(f"❌ ERROR testing {plant_name}: {e}")
            import traceback
            traceback.print_exc()
            all_results[plant_name] = {"status": "error", "error": str(e)}
    
    # Save detailed results
    with open('comprehensive_calculation_results.json', 'w') as f:
        json.dump(all_results, f, indent=2, default=str)
    
    print(f"\n🎯 COMPREHENSIVE TEST SUMMARY")
    print("=" * 80)
    
    total_plants = len(test_plants)
    found_plants = sum(1 for result in all_results.values() if result.get("status") == "found")
    total_units = sum(result.get("total_units", 0) for result in all_results.values() if result.get("status") == "found")
    
    print(f"Plants Tested: {total_plants}")
    print(f"Plants Found: {found_plants}")
    print(f"Total Units: {total_units}")
    print(f"Success Rate: {found_plants/total_plants*100:.1f}%")
    
    print(f"\n📄 Detailed results saved to: comprehensive_calculation_results.json")
    
    return all_results

if __name__ == "__main__":
    results = test_comprehensive_calculations()
