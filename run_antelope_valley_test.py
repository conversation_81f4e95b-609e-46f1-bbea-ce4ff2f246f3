#!/usr/bin/env python3
"""
Run the complete system with Antelope Valley Station to test the new extraction patterns and calculations
"""

import sys
import os
import asyncio
import json
from datetime import datetime

# Add the backend source to the path
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/backend/src')

async def run_antelope_valley_test():
    """Run the complete system with Antelope Valley Station"""
    
    print("🚀 RUNNING COMPLETE SYSTEM TEST WITH ANTELOPE VALLEY STATION")
    print("=" * 70)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Import the main graph
        from agent.graph import graph
        
        # Create the initial state for Antelope Valley Station
        initial_state = {
            "plant_name": "Antelope Valley Station",
            "country": "United States",
            "plant_technology": "Coal",
            "messages": [],
            "stage_results": [],
            "unit_level_data": {},
            "calculation_results": {},
            "final_results": {},
            "current_stage": "start",
            "error_log": []
        }
        
        print("📊 INITIAL STATE:")
        print(f"  • Plant: {initial_state['plant_name']}")
        print(f"  • Country: {initial_state['country']}")
        print(f"  • Technology: {initial_state['plant_technology']}")
        print()
        
        print("🔧 STARTING UNIT EXTRACTION PIPELINE...")
        print("-" * 50)
        
        # Run the graph
        final_state = await graph.ainvoke(initial_state)
        
        print("\n✅ PIPELINE COMPLETED!")
        print("=" * 50)
        
        # Analyze the results
        print("\n📊 FINAL STATE ANALYSIS:")
        print("-" * 30)
        
        # Check unit level data
        if 'unit_level_data' in final_state:
            unit_data = final_state['unit_level_data']
            print(f"📋 Units found: {len(unit_data)}")
            
            for unit_id, unit_info in unit_data.items():
                print(f"\n🔧 Unit {unit_id}:")
                print(f"  • Capacity: {unit_info.get('capacity', 'Unknown')} MW")
                print(f"  • Technology: {unit_info.get('technology', 'Unknown')}")
                print(f"  • Coal Type: {unit_info.get('coal_type', 'Unknown')}")
                
                # Check calculated fields
                calculated_fields = [
                    'plf', 'auxiliary_power_consumed', 'gross_power_generation',
                    'heat_rate', 'efficiency', 'emission_factor'
                ]
                
                print("  📊 Calculated Fields:")
                for field in calculated_fields:
                    if field in unit_info:
                        value = unit_info[field]
                        if isinstance(value, list):
                            print(f"    • {field}: {len(value)} entries")
                            if value:  # Show first entry as example
                                print(f"      Example: {value[0]}")
                        else:
                            print(f"    • {field}: {value}")
                    else:
                        print(f"    • {field}: ❌ Missing")
        
        # Check for JSON files created
        print("\n📁 CHECKING CREATED FILES:")
        print("-" * 30)
        
        # Look for JSON files
        import glob
        json_files = glob.glob("unit#coal#*#plant#*.json")
        
        if json_files:
            print(f"✅ Found {len(json_files)} JSON files:")
            for json_file in json_files:
                print(f"  • {json_file}")
                
                # Read and analyze the first file
                if json_file == json_files[0]:
                    try:
                        with open(json_file, 'r') as f:
                            json_data = json.load(f)
                        
                        print(f"\n📋 Analysis of {json_file}:")
                        
                        # Check key fields
                        key_fields = [
                            'plf', 'auxiliary_power_consumed', 'gross_power_generation',
                            'heat_rate', 'efficiency', 'emission_factor'
                        ]
                        
                        for field in key_fields:
                            if field in json_data:
                                value = json_data[field]
                                if isinstance(value, list):
                                    if value:
                                        print(f"  ✅ {field}: {len(value)} entries")
                                        # Show recent entries
                                        recent = [item for item in value if int(item.get('year', 0)) >= 2022]
                                        if recent:
                                            print(f"    Recent: {recent[:2]}")
                                    else:
                                        print(f"  ❌ {field}: Empty array")
                                else:
                                    if value and value != 0:
                                        print(f"  ✅ {field}: {value}")
                                    else:
                                        print(f"  ❌ {field}: {value}")
                            else:
                                print(f"  ❌ {field}: Missing")
                    
                    except Exception as e:
                        print(f"  ❌ Error reading {json_file}: {e}")
        else:
            print("❌ No JSON files found")
        
        # Check error log
        if 'error_log' in final_state and final_state['error_log']:
            print("\n⚠️ ERRORS ENCOUNTERED:")
            print("-" * 25)
            for error in final_state['error_log']:
                print(f"  • {error}")
        
        # Check stage results
        if 'stage_results' in final_state:
            print(f"\n📊 STAGE RESULTS: {len(final_state['stage_results'])} stages completed")
            
            # Look for the new extraction fields
            new_fields_found = {
                'unit_generation_mwh': False,
                'plant_generation_mwh': False,
                'fuel_consumed_tons': False,
                'annual_emission_mt': False
            }
            
            for stage_result in final_state['stage_results']:
                if isinstance(stage_result, dict):
                    for field in new_fields_found.keys():
                        if field in stage_result:
                            new_fields_found[field] = True
            
            print("\n🔍 NEW EXTRACTION FIELDS CHECK:")
            print("-" * 35)
            for field, found in new_fields_found.items():
                status = "✅" if found else "❌"
                print(f"  {status} {field}: {'Found' if found else 'Not found'}")
        
        print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Run the test
    asyncio.run(run_antelope_valley_test())
    
    print("\n" + "=" * 70)
    print("🎯 TEST SUMMARY")
    print("=" * 70)
    print()
    print("This test runs the complete unit extraction pipeline with:")
    print("1. ✅ Updated extraction patterns for generation/fuel/emissions data")
    print("2. ✅ New Excel calculation engine with fallback calculations")
    print("3. ✅ Improved data flow from extraction to calculations")
    print("4. ✅ JSON file generation and validation")
    print()
    print("🔍 Key things to verify:")
    print("• Are both Unit 1 and Unit 2 processed?")
    print("• Are the PLF, auxiliary_power_consumed, gross_power_generation arrays populated?")
    print("• Are heat_rate and efficiency values calculated correctly?")
    print("• Do the values match the Excel calculation formulas?")
