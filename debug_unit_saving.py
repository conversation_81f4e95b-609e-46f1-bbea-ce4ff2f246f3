#!/usr/bin/env python3
"""
Debug the unit saving process to see why only Unit 2 is saved
"""

import sys
import os
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')

def test_unit_saving_logic():
    """Test the unit saving logic step by step"""
    print("🔍 DEBUGGING UNIT SAVING LOGIC")
    print("=" * 60)
    
    # Simulate the exact data that would be generated for both units
    plant_context = {
        'plant_name': 'Antelope Valley Station',
        'country': 'United States',
        'plant_type': 'coal',
        'plant_id': '1',
        'plant_uid': 'test-plant-uuid-123'
    }
    
    # Test Unit 1 SK generation
    unit_1_sk = f"unit#{plant_context['plant_type']}#{1}#plant#{plant_context['plant_id']}"
    unit_1_filename = f"{unit_1_sk}.json"
    
    print(f"Unit 1:")
    print(f"  SK: {unit_1_sk}")
    print(f"  Filename: {unit_1_filename}")
    
    # Test Unit 2 SK generation
    unit_2_sk = f"unit#{plant_context['plant_type']}#{2}#plant#{plant_context['plant_id']}"
    unit_2_filename = f"{unit_2_sk}.json"
    
    print(f"Unit 2:")
    print(f"  SK: {unit_2_sk}")
    print(f"  Filename: {unit_2_filename}")
    
    # Check if filenames are different
    if unit_1_filename != unit_2_filename:
        print("✅ CORRECT: Different filenames generated")
        print(f"   Unit 1: {unit_1_filename}")
        print(f"   Unit 2: {unit_2_filename}")
    else:
        print("❌ PROBLEM: Same filename for both units!")
        return False
    
    # Test the actual combine_unit_data function
    try:
        from backend.src.agent.unit_extraction_stages import combine_unit_data
        
        # Simulate stage results
        stage_results = [
            {"unit_number": "1", "capacity": 477, "technology": "subcritical"},
            {"heat_rate": 2200, "plf": []},
            {"fuel_type": ["lignite"], "emission_factor": []},
            {"capex_required_renovation": "Not available"}
        ]
        
        print(f"\n🔧 Testing combine_unit_data function:")
        
        # Test Unit 1
        unit_1_data = combine_unit_data(stage_results, "1", plant_context)
        unit_1_actual_sk = unit_1_data.get('sk', 'MISSING')
        print(f"Unit 1 actual SK: {unit_1_actual_sk}")
        
        # Test Unit 2
        stage_results[0]["unit_number"] = "2"  # Update unit number
        unit_2_data = combine_unit_data(stage_results, "2", plant_context)
        unit_2_actual_sk = unit_2_data.get('sk', 'MISSING')
        print(f"Unit 2 actual SK: {unit_2_actual_sk}")
        
        if unit_1_actual_sk != unit_2_actual_sk:
            print("✅ CORRECT: Different SKs generated by combine_unit_data")
        else:
            print("❌ PROBLEM: Same SK generated for both units!")
            return False
            
    except Exception as e:
        print(f"❌ Error testing combine_unit_data: {e}")
        return False
    
    return True

def test_s3_storage_simulation():
    """Test S3 storage simulation"""
    print("\n📁 TESTING S3 STORAGE SIMULATION")
    print("=" * 60)
    
    # Simulate what happens when both units are saved
    unit_1_data = {
        "sk": "unit#coal#1#plant#1",
        "unit_number": "1",
        "capacity": 477,
        "plant_id": "1",
        "plant_uid": "test-plant-uuid-123"
    }
    
    unit_2_data = {
        "sk": "unit#coal#2#plant#1", 
        "unit_number": "2",
        "capacity": 477,
        "plant_id": "1",
        "plant_uid": "test-plant-uuid-123"
    }
    
    # Test filename generation
    unit_1_filename = f"{unit_1_data['sk']}.json"
    unit_2_filename = f"{unit_2_data['sk']}.json"
    
    print(f"Unit 1 would be saved as: {unit_1_filename}")
    print(f"Unit 2 would be saved as: {unit_2_filename}")
    
    # Test S3 path generation
    org_id = "test-org-123"
    plant_id = "test-plant-uuid-123"
    country_folder = "united-states"
    
    unit_1_s3_path = f"{country_folder}/{org_id}/{plant_id}/{unit_1_filename}"
    unit_2_s3_path = f"{country_folder}/{org_id}/{plant_id}/{unit_2_filename}"
    
    print(f"Unit 1 S3 path: {unit_1_s3_path}")
    print(f"Unit 2 S3 path: {unit_2_s3_path}")
    
    if unit_1_s3_path != unit_2_s3_path:
        print("✅ CORRECT: Different S3 paths for both units")
        return True
    else:
        print("❌ PROBLEM: Same S3 path for both units!")
        return False

def check_potential_issues():
    """Check for potential issues that could cause unit saving problems"""
    print("\n🔍 CHECKING POTENTIAL ISSUES")
    print("=" * 60)
    
    issues_found = []
    
    # Issue 1: Variable reuse in loop
    print("1. Checking for variable reuse issues...")
    # This would be in the actual loop - we can't test it here
    print("   ⚠️ Need to check if variables like 'unit_data' are being reused")
    
    # Issue 2: State overwriting
    print("2. Checking for state overwriting issues...")
    print("   ⚠️ Need to check if state is being modified during loop")
    
    # Issue 3: Async/timing issues
    print("3. Checking for async/timing issues...")
    print("   ⚠️ Need to check if S3 uploads are happening asynchronously")
    
    # Issue 4: Exception handling
    print("4. Checking exception handling...")
    print("   ⚠️ Need to check if exceptions in Unit 1 prevent Unit 2 processing")
    
    # Issue 5: Database lookup issues
    print("5. Checking database lookup issues...")
    print("   ⚠️ Need to check if database lookups are failing for some units")
    
    print(f"\n📋 Potential issues to investigate: {len(issues_found) + 5}")
    
    return True

if __name__ == "__main__":
    print("🚀 UNIT SAVING DEBUG ANALYSIS")
    print("=" * 80)
    
    # Test 1: Unit saving logic
    logic_ok = test_unit_saving_logic()
    
    # Test 2: S3 storage simulation
    storage_ok = test_s3_storage_simulation()
    
    # Test 3: Check potential issues
    check_potential_issues()
    
    print("\n" + "=" * 80)
    print("📋 DEBUG SUMMARY:")
    print(f"   Unit Saving Logic: {'✅ PASS' if logic_ok else '❌ FAIL'}")
    print(f"   S3 Storage Simulation: {'✅ PASS' if storage_ok else '❌ FAIL'}")
    
    if logic_ok and storage_ok:
        print("\n💡 CONCLUSION:")
        print("✅ The unit saving logic appears correct")
        print("✅ Different filenames and S3 paths are generated")
        print("🔍 The issue is likely in:")
        print("   1. Exception handling during Unit 1 processing")
        print("   2. Database lookup failures")
        print("   3. State modification during the loop")
        print("   4. Async timing issues")
        print("\n🎯 RECOMMENDATION: Add detailed logging to the actual unit processing loop")
    else:
        print("\n⚠️ ISSUES FOUND IN BASIC LOGIC - NEED TO FIX FIRST")
