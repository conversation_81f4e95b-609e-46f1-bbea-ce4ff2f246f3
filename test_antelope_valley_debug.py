#!/usr/bin/env python3
"""
Test Antelope Valley Station with debug logging to see unit saving issue
"""

import sys
import os
import asyncio
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/backend/src')

async def test_antelope_valley_with_debug():
    """Test Antelope Valley Station extraction with debug logging"""
    print("🚀 TESTING ANTELOPE VALLEY STATION WITH DEBUG LOGGING")
    print("=" * 80)
    
    try:
        # Import the entity extraction controller
        from agent.entity_extraction_controller import EntityExtractionController
        
        # Create controller with organization info
        org_info = {
            "org_id": "test-org-antelope-debug",
            "org_name": "Test Organization", 
            "country": "United States"
        }
        
        controller = EntityExtractionController(org_info)
        
        # Test with Antelope Valley Station
        plant_data = {
            "plant_name": "Antelope Valley Station",
            "plant_id": "test-plant-antelope-debug"
        }
        
        print(f"🏭 Testing plant: {plant_data['plant_name']}")
        print(f"🆔 Plant ID: {plant_data['plant_id']}")
        print(f"🏢 Organization: {org_info['org_name']}")
        
        # Process the plant
        print(f"\n🔄 Starting extraction process...")
        result = await controller.process_single_plant(plant_data, "debug-job-123")
        
        print(f"\n📋 EXTRACTION RESULT:")
        print(f"   Success: {result.get('success', False)}")
        print(f"   S3 URLs: {len(result.get('s3_urls', []))}")
        
        if result.get('success'):
            s3_urls = result.get('s3_urls', [])
            print(f"\n📁 S3 FILES CREATED:")
            for i, url in enumerate(s3_urls, 1):
                print(f"   {i}. {url}")
                
            # Check for unit files specifically
            unit_files = [url for url in s3_urls if 'unit#coal#' in url]
            print(f"\n⚡ UNIT FILES FOUND: {len(unit_files)}")
            for unit_file in unit_files:
                print(f"   📄 {unit_file}")
                
            if len(unit_files) == 2:
                print("✅ SUCCESS: Both Unit 1 and Unit 2 JSON files created!")
            elif len(unit_files) == 1:
                print("❌ PROBLEM: Only 1 unit JSON file created (should be 2)")
            else:
                print(f"❌ PROBLEM: {len(unit_files)} unit JSON files created (should be 2)")
        else:
            print(f"❌ Extraction failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Run the async test
    asyncio.run(test_antelope_valley_with_debug())
