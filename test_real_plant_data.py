#!/usr/bin/env python3
"""
Test real plant data extraction to see what we're actually getting
"""

import json
import os

def test_antelope_valley_data():
    """Test the actual Antelope Valley Station data"""
    
    print("🔍 TESTING ANTELOPE VALLEY STATION DATA")
    print("=" * 60)
    
    # Check if the JSON file exists
    json_file = "/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/unit#coal#2#plant#1.json"
    
    if os.path.exists(json_file):
        print(f"📁 Found JSON file: {json_file}")
        
        with open(json_file, 'r') as f:
            unit_data = json.load(f)
        
        print("\n📊 CURRENT UNIT DATA:")
        print("-" * 30)
        
        # Key fields for calculations
        key_fields = [
            'capacity', 'technology', 'coal_unit_efficiency', 'heat_rate', 
            'fuel_type', 'gcv_coal', 'plf', 'auxiliary_power_consumed', 
            'gross_power_generation', 'emission_factor'
        ]
        
        for field in key_fields:
            value = unit_data.get(field, "NOT FOUND")
            status = "✅" if value and value != [] else "❌"
            print(f"  {status} {field}: {value}")
        
        print("\n🔍 SEARCHING FOR GENERATION DATA:")
        print("-" * 35)
        
        # Look for any generation-related fields
        generation_fields = []
        for key, value in unit_data.items():
            if any(term in key.lower() for term in ['generation', 'output', 'produced', 'mwh', 'gwh']):
                generation_fields.append((key, value))
        
        if generation_fields:
            print("  Found generation-related fields:")
            for key, value in generation_fields:
                print(f"    • {key}: {value}")
        else:
            print("  ❌ No generation-related fields found")
        
        print("\n🔍 SEARCHING FOR FUEL CONSUMPTION DATA:")
        print("-" * 40)
        
        # Look for fuel consumption fields
        fuel_fields = []
        for key, value in unit_data.items():
            if any(term in key.lower() for term in ['fuel', 'coal', 'consumption', 'tons', 'consumed']):
                fuel_fields.append((key, value))
        
        if fuel_fields:
            print("  Found fuel-related fields:")
            for key, value in fuel_fields:
                print(f"    • {key}: {value}")
        else:
            print("  ❌ No fuel consumption fields found")
        
        print("\n🔍 SEARCHING FOR EMISSIONS DATA:")
        print("-" * 32)
        
        # Look for emissions fields
        emission_fields = []
        for key, value in unit_data.items():
            if any(term in key.lower() for term in ['emission', 'co2', 'carbon', 'factor']):
                emission_fields.append((key, value))
        
        if emission_fields:
            print("  Found emission-related fields:")
            for key, value in emission_fields:
                print(f"    • {key}: {value}")
        else:
            print("  ❌ No emission fields found")
        
        print("\n🔍 ALL AVAILABLE FIELDS:")
        print("-" * 25)
        print(f"  Total fields: {len(unit_data)}")
        for key in sorted(unit_data.keys()):
            print(f"    • {key}")
            
    else:
        print(f"❌ JSON file not found: {json_file}")

def test_usa_excel_data():
    """Test if USA Excel data has the required fields"""
    
    print("\n" + "=" * 60)
    print("🔍 TESTING USA EXCEL DATA AVAILABILITY")
    print("=" * 60)
    
    try:
        # Import the Excel tool
        import sys
        sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')
        from excel_power_plant_tool import ExcelPowerPlantTool
        
        excel_tool = ExcelPowerPlantTool()
        
        # Test search for Antelope Valley Station
        print("🔍 Searching for Antelope Valley Station in Excel data...")
        
        # Search for the plant using correct method
        search_results = excel_tool.get_plant_data("Antelope Valley Station")
        
        if search_results:
            print(f"✅ Found {len(search_results)} units")

            for unit in search_results:
                print(f"\n📊 Unit {unit.get('unit_id', 'Unknown')}:")
                
                # Check for generation data
                generation_fields = [k for k in unit.keys() if 'generation' in k.lower() or 'mwh' in k.lower() or 'gwh' in k.lower()]
                if generation_fields:
                    print("  ✅ Generation fields found:")
                    for field in generation_fields:
                        print(f"    • {field}: {unit[field]}")
                else:
                    print("  ❌ No generation fields found")
                
                # Check for fuel consumption data
                fuel_fields = [k for k in unit.keys() if 'fuel' in k.lower() or 'coal' in k.lower() or 'consumption' in k.lower()]
                if fuel_fields:
                    print("  ✅ Fuel fields found:")
                    for field in fuel_fields:
                        print(f"    • {field}: {unit[field]}")
                else:
                    print("  ❌ No fuel fields found")
                
                # Check for emissions data
                emission_fields = [k for k in unit.keys() if 'emission' in k.lower() or 'co2' in k.lower()]
                if emission_fields:
                    print("  ✅ Emission fields found:")
                    for field in emission_fields:
                        print(f"    • {field}: {unit[field]}")
                else:
                    print("  ❌ No emission fields found")
                
                print(f"  📋 All available fields ({len(unit)} total):")
                for key in sorted(unit.keys()):
                    print(f"    • {key}")
        else:
            print("❌ No units found in Excel data")
            
    except Exception as e:
        print(f"❌ Error testing Excel data: {e}")
        import traceback
        traceback.print_exc()

def check_web_search_schema():
    """Check what fields are being extracted by web search"""
    
    print("\n" + "=" * 60)
    print("🔍 CHECKING WEB SEARCH EXTRACTION SCHEMA")
    print("=" * 60)
    
    try:
        # Check the unit extraction stages
        with open('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/backend/src/agent/unit_extraction_stages.py', 'r') as f:
            content = f.read()
        
        print("🔍 Searching for generation-related extraction patterns...")
        
        # Look for generation patterns
        generation_patterns = []
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if any(term in line.lower() for term in ['generation', 'mwh', 'gwh', 'output', 'produced']):
                if 'search' in line.lower() or 'extract' in line.lower() or 'look for' in line.lower():
                    generation_patterns.append(f"Line {i+1}: {line.strip()}")
        
        if generation_patterns:
            print("  ✅ Found generation extraction patterns:")
            for pattern in generation_patterns[:10]:  # Show first 10
                print(f"    • {pattern}")
        else:
            print("  ❌ No generation extraction patterns found")
        
        print("\n🔍 Searching for fuel consumption extraction patterns...")
        
        # Look for fuel patterns
        fuel_patterns = []
        for i, line in enumerate(lines):
            if any(term in line.lower() for term in ['fuel', 'coal', 'consumption', 'tons', 'consumed']):
                if 'search' in line.lower() or 'extract' in line.lower() or 'look for' in line.lower():
                    fuel_patterns.append(f"Line {i+1}: {line.strip()}")
        
        if fuel_patterns:
            print("  ✅ Found fuel extraction patterns:")
            for pattern in fuel_patterns[:10]:  # Show first 10
                print(f"    • {pattern}")
        else:
            print("  ❌ No fuel extraction patterns found")
        
        print("\n🔍 Searching for emissions extraction patterns...")
        
        # Look for emission patterns
        emission_patterns = []
        for i, line in enumerate(lines):
            if any(term in line.lower() for term in ['emission', 'co2', 'carbon', 'factor']):
                if 'search' in line.lower() or 'extract' in line.lower() or 'look for' in line.lower():
                    emission_patterns.append(f"Line {i+1}: {line.strip()}")
        
        if emission_patterns:
            print("  ✅ Found emission extraction patterns:")
            for pattern in emission_patterns[:10]:  # Show first 10
                print(f"    • {pattern}")
        else:
            print("  ❌ No emission extraction patterns found")
            
    except Exception as e:
        print(f"❌ Error checking web search schema: {e}")

if __name__ == "__main__":
    # Test actual unit data
    test_antelope_valley_data()
    
    # Test USA Excel data
    test_usa_excel_data()
    
    # Check web search schema
    check_web_search_schema()
    
    print("\n" + "=" * 60)
    print("🎯 SUMMARY OF FINDINGS")
    print("=" * 60)
    print()
    print("1. 📊 CURRENT UNIT JSON:")
    print("   - Has basic fields (capacity, technology, efficiency)")
    print("   - Missing generation, fuel consumption, emissions data")
    print("   - Empty arrays for calculated fields")
    print()
    print("2. 📋 USA EXCEL DATA:")
    print("   - Need to verify if it contains generation/fuel/emissions data")
    print("   - May have time series data for calculations")
    print()
    print("3. 🔍 WEB SEARCH SCHEMA:")
    print("   - Need to add extraction patterns for missing fields")
    print("   - Current schema may not capture generation/fuel/emissions")
    print()
    print("4. 🎯 NEXT ACTIONS:")
    print("   - Update web search extraction patterns")
    print("   - Test with multiple plants to verify data availability")
    print("   - Implement fallback calculations when data is missing")
