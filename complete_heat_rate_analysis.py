#!/usr/bin/env python3
"""
Complete analysis of Heat Rate & Efficiency sheet
"""

import pandas as pd
import openpyxl

def analyze_complete_heat_rate_sheet():
    """Get complete data from Heat Rate & Efficiency sheet"""
    print("🔍 COMPLETE HEAT RATE & EFFICIENCY ANALYSIS")
    print("=" * 80)
    
    try:
        # Load the workbook
        wb = openpyxl.load_workbook('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/current state calculations.xlsx', data_only=False)
        ws = wb["Heat Rate & Efficiency"]
        
        print(f"📏 Sheet dimensions: {ws.max_row} rows x {ws.max_column} columns")
        
        # Get ALL data from the sheet
        print(f"\n📋 COMPLETE SHEET CONTENT:")
        print("-" * 80)
        
        for row in range(1, ws.max_row + 1):
            row_data = []
            for col in range(1, ws.max_column + 1):
                cell = ws.cell(row=row, column=col)
                cell_value = cell.value
                if cell_value:
                    row_data.append(str(cell_value))
            
            if row_data:
                print(f"Row {row:2d}: {' | '.join(row_data)}")
        
        wb.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def analyze_assumptions_sheet():
    """Analyze the Assumptions sheet for GCV lookup table"""
    print("\n🔍 ASSUMPTIONS SHEET ANALYSIS")
    print("=" * 80)
    
    try:
        # Load the workbook
        wb = openpyxl.load_workbook('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/current state calculations.xlsx', data_only=False)
        ws = wb["Assumptions"]
        
        print(f"📏 Sheet dimensions: {ws.max_row} rows x {ws.max_column} columns")
        
        # Get ALL data from the sheet
        print(f"\n📋 ASSUMPTIONS SHEET CONTENT:")
        print("-" * 80)
        
        for row in range(1, min(30, ws.max_row + 1)):  # First 30 rows
            row_data = []
            for col in range(1, ws.max_column + 1):
                cell = ws.cell(row=row, column=col)
                cell_value = cell.value
                if cell_value:
                    row_data.append(str(cell_value))
            
            if row_data:
                print(f"Row {row:2d}: {' | '.join(row_data)}")
        
        wb.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main function"""
    analyze_complete_heat_rate_sheet()
    analyze_assumptions_sheet()

if __name__ == "__main__":
    main()
