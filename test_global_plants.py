#!/usr/bin/env python3
"""
Test Global Plant Functionality
===============================

Tests the global fixes for non-USA plants:
1. Country-specific retirement ages
2. Remaining useful life calculation
3. Fuel percentage time range
4. Fallback calculations
"""

import sys
from datetime import datetime

# Add the backend directory to path
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/backend/src')
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')

def test_country_retirement_ages():
    """Test country-specific retirement ages"""
    print("🌍 TESTING COUNTRY-SPECIFIC RETIREMENT AGES")
    print("=" * 60)
    
    try:
        from agent.reference_data import CALCULATOR
        
        # Test different countries
        test_countries = [
            ("United States", 50),
            ("USA", 50),
            ("India", 25),
            ("China", 30),
            ("Germany", 40),
            ("Japan", 40),
            ("Australia", 45),
            ("Unknown Country", 35),  # Should use default
            ("", 35),  # Empty string should use default
        ]
        
        all_correct = True
        
        for country, expected_age in test_countries:
            actual_age = CALCULATOR.get_country_retirement_age(country)
            status = "✅" if actual_age == expected_age else "❌"
            print(f"{status} {country or '(empty)'}: {actual_age} years (expected: {expected_age})")
            
            if actual_age != expected_age:
                all_correct = False
        
        if all_correct:
            print("✅ SUCCESS: All country retirement ages are correct")
            return True
        else:
            print("❌ ERROR: Some country retirement ages are incorrect")
            return False
                
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_fallback_remaining_useful_life():
    """Test fallback remaining useful life calculation for non-USA plants"""
    print("\n⏰ TESTING FALLBACK REMAINING USEFUL LIFE")
    print("=" * 60)
    
    try:
        from agent.fallback_calculations import FallbackCalculationEngine
        
        # Initialize fallback calculator
        fallback_calc = FallbackCalculationEngine()
        
        # Test data for different countries
        test_cases = [
            {
                "country": "India",
                "commencement_date": "2000-01-01T00:00:00.000Z",
                "expected_retirement_year": 2025  # 2000 + 25 years
            },
            {
                "country": "China", 
                "commencement_date": "2010-06-15T00:00:00.000Z",
                "expected_retirement_year": 2040  # 2010 + 30 years
            },
            {
                "country": "Germany",
                "commencement_date": "2005-03-20T00:00:00.000Z", 
                "expected_retirement_year": 2045  # 2005 + 40 years
            },
            {
                "country": "Unknown Country",
                "commencement_date": "2015-12-01T00:00:00.000Z",
                "expected_retirement_year": 2050  # 2015 + 35 years (default)
            }
        ]
        
        all_correct = True
        
        for test_case in test_cases:
            enhanced_data = {
                "country": test_case["country"],
                "commencement_date": test_case["commencement_date"]
            }
            
            # Run the calculation
            result = fallback_calc._calculate_remaining_useful_life(enhanced_data, "test_session")
            
            if result:
                remaining_useful_life = enhanced_data.get("remaining_useful_life")
                unit_lifetime = enhanced_data.get("unit_lifetime")
                
                if remaining_useful_life:
                    # Parse the retirement year from the ISO timestamp
                    retirement_year = int(remaining_useful_life.split('-')[0])
                    expected_year = test_case["expected_retirement_year"]
                    
                    status = "✅" if retirement_year == expected_year else "❌"
                    print(f"{status} {test_case['country']}: {retirement_year} (expected: {expected_year})")
                    print(f"   Commencement: {test_case['commencement_date'][:10]}")
                    print(f"   Retirement: {remaining_useful_life[:10]}")
                    print(f"   Unit lifetime: {unit_lifetime} years")
                    
                    if retirement_year != expected_year:
                        all_correct = False
                else:
                    print(f"❌ {test_case['country']}: No remaining_useful_life calculated")
                    all_correct = False
            else:
                print(f"❌ {test_case['country']}: Calculation failed")
                all_correct = False
        
        if all_correct:
            print("✅ SUCCESS: All remaining useful life calculations are correct")
            return True
        else:
            print("❌ ERROR: Some remaining useful life calculations are incorrect")
            return False
                
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_fallback_fuel_percentage_years():
    """Test fallback fuel percentage operational years calculation"""
    print("\n⛽ TESTING FALLBACK FUEL PERCENTAGE YEARS")
    print("=" * 60)
    
    try:
        from agent.fallback_calculations import FallbackCalculationEngine
        
        # Initialize fallback calculator
        fallback_calc = FallbackCalculationEngine()
        
        # Test data
        test_cases = [
            {
                "country": "India",
                "commencement_date": "2010-01-01T00:00:00.000Z",
                "expected_start_year": 2010,
                "expected_end_year": 2035  # 2010 + 25 years
            },
            {
                "country": "China",
                "commencement_date": "2000-06-15T00:00:00.000Z", 
                "expected_start_year": 2000,
                "expected_end_year": 2030  # 2000 + 30 years
            }
        ]
        
        all_correct = True
        
        for test_case in test_cases:
            extracted_data = {
                "commencement_date": test_case["commencement_date"]
            }
            unit_context = {
                "country": test_case["country"]
            }
            
            # Get operational years
            operational_years = fallback_calc._get_unit_operational_years(extracted_data, unit_context)
            
            if operational_years:
                # Convert to integers and sort
                years = sorted([int(year) for year in operational_years])
                actual_start = min(years)
                actual_end = max(years)
                
                expected_start = test_case["expected_start_year"]
                expected_end = test_case["expected_end_year"]
                
                # Allow some flexibility in the range due to current year logic
                start_ok = abs(actual_start - expected_start) <= 5
                end_ok = abs(actual_end - expected_end) <= 5
                
                status = "✅" if start_ok and end_ok else "❌"
                print(f"{status} {test_case['country']}: {actual_start}-{actual_end} (expected: ~{expected_start}-{expected_end})")
                print(f"   Total years: {len(operational_years)}")
                print(f"   Sample years: {operational_years[:5]}...")
                
                if not (start_ok and end_ok):
                    all_correct = False
            else:
                print(f"❌ {test_case['country']}: No operational years calculated")
                all_correct = False
        
        if all_correct:
            print("✅ SUCCESS: All fuel percentage year calculations are reasonable")
            return True
        else:
            print("❌ ERROR: Some fuel percentage year calculations are incorrect")
            return False
                
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    """Run global plant tests"""
    print("🌍 TESTING GLOBAL PLANT FUNCTIONALITY")
    print("=" * 80)
    
    results = []
    
    # Test 1: Country retirement ages
    results.append(test_country_retirement_ages())
    
    # Test 2: Fallback remaining useful life
    results.append(test_fallback_remaining_useful_life())
    
    # Test 3: Fallback fuel percentage years
    results.append(test_fallback_fuel_percentage_years())
    
    # Summary
    print("\n" + "=" * 80)
    print("📋 GLOBAL PLANT TEST SUMMARY")
    print("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL GLOBAL PLANT TESTS PASSED!")
        print("\n✅ GLOBAL FUNCTIONALITY VERIFIED:")
        print("   1. ✅ Country-specific retirement ages working")
        print("   2. ✅ Fallback remaining useful life calculation working")
        print("   3. ✅ Fallback fuel percentage years working")
        print("   4. ✅ Non-USA plants will use country-specific logic")
    else:
        print("⚠️ Some global plant tests failed. Check the output above.")

if __name__ == "__main__":
    main()
