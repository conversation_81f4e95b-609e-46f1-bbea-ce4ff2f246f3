#!/usr/bin/env python3
"""
<PERSON>ript to analyze Excel formulas from USA Details.xlsx and current state calculations.xlsx
"""

import pandas as pd
import openpyxl
from openpyxl.utils import get_column_letter
import sys

def analyze_usa_details():
    """Analyze USA Details.xlsx file"""
    print("🔍 ANALYZING USA DETAILS.xlsx")
    print("=" * 60)
    
    try:
        # Load the workbook
        wb = openpyxl.load_workbook('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx', data_only=False)
        
        print(f"📊 Available sheets: {wb.sheetnames}")
        
        # Check Coal sheets for the new "Total Fuel Consumed Tons" column
        coal_sheets = [sheet for sheet in wb.sheetnames if 'Coal' in sheet and any(year in sheet for year in ['2020', '2021', '2022', '2023', '2024'])]
        
        print(f"\n🔥 Coal sheets found: {coal_sheets}")
        
        for sheet_name in coal_sheets:
            print(f"\n📋 Analyzing {sheet_name}:")
            ws = wb[sheet_name]
            
            # Get headers from first row
            headers = []
            for col in range(1, ws.max_column + 1):
                cell_value = ws.cell(row=1, column=col).value
                if cell_value:
                    headers.append(cell_value)
            
            print(f"  📝 Headers: {headers}")
            
            # Look for "Total Fuel Consumed Tons" column
            if "Total Fuel Consumed Tons" in headers:
                col_index = headers.index("Total Fuel Consumed Tons") + 1
                col_letter = get_column_letter(col_index)
                print(f"  ✅ Found 'Total Fuel Consumed Tons' in column {col_letter}")
                
                # Check a few sample values
                sample_values = []
                for row in range(2, min(6, ws.max_row + 1)):  # Check first 4 data rows
                    cell_value = ws.cell(row=row, column=col_index).value
                    sample_values.append(cell_value)
                print(f"  📊 Sample values: {sample_values}")
            else:
                print(f"  ❌ 'Total Fuel Consumed Tons' column not found")
        
        wb.close()
        
    except Exception as e:
        print(f"❌ Error analyzing USA Details.xlsx: {e}")

def analyze_current_state_calculations():
    """Analyze current state calculations.xlsx file"""
    print("\n🔍 ANALYZING CURRENT STATE CALCULATIONS.xlsx")
    print("=" * 60)
    
    try:
        # Load the workbook
        wb = openpyxl.load_workbook('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/current state calculations.xlsx', data_only=False)
        
        print(f"📊 Available sheets: {wb.sheetnames}")
        
        # Focus on "Heat Rate & Efficiency" sheet
        if "Heat Rate & Efficiency" in wb.sheetnames:
            print(f"\n🔥 Analyzing 'Heat Rate & Efficiency' sheet:")
            ws = wb["Heat Rate & Efficiency"]
            
            print(f"  📏 Sheet dimensions: {ws.max_row} rows x {ws.max_column} columns")
            
            # Get all formulas in the sheet
            formulas_found = []
            for row in range(1, ws.max_row + 1):
                for col in range(1, ws.max_column + 1):
                    cell = ws.cell(row=row, column=col)
                    if cell.value and isinstance(cell.value, str) and cell.value.startswith('='):
                        cell_ref = f"{get_column_letter(col)}{row}"
                        formulas_found.append({
                            'cell': cell_ref,
                            'formula': cell.value,
                            'value': cell.displayed_value if hasattr(cell, 'displayed_value') else 'N/A'
                        })
            
            print(f"  🧮 Found {len(formulas_found)} formulas")
            
            # Display formulas
            for i, formula_info in enumerate(formulas_found):
                print(f"\n  📐 Formula {i+1}:")
                print(f"    Cell: {formula_info['cell']}")
                print(f"    Formula: {formula_info['formula']}")
                print(f"    Value: {formula_info['value']}")
                
                if i >= 9:  # Limit to first 10 formulas
                    print(f"    ... and {len(formulas_found) - 10} more formulas")
                    break
            
            # Get ALL data from the sheet since it's small
            print(f"\n  📝 Complete sheet data:")
            for row in range(1, ws.max_row + 1):
                row_data = []
                for col in range(1, ws.max_column + 1):
                    cell = ws.cell(row=row, column=col)
                    cell_value = cell.value
                    if cell_value:
                        # Check if it's a formula
                        if isinstance(cell_value, str) and cell_value.startswith('='):
                            row_data.append(f"FORMULA: {cell_value}")
                        else:
                            row_data.append(str(cell_value))
                if row_data:
                    print(f"    Row {row}: {row_data}")
        else:
            print(f"  ❌ 'Heat Rate & Efficiency' sheet not found")
            print(f"  📋 Available sheets: {wb.sheetnames}")
        
        wb.close()
        
    except Exception as e:
        print(f"❌ Error analyzing current state calculations.xlsx: {e}")

def main():
    """Main function"""
    print("🔧 EXCEL FORMULAS ANALYSIS")
    print("=" * 60)
    print("Task 4: Analyzing Excel files for heat rate and efficiency calculations")
    print("User requested: Read USA Details.xlsx and current state calculations.xlsx")
    print("Focus: New 'Total Fuel Consumed Tons' column and Heat rate/Efficiency formulas")
    
    analyze_usa_details()
    analyze_current_state_calculations()
    
    print(f"\n✅ Analysis completed!")

if __name__ == "__main__":
    main()
