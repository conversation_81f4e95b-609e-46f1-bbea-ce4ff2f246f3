#!/usr/bin/env python3
"""
Test the complete fix for unit detection and all calculations
"""

import sys
import os
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')

from usa_excel_calculation_engine import create_usa_excel_calculation_engine
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_unit_detection_from_usa_details():
    """Test unit detection from USA Details sheet"""
    print("🔍 TESTING UNIT DETECTION FROM USA DETAILS SHEET")
    print("=" * 60)
    
    calc_engine = create_usa_excel_calculation_engine()
    
    # Test unit detection for Antelope Valley
    units = calc_engine.get_plant_units_from_usa_details("Antelope Valley")
    
    print(f"📊 Units detected: {len(units)}")
    
    for unit in units:
        print(f"   Unit {unit['unit_id']}: {unit['capacity_mw']} MW")
        print(f"     Operating Year: {unit['operating_year']}")
        print(f"     Plant Type: {unit['plant_type']}")
    
    if len(units) == 2:
        unit_ids = [unit['unit_id'] for unit in units]
        if '1' in unit_ids and '2' in unit_ids:
            print("✅ CORRECT: Both Unit 1 and Unit 2 detected from USA Details sheet")
            return True
        else:
            print(f"❌ INCORRECT: Expected Unit 1 and 2, got: {unit_ids}")
            return False
    else:
        print(f"❌ INCORRECT: Expected 2 units, got {len(units)}")
        return False

def test_all_calculation_formulas():
    """Test all calculation formulas from Excel sheets"""
    print("\n🧮 TESTING ALL CALCULATION FORMULAS")
    print("=" * 60)
    
    calc_engine = create_usa_excel_calculation_engine()
    
    # Test data
    test_data = {
        'efficiency': 0.35,  # 35% efficiency
        'coal_quantity_tons': 1000000,  # 1 million tons
        'gcv_kcal_per_kg': 6690,  # Bituminous coal GCV
        'gross_generation_mwh': 3000000,  # 3 TWh
        'annual_emission_mt': 2.5,  # 2.5 Mt CO2
        'emission_factor_kg_per_kg': 2.42,  # kg CO2 per kg coal
        'fuel_consumed_tons': 1500000  # 1.5 million tons
    }
    
    print("🔧 Testing Heat Rate & Efficiency calculations:")
    
    # Test heat rate from efficiency
    heat_rate = calc_engine.calculate_heat_rate(test_data['efficiency'])
    print(f"   Heat Rate from efficiency: {heat_rate:.2f} kcal/kWh")
    
    # Test heat rate from fuel data
    heat_rate_fuel = calc_engine.calculate_heat_rate_from_fuel_data(
        test_data['coal_quantity_tons'], 
        test_data['gcv_kcal_per_kg'], 
        test_data['gross_generation_mwh']
    )
    print(f"   Heat Rate from fuel data: {heat_rate_fuel:.2f} kcal/kWh")
    
    # Test efficiency from fuel data
    efficiency_calc = calc_engine.calculate_efficiency_from_fuel_data(
        test_data['gross_generation_mwh'],
        test_data['gcv_kcal_per_kg'],
        test_data['fuel_consumed_tons']
    )
    print(f"   Efficiency from fuel data: {efficiency_calc:.3f} ({efficiency_calc*100:.1f}%)")
    
    print("\n🔧 Testing Fuel Consumption & Emissions calculations:")
    
    # Test fuel consumption from emissions
    fuel_consumption = calc_engine.calculate_fuel_consumption_from_emissions(
        test_data['annual_emission_mt'],
        test_data['emission_factor_kg_per_kg']
    )
    print(f"   Fuel consumption from emissions: {fuel_consumption:,.0f} kg")
    
    # Test emission factor calculation
    emission_factor = calc_engine.calculate_emission_factor(
        test_data['annual_emission_mt'],
        test_data['gross_generation_mwh']
    )
    print(f"   Emission factor: {emission_factor:.6f} kg CO2/kWh")
    
    # Test heat from coal
    heat_from_coal = calc_engine.calculate_heat_from_coal(
        test_data['coal_quantity_tons'] * 1000,  # Convert to kg
        test_data['gcv_kcal_per_kg']
    )
    print(f"   Heat from coal: {heat_from_coal:,.0f} kWh")
    
    # Test electricity from heat
    electricity_from_heat = calc_engine.calculate_electricity_from_heat(
        heat_from_coal,
        test_data['efficiency']
    )
    print(f"   Electricity from heat: {electricity_from_heat:,.0f} kWh")
    
    print("✅ All calculation formulas tested")

def test_multiple_unit_processing():
    """Test processing both units separately"""
    print("\n🏭 TESTING MULTIPLE UNIT PROCESSING")
    print("=" * 60)
    
    calc_engine = create_usa_excel_calculation_engine()
    
    # Get units from USA Details
    units = calc_engine.get_plant_units_from_usa_details("Antelope Valley")
    
    if not units:
        print("❌ No units found - cannot test processing")
        return False
    
    print(f"📊 Processing {len(units)} units:")
    
    all_results = {}
    
    for unit in units:
        unit_id = unit['unit_id']
        capacity = unit['capacity_mw']
        
        print(f"\n🔧 Processing Unit {unit_id} ({capacity} MW)")
        
        unit_data = {
            "unit_number": unit_id,
            "capacity": capacity,
            "technology": "subcritical"
        }
        
        try:
            result = calc_engine.calculate_unit_parameters_usa("Antelope Valley", unit_data)
            
            if result:
                all_results[unit_id] = result
                print(f"✅ Unit {unit_id} calculation successful")
                
                # Show key metrics
                plf_values = result.get('plf', [])
                if plf_values:
                    first_plf = plf_values[0].get('value', 0) * 100
                    print(f"   PLF (2024): {first_plf:.1f}%")
                
                aux_values = result.get('auxiliary_power_consumed', [])
                if aux_values:
                    first_aux = aux_values[0].get('value', 0) * 100
                    print(f"   Auxiliary Power: {first_aux:.1f}%")
                    
                gross_values = result.get('gross_power_generation', [])
                if gross_values:
                    first_gross = gross_values[0].get('value', 0)
                    print(f"   Gross Generation (2024): {first_gross:.1f} GWh")
                    
            else:
                print(f"❌ Unit {unit_id} calculation failed")
                
        except Exception as e:
            print(f"❌ Unit {unit_id} processing failed: {e}")
    
    # Verify both units were processed
    if len(all_results) == len(units):
        print(f"\n✅ SUCCESS: All {len(units)} units processed successfully")
        
        # Check if results are different or same (they should be same for Case 1)
        unit_ids = list(all_results.keys())
        if len(unit_ids) >= 2:
            unit1_plf = all_results[unit_ids[0]].get('plf', [{}])[0].get('value', 0)
            unit2_plf = all_results[unit_ids[1]].get('plf', [{}])[0].get('value', 0)
            
            if abs(unit1_plf - unit2_plf) < 0.001:  # Same values (Case 1 behavior)
                print(f"✅ Both units have same PLF (Case 1 behavior): {unit1_plf*100:.1f}%")
            else:
                print(f"📊 Units have different PLF: Unit {unit_ids[0]}={unit1_plf*100:.1f}%, Unit {unit_ids[1]}={unit2_plf*100:.1f}%")
        
        return True
    else:
        print(f"❌ FAILURE: Only {len(all_results)} out of {len(units)} units processed")
        return False

if __name__ == "__main__":
    print("🚀 COMPREHENSIVE FIX TESTING")
    print("=" * 80)
    
    # Test 1: Unit detection
    unit_detection_ok = test_unit_detection_from_usa_details()
    
    # Test 2: All calculation formulas
    test_all_calculation_formulas()
    
    # Test 3: Multiple unit processing
    processing_ok = test_multiple_unit_processing()
    
    print("\n" + "=" * 80)
    print("📋 SUMMARY:")
    print(f"   Unit Detection: {'✅ PASS' if unit_detection_ok else '❌ FAIL'}")
    print(f"   Multiple Unit Processing: {'✅ PASS' if processing_ok else '❌ FAIL'}")
    
    if unit_detection_ok and processing_ok:
        print("\n🎉 ALL TESTS PASSED - ISSUES SHOULD BE FIXED!")
    else:
        print("\n⚠️ SOME TESTS FAILED - ISSUES REMAIN")
