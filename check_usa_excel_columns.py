#!/usr/bin/env python3
"""
Check the actual column names in USA Details.xlsx
"""

import pandas as pd

def check_usa_excel_structure():
    """Check the structure of USA Details.xlsx"""
    print("🔍 CHECKING USA DETAILS.XLSX STRUCTURE")
    print("=" * 60)
    
    try:
        usa_file = "/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx"
        
        # Check Coal 2024 sheet
        df_2024 = pd.read_excel(usa_file, sheet_name="Coal 2024")
        
        print(f"📊 Coal 2024 sheet shape: {df_2024.shape}")
        print(f"📋 Column names: {list(df_2024.columns)}")
        
        # Filter for Antelope Valley
        antelope_records = df_2024[df_2024['Plant Name'].str.contains('Antelope Valley', na=False)]
        
        print(f"\n📊 Found {len(antelope_records)} records for Antelope Valley")
        
        if len(antelope_records) > 0:
            print("\n📋 Antelope Valley records (all columns):")
            for idx, record in antelope_records.iterrows():
                print(f"\n   Record {idx}:")
                for col in df_2024.columns:
                    value = record.get(col, 'N/A')
                    print(f"     {col}: {value}")
        
        # Check if there are unit-related columns
        unit_columns = [col for col in df_2024.columns if 'unit' in col.lower() or 'id' in col.lower()]
        print(f"\n🔍 Potential unit-related columns: {unit_columns}")
        
    except Exception as e:
        print(f"❌ Failed to check USA Excel structure: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_usa_excel_structure()
