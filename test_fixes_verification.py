#!/usr/bin/env python3
"""
Test script to verify the auxiliary power and remaining useful life fixes
"""

import sys
import os
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/backend/src')

def test_auxiliary_power_and_remaining_life():
    """Test both fixes for auxiliary power and remaining useful life"""
    
    print("🔧 TESTING AUXILIARY POWER & REMAINING USEFUL LIFE FIXES")
    print("=" * 60)
    
    try:
        # Import required modules
        from excel_power_plant_tool import ExcelPowerPlantTool
        from agent.unit_extraction_stages import combine_unit_data
        
        # Initialize Excel tool
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx')
        
        # Test with Antelope Valley Station (2 units)
        plant_name = "Antelope Valley Station"
        
        for unit_num in ['1', '2']:
            print(f"\n📊 TESTING UNIT {unit_num}")
            print("-" * 40)
            
            # Get Excel data
            excel_results = excel_tool.get_plant_data(plant_name, unit_num)
            
            if excel_results:
                excel_data = excel_results[0]
                
                print(f"📋 Excel Data:")
                print(f"  capacity: {excel_data.get('capacity')} MW")
                print(f"  commencement_date: {excel_data.get('commencement_date')}")
                print(f"  remaining_useful_life: {excel_data.get('remaining_useful_life')}")
                print(f"  unit_lifetime: {excel_data.get('unit_lifetime')} years")
                
                # Create plant context
                plant_context = {
                    'excel_data': excel_data,
                    'plant_name': plant_name,
                    'country': 'United States',
                    'plant_uid': f'test-fix-verification-{unit_num}',
                    'plant_type': 'coal'
                }
                
                # Process unit data
                print(f"\n🔧 Processing Unit {unit_num} data...")
                combined_data = combine_unit_data([], unit_num, plant_context)
                
                # Check results
                print(f"\n📊 FINAL RESULTS:")
                print(f"  remaining_useful_life: {combined_data.get('remaining_useful_life')}")
                
                # Check auxiliary power format
                aux_power = combined_data.get('auxiliary_power_consumed', [])
                if aux_power and len(aux_power) > 0:
                    first_aux = aux_power[0]
                    print(f"  auxiliary_power_consumed[0]: {first_aux}")
                    
                    # Verify it's in decimal format (0.08) not percentage (8.00)
                    aux_value = first_aux.get('value', 0)
                    if aux_value < 1.0:
                        print(f"  ✅ Auxiliary power is in decimal format: {aux_value}")
                    else:
                        print(f"  ❌ Auxiliary power still in percentage format: {aux_value}")
                else:
                    print(f"  auxiliary_power_consumed: {aux_power}")
                
                # Verify remaining useful life is not null
                rul = combined_data.get('remaining_useful_life')
                if rul and rul not in [None, "", "null", "Not available"]:
                    print(f"  ✅ Remaining useful life has valid value: {rul}")
                else:
                    print(f"  ❌ Remaining useful life is null/empty: {rul}")
                    
            else:
                print(f"❌ No Excel data found for Unit {unit_num}")
        
        print(f"\n✅ Test completed!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_auxiliary_power_and_remaining_life()
