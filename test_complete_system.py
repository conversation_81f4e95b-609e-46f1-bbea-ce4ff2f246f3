#!/usr/bin/env python3
"""
Complete system test to verify all fixes work together
"""

import sys
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/backend/src')

from excel_power_plant_tool import ExcelPowerPlantTool
from agent.unit_extraction_stages import combine_unit_data
from agent.power_plant_calculation_engine import create_calculation_engine
import json

def test_complete_unit_processing():
    """Test complete unit processing for Cross Generating Station"""
    
    print("🔍 COMPLETE SYSTEM TEST - CROSS GENERATING STATION")
    print("=" * 80)
    
    try:
        # Initialize Excel tool
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx')
        
        plant_name = "Cross Generating Station"
        
        # Process all 4 units
        for unit_num in ['1', '2', '3', '4']:
            print(f"\n📊 PROCESSING UNIT {unit_num}")
            print("-" * 40)
            
            # Get Excel data for this unit
            excel_results = excel_tool.get_plant_data(plant_name, unit_num)
            
            if excel_results:
                excel_data = excel_results[0]
                
                # Create plant context
                plant_context = {
                    'excel_data': excel_data,
                    'plant_name': plant_name,
                    'country': 'United States',
                    'plant_uid': f'test-uuid-{unit_num}',
                    'plant_type': 'coal'
                }
                
                # Process unit data
                combined_data = combine_unit_data([], unit_num, plant_context)
                
                # Verify critical fields
                capacity = combined_data.get('capacity')
                plf = combined_data.get('plf', [])
                auxiliary_power = combined_data.get('auxiliary_power_consumed', [])
                gross_power = combined_data.get('gross_power_generation', [])
                emission_factor = combined_data.get('emission_factor', [])
                
                print(f"✅ Unit {unit_num} Results:")
                print(f"   📊 Capacity: {capacity} MW")
                print(f"   📊 PLF records: {len(plf)}")
                print(f"   📊 Auxiliary power records: {len(auxiliary_power)}")
                print(f"   📊 Gross power records: {len(gross_power)}")
                print(f"   📊 Emission factor records: {len(emission_factor)}")
                
                # Check data quality
                issues = []
                
                if not capacity or capacity <= 0:
                    issues.append("Invalid capacity")
                
                if len(plf) == 0:
                    issues.append("Missing PLF data")
                
                if len(auxiliary_power) == 0:
                    issues.append("Missing auxiliary power data")
                
                if len(gross_power) == 0:
                    issues.append("Missing gross power generation data")
                
                if len(emission_factor) == 0:
                    issues.append("Missing emission factor data")
                
                # Check emission factor years
                ef_years = [ef.get('year') for ef in emission_factor if isinstance(ef, dict)]
                unique_years = len(set(ef_years))
                if unique_years <= 1:
                    issues.append("Emission factor years not diverse")
                
                # Check gross power calculation
                if plf and gross_power and capacity:
                    expected_gross = capacity * plf[0]['value'] * 8760 / 1000
                    actual_gross = gross_power[0]['value']
                    if abs(expected_gross - actual_gross) > 1:
                        issues.append("Gross power calculation incorrect")
                
                if issues:
                    print(f"   ❌ Issues found: {', '.join(issues)}")
                else:
                    print(f"   ✅ All data looks good!")
                
                # Show sample data
                if plf:
                    print(f"   📈 Sample PLF: {plf[0]}")
                if gross_power:
                    print(f"   ⚡ Sample Gross Power: {gross_power[0]}")
                if emission_factor:
                    print(f"   🌍 Sample Emission Factor: {emission_factor[0]}")
                
            else:
                print(f"❌ Unit {unit_num}: No Excel data found")
        
        print(f"\n🎉 COMPLETE SYSTEM TEST FINISHED!")
        
    except Exception as e:
        print(f"❌ Complete system test failed: {e}")
        import traceback
        traceback.print_exc()

def test_emission_factor_data_quality():
    """Test emission factor data quality in detail"""
    
    print("\n🔍 DETAILED EMISSION FACTOR ANALYSIS")
    print("=" * 60)
    
    try:
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx')
        
        # Test Unit 1
        excel_results = excel_tool.get_plant_data("Cross Generating Station", "1")
        if excel_results:
            excel_data = excel_results[0]
            emission_factor = excel_data.get('emission_factor', [])
            
            print(f"📊 Raw emission factor data: {emission_factor}")
            
            # Analyze the data
            years = [ef.get('year') for ef in emission_factor]
            values = [ef.get('value') for ef in emission_factor]
            
            print(f"📅 Years: {years}")
            print(f"📊 Values: {values}")
            
            # Check for issues
            if len(set(years)) == len(years):
                print("✅ All years are unique")
            else:
                print("❌ Duplicate years found")
            
            if all(isinstance(year, int) for year in years):
                print("✅ All years are integers")
            else:
                print("❌ Some years are not integers")
            
            if all(2020 <= year <= 2024 for year in years):
                print("✅ All years are in expected range (2020-2024)")
            else:
                print("❌ Some years are outside expected range")
            
            if all(0 < value < 2 for value in values):
                print("✅ All emission factor values are reasonable")
            else:
                print("❌ Some emission factor values seem unreasonable")
                
    except Exception as e:
        print(f"❌ Emission factor analysis failed: {e}")

if __name__ == "__main__":
    test_complete_unit_processing()
    test_emission_factor_data_quality()
