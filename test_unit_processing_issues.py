#!/usr/bin/env python3
"""
Test script to identify and verify unit processing issues
"""

import sys
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/backend/src')

from excel_power_plant_tool import ExcelPowerPlantTool
from agent.unit_extraction_stages import combine_unit_data
from agent.power_plant_calculation_engine import create_calculation_engine
import json

def test_excel_data_extraction():
    """Test Excel data extraction for Cross Generating Station"""
    
    print("🔍 TESTING EXCEL DATA EXTRACTION")
    print("=" * 60)
    
    try:
        # Initialize Excel tool
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx')
        
        # Test with Cross Generating Station
        plant_name = "Cross Generating Station"
        
        # Get all units for the plant
        all_results = excel_tool.get_plant_data(plant_name)
        print(f"📊 Found {len(all_results)} total units for {plant_name}")
        
        # Get individual units
        for unit_num in ['1', '2', '3', '4']:
            unit_results = excel_tool.get_plant_data(plant_name, unit_num)
            if unit_results:
                unit_data = unit_results[0]
                print(f"\n✅ Unit {unit_num} data found:")
                print(f"   - Unit ID: {unit_data.get('unit_id')}")
                print(f"   - Capacity: {unit_data.get('capacity')} MW")
                print(f"   - Emission Factor: {unit_data.get('emission_factor')}")
                print(f"   - Commencement Date: {unit_data.get('commencement_date')}")
            else:
                print(f"❌ Unit {unit_num} data not found")
                
    except Exception as e:
        print(f"❌ Excel data extraction failed: {e}")

def test_emission_factor_processing():
    """Test emission factor time series processing"""
    
    print("\n🔍 TESTING EMISSION FACTOR PROCESSING")
    print("=" * 60)
    
    try:
        # Create calculation engine
        calc_engine = create_calculation_engine('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/current state calculations.xlsx')
        
        # Test emission factor calculation
        unit_data = {
            'capacity': 300,
            'technology': 'subcritical',
            'coal_type': 'bituminous'
        }
        
        plant_context = {
            'plant_name': 'Cross Generating Station',
            'country': 'United States'
        }
        
        # Run calculations
        calc_results = calc_engine.calculate_unit_parameters(unit_data, plant_context)
        
        print(f"📊 Calculation results keys: {list(calc_results.keys())}")
        
        if 'time_series_emission_factor' in calc_results:
            emission_ts = calc_results['time_series_emission_factor']
            print(f"📅 Time series emission factor years: {list(emission_ts.keys())}")
            print(f"📊 Sample values: {list(emission_ts.values())[:3]}")
        
    except Exception as e:
        print(f"❌ Emission factor processing failed: {e}")

def test_combine_unit_data_function():
    """Test the combine_unit_data function with Excel data"""
    
    print("\n🔍 TESTING COMBINE_UNIT_DATA FUNCTION")
    print("=" * 60)
    
    try:
        # Get Excel data for Unit 1
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx')
        excel_results = excel_tool.get_plant_data("Cross Generating Station", "1")
        
        if excel_results:
            excel_data = excel_results[0]
            print(f"📊 Excel data for Unit 1: {list(excel_data.keys())}")
            
            # Test combine_unit_data with Excel data
            plant_context = {
                'excel_data': excel_data,
                'plant_name': 'Cross Generating Station',
                'country': 'United States',
                'plant_uid': 'test-uuid-123',
                'plant_type': 'coal'
            }
            
            # Call combine_unit_data
            combined_data = combine_unit_data([], "1", plant_context)
            
            print(f"\n📋 Combined data keys: {list(combined_data.keys())}")
            print(f"📊 Emission factor: {combined_data.get('emission_factor')}")
            print(f"📊 PLF: {combined_data.get('plf')}")
            print(f"📊 Auxiliary power: {combined_data.get('auxiliary_power_consumed')}")
            print(f"📊 Gross power generation: {combined_data.get('gross_power_generation')}")
            
        else:
            print("❌ No Excel data found for Unit 1")
            
    except Exception as e:
        print(f"❌ combine_unit_data test failed: {e}")

def test_unit_level_json():
    """Test unit_level.json loading"""
    
    print("\n🔍 TESTING UNIT_LEVEL.JSON")
    print("=" * 60)
    
    try:
        with open('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/unit_level.json', 'r') as f:
            unit_template = json.load(f)
        
        print(f"✅ unit_level.json loaded with {len(unit_template)} fields")
        
        # Check for critical fields
        critical_fields = ['auxiliary_power_consumed', 'plf', 'gross_power_generation', 'emission_factor']
        for field in critical_fields:
            if field in unit_template:
                print(f"✅ {field}: Found in template")
            else:
                print(f"❌ {field}: Missing from template")
                
    except Exception as e:
        print(f"❌ unit_level.json test failed: {e}")

if __name__ == "__main__":
    print("🧪 UNIT PROCESSING ISSUES TEST")
    print("=" * 80)
    
    test_excel_data_extraction()
    test_emission_factor_processing()
    test_combine_unit_data_function()
    test_unit_level_json()
    
    print("\n🎉 TEST COMPLETED!")
