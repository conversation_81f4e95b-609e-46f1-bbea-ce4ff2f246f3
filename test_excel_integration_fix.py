#!/usr/bin/env python3
"""
Test script to verify Excel integration fix
"""

import sys
sys.path.append('backend/src')

from excel_power_plant_tool import ExcelPowerPlantTool
from agent.unit_extraction_stages import combine_unit_data

def test_excel_integration():
    """Test the Excel integration with combine_unit_data function"""
    
    print("🔍 Testing Excel integration fix...")
    
    # 1. Test Excel tool data extraction
    excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx')
    results = excel_tool.get_plant_data('Cross Generating Station', '1')
    
    if not results:
        print("❌ Excel tool failed to find data")
        return False
    
    excel_data = results[0]
    print(f"✅ Excel tool found data for Cross Unit 1:")
    print(f"   - Capacity: {excel_data.get('capacity')} MW")
    print(f"   - Commencement: {excel_data.get('commencement_date')}")
    print(f"   - Unit ID: {excel_data.get('unit_id')}")
    
    # 2. Test combine_unit_data with Excel data
    print("\n🔧 Testing combine_unit_data with Excel data...")
    
    # Mock stage results (empty since we're using Excel data)
    stage_results = [{}, {}, {}, {}, {}]
    
    # Plant context with Excel data
    plant_context = {
        'plant_type': 'coal',
        'plant_id': '1',
        'plant_uid': 'test-uuid-123',
        'country': 'USA',
        'excel_data': excel_data  # This is the key addition
    }
    
    # Call combine_unit_data
    try:
        unit_data = combine_unit_data(stage_results, '1', plant_context)
        
        print(f"✅ combine_unit_data completed successfully!")
        print(f"   - Generated {len(unit_data)} fields")
        print(f"   - Capacity from Excel: {unit_data.get('capacity')} MW")
        print(f"   - Commencement from Excel: {unit_data.get('commencement_date')}")
        print(f"   - Unit ID from Excel: {unit_data.get('unit_id')}")
        print(f"   - Plant Name from Excel: {unit_data.get('plant_name')}")
        
        # Check if Excel data was used
        if unit_data.get('capacity') == excel_data.get('capacity'):
            print("✅ Excel capacity data was used correctly!")
        else:
            print(f"❌ Excel capacity not used. Expected: {excel_data.get('capacity')}, Got: {unit_data.get('capacity')}")
            
        if unit_data.get('commencement_date') == excel_data.get('commencement_date'):
            print("✅ Excel commencement date was used correctly!")
        else:
            print(f"❌ Excel commencement date not used. Expected: {excel_data.get('commencement_date')}, Got: {unit_data.get('commencement_date')}")
        
        return True
        
    except Exception as e:
        print(f"❌ combine_unit_data failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_excel_integration()
    if success:
        print("\n🎉 Excel integration test PASSED!")
    else:
        print("\n💥 Excel integration test FAILED!")
