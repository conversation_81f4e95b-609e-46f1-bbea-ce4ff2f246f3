#!/usr/bin/env python3
"""
Test Global Fixes Implementation
================================

Tests the global fixes for:
1. Auxiliary power calculation using plant capacity instead of unit capacity
2. Remaining useful life calculation with country-specific retirement ages
3. Fuel percentage time range using retirement year instead of 2050
4. Dynamic time series capacity accounting for retired units

This will verify that all fixes work correctly for both USA and non-USA plants.
"""

import sys
import json
from datetime import datetime
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')

from excel_power_plant_tool import ExcelPowerPlantTool
from usa_excel_calculation_engine import USAExcelCalculationEngine, create_usa_excel_calculation_engine
from backend.src.agent.reference_data import CALCULATOR

def test_usa_auxiliary_power_fix():
    """Test that USA plants use plant capacity for auxiliary power calculation"""
    print("\n🔥 TESTING USA AUXILIARY POWER FIX")
    print("=" * 60)
    
    try:
        # Initialize USA calculation engine
        usa_engine = create_usa_excel_calculation_engine()
        
        # Test a known USA plant
        plant_name = "Antelope Valley Station"
        
        # Get plant capacity (should be sum of all units)
        plant_capacity = usa_engine._calculate_plant_capacity_from_excel(plant_name)
        print(f"Plant capacity for {plant_name}: {plant_capacity} MW")
        
        # Get auxiliary power based on plant capacity
        aux_power = usa_engine.get_auxiliary_power_percentage(plant_capacity, 'subcritical')
        print(f"Auxiliary power (plant capacity): {aux_power*100:.1f}%")
        
        # Compare with what it would be for a single unit (should be different)
        unit_capacity = 238.5  # Known unit capacity for Antelope Valley Unit 2
        unit_aux_power = usa_engine.get_auxiliary_power_percentage(unit_capacity, 'subcritical')
        print(f"Auxiliary power (unit capacity): {unit_aux_power*100:.1f}%")
        
        if plant_capacity > unit_capacity and aux_power != unit_aux_power:
            print("✅ SUCCESS: Plant capacity is correctly used for auxiliary power calculation")
            print(f"   Plant capacity ({plant_capacity} MW) gives {aux_power*100:.1f}% aux power")
            print(f"   Unit capacity ({unit_capacity} MW) would give {unit_aux_power*100:.1f}% aux power")
        else:
            print("❌ ISSUE: Auxiliary power calculation may not be using plant capacity correctly")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")

def test_country_retirement_ages():
    """Test country-specific retirement ages"""
    print("\n🌍 TESTING COUNTRY-SPECIFIC RETIREMENT AGES")
    print("=" * 60)
    
    try:
        # Test different countries
        test_countries = [
            ("United States", 50),
            ("India", 25),
            ("China", 30),
            ("Germany", 40),
            ("Unknown Country", 35)  # Should use default
        ]
        
        for country, expected_age in test_countries:
            actual_age = CALCULATOR.get_country_retirement_age(country)
            print(f"{country}: {actual_age} years (expected: {expected_age})")
            
            if actual_age == expected_age:
                print(f"  ✅ CORRECT")
            else:
                print(f"  ❌ ERROR: Expected {expected_age}, got {actual_age}")
                
    except Exception as e:
        print(f"❌ ERROR: {e}")

def test_usa_year_specific_capacity():
    """Test year-specific capacity calculation for USA plants"""
    print("\n📅 TESTING USA YEAR-SPECIFIC CAPACITY")
    print("=" * 60)
    
    try:
        # Initialize USA calculation engine
        usa_engine = create_usa_excel_calculation_engine()
        
        # Test a plant with multiple units
        plant_name = "Antelope Valley Station"
        
        # Get units information
        units = usa_engine.get_plant_units_from_usa_details(plant_name)
        print(f"Units for {plant_name}:")
        for unit in units:
            print(f"  Unit {unit.get('unit_id')}: {unit.get('capacity_mw')} MW (Operating: {unit.get('operating_year')})")
        
        # Test capacity for different years
        test_years = [2000, 2010, 2020, 2024, 2030, 2070]
        
        for year in test_years:
            capacity = usa_engine._calculate_year_specific_plant_capacity(plant_name, year)
            print(f"Year {year}: {capacity} MW")
            
        print("✅ Year-specific capacity calculation completed")
        
    except Exception as e:
        print(f"❌ ERROR: {e}")

def test_fuel_percentage_time_range():
    """Test fuel percentage time range using retirement year"""
    print("\n⛽ TESTING FUEL PERCENTAGE TIME RANGE")
    print("=" * 60)
    
    try:
        # Initialize Excel tool
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx', 'test_session')
        
        # Test a known plant
        plant_name = "Antelope Valley Station"
        results = excel_tool.get_plant_data(plant_name)
        
        if results:
            result = results[0]  # Get first unit
            fuel_types = result.get('fuel_type', [])
            
            if fuel_types:
                fuel_type = fuel_types[0]
                years_percentage = fuel_type.get('years_percentage', {})
                
                print(f"Fuel percentage years for {plant_name}:")
                years = sorted([int(y) for y in years_percentage.keys() if y.isdigit()])
                print(f"  Year range: {min(years)} to {max(years)}")
                print(f"  Total years: {len(years)}")
                
                # Check if it goes beyond 2050 (should not for most plants)
                if max(years) <= 2080:  # Reasonable upper limit
                    print("✅ SUCCESS: Fuel percentage uses reasonable year range")
                else:
                    print(f"❌ ISSUE: Fuel percentage extends to {max(years)} (too far)")
            else:
                print("⚠️ No fuel type data found")
        else:
            print("⚠️ No plant data found")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")

def test_complete_usa_unit_calculation():
    """Test complete unit calculation with all fixes"""
    print("\n🔧 TESTING COMPLETE USA UNIT CALCULATION")
    print("=" * 60)
    
    try:
        # Initialize USA calculation engine
        usa_engine = create_usa_excel_calculation_engine()
        
        # Test unit calculation
        plant_name = "Antelope Valley Station"
        unit_data = {
            'capacity': 238.5,
            'unit_number': '2',
            'technology': 'subcritical'
        }
        
        print(f"Testing complete calculation for {plant_name} Unit {unit_data['unit_number']}")
        
        # Run the calculation
        results = usa_engine.calculate_unit_parameters_usa(plant_name, unit_data)
        
        if results:
            print("✅ Unit calculation completed successfully")
            
            # Check key results
            plf_data = results.get('plf', [])
            aux_power_data = results.get('auxiliary_power_consumed', [])
            
            print(f"PLF data points: {len(plf_data)}")
            print(f"Auxiliary power data points: {len(aux_power_data)}")
            
            if plf_data:
                print(f"Sample PLF: {plf_data[0]}")
            if aux_power_data:
                print(f"Sample aux power: {aux_power_data[0]}")
                
        else:
            print("❌ Unit calculation failed")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")

def main():
    """Run all tests"""
    print("🚀 TESTING GLOBAL FIXES IMPLEMENTATION")
    print("=" * 80)
    
    # Test 1: USA auxiliary power fix
    test_usa_auxiliary_power_fix()
    
    # Test 2: Country retirement ages
    test_country_retirement_ages()
    
    # Test 3: USA year-specific capacity
    test_usa_year_specific_capacity()
    
    # Test 4: Fuel percentage time range
    test_fuel_percentage_time_range()
    
    # Test 5: Complete USA unit calculation
    test_complete_usa_unit_calculation()
    
    print("\n" + "=" * 80)
    print("🏁 ALL TESTS COMPLETED")

if __name__ == "__main__":
    main()
