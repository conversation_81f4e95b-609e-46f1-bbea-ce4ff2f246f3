#!/usr/bin/env python3
"""
Check the actual capacity data for Antelope Valley from USA Details.xlsx
"""

import pandas as pd

def check_antelope_valley_capacity():
    """Check the actual capacity for Antelope Valley"""
    
    print("🔍 CHECKING ANTELOPE VALLEY CAPACITY DATA")
    print("=" * 60)
    
    try:
        excel_file = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx'
        
        # Check USA Details sheet for capacity information
        print("📊 Checking USA Details sheet:")
        usa_df = pd.read_excel(excel_file, sheet_name='USA Details')
        
        # Find Antelope Valley data
        antelope_mask = usa_df['Plant Name'].str.contains('Antelope Valley', na=False, case=False)
        antelope_data = usa_df[antelope_mask]
        
        if not antelope_data.empty:
            print(f"✅ Found {len(antelope_data)} records for Antelope Valley")
            print(f"📋 Available columns: {list(usa_df.columns)}")
            
            # Look for capacity-related columns
            capacity_cols = [col for col in usa_df.columns if any(word in col.lower() for word in ['capacity', 'mw', 'power', 'size'])]
            print(f"🔧 Capacity-related columns: {capacity_cols}")
            
            # Show all data for Antelope Valley
            print(f"\n📊 ANTELOPE VALLEY DATA:")
            for idx, row in antelope_data.iterrows():
                print(f"\nRecord {idx + 1}:")
                for col in antelope_data.columns:
                    if pd.notna(row[col]):
                        print(f"  {col}: {row[col]}")
        else:
            print("❌ No Antelope Valley data found in USA Details sheet")
        
        # Also check if there are any other sheets with capacity data
        xl_file = pd.ExcelFile(excel_file)
        sheet_names = xl_file.sheet_names
        print(f"\n📋 All available sheets: {sheet_names}")
        
        # Check if there's a capacity or plant details sheet
        for sheet_name in sheet_names:
            if any(word in sheet_name.lower() for word in ['capacity', 'plant', 'detail']):
                print(f"\n🔍 Checking {sheet_name} sheet:")
                try:
                    df = pd.read_excel(excel_file, sheet_name=sheet_name)
                    if 'Plant Name' in df.columns:
                        antelope_in_sheet = df[df['Plant Name'].str.contains('Antelope Valley', na=False, case=False)]
                        if not antelope_in_sheet.empty:
                            print(f"  ✅ Found Antelope Valley data in {sheet_name}")
                            print(f"  📋 Columns: {list(df.columns)}")
                            
                            # Show capacity data if available
                            capacity_cols = [col for col in df.columns if any(word in col.lower() for word in ['capacity', 'mw', 'power'])]
                            if capacity_cols:
                                print(f"  🔧 Capacity columns: {capacity_cols}")
                                for idx, row in antelope_in_sheet.iterrows():
                                    for col in capacity_cols:
                                        if pd.notna(row[col]):
                                            print(f"    {col}: {row[col]}")
                except Exception as e:
                    print(f"  ❌ Error reading {sheet_name}: {e}")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_antelope_valley_capacity()
    
    print("\n" + "=" * 60)
    print("🎯 CAPACITY ANALYSIS SUMMARY")
    print("=" * 60)
    print()
    print("ISSUE: Used 238.5 MW instead of correct 477 MW")
    print("IMPACT: This affects ALL PLF calculations!")
    print()
    print("CORRECT CALCULATION SHOULD BE:")
    print("- Total Plant Capacity: 477 MW")
    print("- If 2 units: Each unit = 477/2 = 238.5 MW")
    print("- If 1 unit: Single unit = 477 MW")
    print()
    print("NEED TO VERIFY:")
    print("1. Is Antelope Valley 1 unit of 477 MW or 2 units of ~238.5 MW each?")
    print("2. What does the USA Details.xlsx actually show for capacity?")
    print("3. How should we interpret the 2 records per year in Coal sheets?")
