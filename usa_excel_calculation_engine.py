#!/usr/bin/env python3
"""
Enhanced Excel Calculation Engine for USA Plants using real data from USA Details.xlsx
"""

import pandas as pd
import logging
from typing import Dict, Any, List, Optional, Tuple

logger = logging.getLogger(__name__)

class USAExcelCalculationEngine:
    """
    Enhanced calculation engine that uses real data from USA Details.xlsx
    for USA plants, implementing PLF Case 1 and Case 4 formulas
    """
    
    def get_plant_units_from_usa_details(self, plant_name: str) -> List[Dict[str, Any]]:
        """
        Extract unit information from USA Details sheet
        Returns list of units with their IDs and capacities
        """
        logger.info(f"🔍 Extracting unit information for {plant_name} from USA Details sheet")

        try:
            # Read USA Details sheet
            df_details = pd.read_excel(self.usa_details_path, sheet_name="USA Details")

            # Filter for the specific plant
            plant_records = df_details[df_details['Plant Name'].str.contains(plant_name, na=False, case=False)]

            if plant_records.empty:
                logger.warning(f"⚠️ No records found for {plant_name} in USA Details sheet")
                return []

            units = []
            for idx, record in plant_records.iterrows():
                unit_id = record.get('Unit IDs', None)
                capacity = record.get('Capacity', None)

                if unit_id is not None and capacity is not None:
                    # 🚨 FIXED: Handle non-numeric unit IDs like 'CTG1', 'GT1', and float strings like '1.0'
                    try:
                        # Try to convert to float first, then int (handles '1.0' -> 1)
                        clean_unit_id = str(int(float(unit_id)))
                    except (ValueError, TypeError):
                        # For non-numeric unit IDs like 'CTG1', keep as string
                        clean_unit_id = str(unit_id)

                    units.append({
                        'unit_id': clean_unit_id,
                        'capacity_mw': float(capacity),
                        'operating_year': record.get('Operating Year', None),
                        'planned_retirement_year': record.get('Planned Retirement Year', None),
                        'plant_type': record.get('Plant Type', 'Unknown')
                    })
                    logger.info(f"   Found Unit {unit_id}: {capacity} MW")

            logger.info(f"✅ Found {len(units)} units for {plant_name}")
            return units

        except Exception as e:
            logger.error(f"❌ Failed to extract units from USA Details: {e}")
            return []

    def __init__(self, usa_details_path: str, calculations_path: str):
        """
        Initialize with paths to both Excel files
        
        Args:
            usa_details_path: Path to USA Details.xlsx
            calculations_path: Path to current state calculations.xlsx
        """
        self.usa_details_path = usa_details_path
        self.calculations_path = calculations_path
        
        # Load auxiliary power data from Assumptions sheet
        self.auxiliary_power_data = self._load_auxiliary_power_data()
        
        logger.info(f"✅ USAExcelCalculationEngine initialized")
        logger.info(f"   USA Details: {usa_details_path}")
        logger.info(f"   Calculations: {calculations_path}")

    def get_auxiliary_power_percentage(self, capacity_mw: float, technology: str) -> float:
        """
        Get auxiliary power percentage based on capacity and technology from Assumptions sheet

        Args:
            capacity_mw: Plant capacity in MW
            technology: Technology type (subcritical, supercritical, etc.)

        Returns:
            Auxiliary power percentage as decimal (e.g., 0.08 for 8%)
        """
        # Normalize technology name
        tech_normalized = technology.lower().replace(' ', '-').replace('_', '-')

        # Map common variations
        tech_mapping = {
            'subcritical': 'subcritical',
            'sub-critical': 'subcritical',
            'supercritical': 'supercritical',
            'super-critical': 'supercritical',
            'ultra-supercritical': 'ultra-supercritical',
            'ultra-super-critical': 'ultra-supercritical',
            'usc': 'ultra-supercritical',
            'advanced-usc': 'advanced-usc',
            'advanced-ultra-supercritical': 'advanced-usc'
        }

        tech_key = tech_mapping.get(tech_normalized, 'subcritical')  # Default to subcritical

        if tech_key not in self.auxiliary_power_data:
            logger.warning(f"Technology '{technology}' not found, using subcritical")
            tech_key = 'subcritical'

        # Find the appropriate capacity range
        capacity_ranges = self.auxiliary_power_data[tech_key]
        for (min_cap, max_cap), aux_power in capacity_ranges.items():
            if min_cap <= capacity_mw <= max_cap:
                logger.info(f"Auxiliary power for {capacity_mw} MW {technology}: {aux_power*100:.1f}%")
                return aux_power

        # Default to largest capacity range if not found
        default_aux = list(capacity_ranges.values())[-1]
        logger.warning(f"Capacity {capacity_mw} MW not in range, using default: {default_aux*100:.1f}%")
        return default_aux
    
    def _load_auxiliary_power_data(self) -> Dict[str, Dict[str, float]]:
        """Load auxiliary power data from Assumptions sheet with capacity-based matrix"""
        try:
            assumptions_df = pd.read_excel(self.calculations_path, sheet_name='Assumptions')

            # 🚨 COMPLETELY FIXED: Capacity and technology-based auxiliary power matrix
            # Based on Assumptions sheet - Unit Level Auxiliary Power Consumption (%)
            aux_power_matrix = {
                'subcritical': {
                    (0, 250): 0.11,      # ≤250 MW: 11%
                    (251, 500): 0.10,    # >250, ≤500 MW: 10%
                    (501, 750): 0.09,    # >500, ≤750 MW: 9%
                    (751, 1000): 0.08,   # >750, ≤1000 MW: 8%
                    (1001, float('inf')): 0.07  # >1000 MW: 7%
                },
                'supercritical': {
                    (0, 250): 0.09,      # ≤250 MW: 9%
                    (251, 500): 0.08,    # >250, ≤500 MW: 8%
                    (501, 750): 0.07,    # >500, ≤750 MW: 7%
                    (751, 1000): 0.06,   # >750, ≤1000 MW: 6%
                    (1001, float('inf')): 0.05  # >1000 MW: 5%
                },
                'ultra-supercritical': {
                    (0, 250): 0.06,      # ≤250 MW: 6%
                    (251, 500): 0.055,   # >250, ≤500 MW: 5.5%
                    (501, 750): 0.05,    # >500, ≤750 MW: 5%
                    (751, 1000): 0.0475, # >750, ≤1000 MW: 4.75%
                    (1001, float('inf')): 0.045  # >1000 MW: 4.5%
                },
                'advanced-usc': {
                    (0, 250): 0.055,     # ≤250 MW: 5.5%
                    (251, 500): 0.0525,  # >250, ≤500 MW: 5.25%
                    (501, 750): 0.05,    # >500, ≤750 MW: 5%
                    (751, 1000): 0.045,  # >750, ≤1000 MW: 4.5%
                    (1001, float('inf')): 0.04   # >1000 MW: 4%
                }
            }

            logger.info(f"✅ Loaded auxiliary power matrix from Assumptions sheet")
            return aux_power_matrix

        except Exception as e:
            logger.warning(f"⚠️ Could not load auxiliary power data: {e}")
            # Return default matrix
            return {
                'subcritical': {
                    (0, 250): 0.11, (251, 500): 0.10, (501, 750): 0.09,
                    (751, 1000): 0.08, (1001, float('inf')): 0.07
                }
            }
    
    def extract_plant_data_from_usa_excel(self, plant_name: str) -> Dict[str, Any]:
        """
        Extract and aggregate plant-level data from USA Details.xlsx Coal yearly sheets
        Using CASE 1 approach: Plant-level data → Unit-level extrapolation

        Args:
            plant_name: Name of the plant (e.g., "Antelope Valley Station")

        Returns:
            Dictionary with aggregated plant-level data by year
        """
        logger.info(f"🔍 Extracting USA plant-level data for: {plant_name}")
        logger.info(f"📋 Using CASE 1 approach: Plant-level data → Unit-level extrapolation")

        # CRITICAL FIX: Normalize plant name for better matching
        # Remove common suffixes that might cause mismatches
        normalized_plant_name = plant_name
        suffixes_to_remove = [' Power Plant', ' Generating Plant', ' Power Station', ' Generating Station', ' Energy Center', ' Center', ' Power Complex', ' Generating Complex', ' Power Facility', ' Generating Facility', ' Power Project', ' Generating Project', ' Power Site', ' Generating Site', ' Power Area', ' Generating Area', ' Electric Plant', ' Plant', ' Station', ' Generation Complex', ' Steam Station', ' Steam Electric Plant', ' Energy Complex', ' Electric Plant', ' Complex', ' Fossil Plant']
        for suffix in suffixes_to_remove:
            if normalized_plant_name.endswith(suffix):
                normalized_plant_name = normalized_plant_name[:-len(suffix)]
                break

        logger.info(f"🔧 Normalized plant name: '{plant_name}' → '{normalized_plant_name}'")

        coal_sheets = ['Coal 2024', 'Coal 2023', 'Coal 2022', 'Coal 2021', 'Coal 2020']
        plant_data = {}

        for sheet_name in coal_sheets:
            try:
                year = sheet_name.split(' ')[1]
                df = pd.read_excel(self.usa_details_path, sheet_name=sheet_name)

                # IMPROVED: Multi-strategy plant name matching
                plant_records = pd.DataFrame()

                # Strategy 1: Exact match with normalized name
                exact_mask = df['Plant Name'].str.contains(f'^{normalized_plant_name}$', na=False, case=False, regex=True)
                plant_records = df[exact_mask]

                # Strategy 2: Contains match with normalized name
                if plant_records.empty:
                    contains_mask = df['Plant Name'].str.contains(normalized_plant_name, na=False, case=False, regex=False)
                    plant_records = df[contains_mask]

                # Strategy 3: Fuzzy match (remove periods, spaces)
                if plant_records.empty:
                    clean_input = normalized_plant_name.replace('.', '').replace(' ', '').lower()
                    df_clean = df['Plant Name'].str.replace('.', '').str.replace(' ', '').str.lower()
                    fuzzy_mask = df_clean.str.contains(clean_input, na=False)
                    plant_records = df[fuzzy_mask]

                if not plant_records.empty:
                    actual_plant_name = plant_records.iloc[0]['Plant Name']
                    logger.info(f"  📅 {year}: Found {len(plant_records)} records for '{actual_plant_name}'")
                else:
                    logger.warning(f"  📅 {year}: No records found for '{normalized_plant_name}'")

                if not plant_records.empty:
                    logger.info(f"  📅 {year}: Found {len(plant_records)} records")

                    # CASE 1: Aggregate all fuel types to get plant-level totals
                    total_net_generation = 0
                    total_fuel_consumption = 0
                    fuel_types = []
                    emission_factors = []

                    for idx, record in plant_records.iterrows():
                        net_gen = record.get('Net Generation (Megawatthours)', 0)
                        fuel_cons = record.get('Electric Fuel Consumption Quantity', 0)
                        coal_type = record.get('Type', 'Unknown')
                        emission_factor = record.get('Emission Factor', 0)

                        total_net_generation += net_gen
                        total_fuel_consumption += fuel_cons

                        if net_gen > 0:  # Only include non-zero records
                            fuel_types.append(coal_type)
                            emission_factors.append(emission_factor)

                        logger.info(f"    Record: {net_gen:,.0f} MWh ({coal_type})")

                    logger.info(f"    🔧 Plant Total: {total_net_generation:,.0f} MWh")

                    # Store aggregated plant-level data
                    plant_data[year] = {
                        'net_generation_mwh': total_net_generation,
                        'fuel_consumption': total_fuel_consumption,
                        'fuel_types': fuel_types,
                        'emission_factors': emission_factors,
                        'primary_coal_type': fuel_types[0] if fuel_types else 'Unknown',
                        'weighted_emission_factor': sum(emission_factors) / len(emission_factors) if emission_factors else 0
                    }

            except Exception as e:
                logger.warning(f"⚠️ Error processing {sheet_name}: {e}")

        logger.info(f"✅ Extracted aggregated plant data for {len(plant_data)} years")
        return plant_data
    
    def calculate_plf_case1_plant_level(self, plant_data: Dict[str, Any], plant_capacity_mw: float, plant_name: str = None) -> Dict[str, Any]:
        """
        PLF Case 1: Plant Level PLF Calculation
        Formula: PLF_plant_level = (Gross_Plant_Level_Generation)/(Plant_Capacity_MW * 8760 *PAF_plant_level)
        """
        """
        Calculate PLF using Case 1: Plant Level
        Formula: PLF_plant_level = (Gross_Plant_Level_Generation) / (Plant_Capacity_MW * 8760 * PAF_plant_level)

        Args:
            plant_data: Aggregated plant data extracted from USA Excel
            plant_capacity_mw: Total plant capacity in MW

        Returns:
            Dictionary with PLF calculations
        """
        logger.info("🔧 Calculating PLF using Case 1 (Plant Level)")
        logger.info(f"   Plant Capacity: {plant_capacity_mw} MW")

        plf_results = {}
        # FIXED: Use capacity-based auxiliary power calculation
        auxiliary_power_percent = self.get_auxiliary_power_percentage(plant_capacity_mw, 'subcritical')
        paf_plant_level = 1.0  # Plant Availability Factor (assume 1.0 if not available)

        for year, year_data in plant_data.items():
            # Get aggregated plant-level net generation
            total_net_generation_mwh = year_data.get('net_generation_mwh', 0)

            if total_net_generation_mwh > 0:
                # Step 1: Convert Net to Gross using Case 4a
                # Gross = Net / (1 - auxiliary_power_percent)
                gross_plant_generation_mwh = total_net_generation_mwh / (1 - auxiliary_power_percent)

                # Step 2: Calculate year-specific plant capacity (accounts for retired units)
                year_int = int(year) if isinstance(year, str) else year
                year_specific_capacity = self._calculate_year_specific_plant_capacity(plant_name, year_int) if plant_name else plant_capacity_mw

                # Step 3: Calculate Plant PLF using Case 1 formula with year-specific capacity
                # PLF_plant_level = (Gross_Plant_Level_Generation) / (Year_Specific_Plant_Capacity_MW * 8760 * PAF_plant_level)
                max_generation_mwh = year_specific_capacity * 8760 * paf_plant_level
                plf_plant_level = gross_plant_generation_mwh / max_generation_mwh

                # Cap PLF at 100%
                plf_plant_level = min(plf_plant_level, 1.0)

                plf_results[year] = {
                    'plf': plf_plant_level,
                    'net_generation_mwh': total_net_generation_mwh,
                    'gross_generation_mwh': gross_plant_generation_mwh,
                    'max_generation_mwh': max_generation_mwh,
                    'auxiliary_power_percent': auxiliary_power_percent,
                    'paf_plant_level': paf_plant_level,
                    'year_specific_capacity_mw': year_specific_capacity
                }

                logger.info(f"  📅 {year}: PLF = {plf_plant_level:.1%} ({plf_plant_level*100:.1f}%) [Capacity: {year_specific_capacity:.0f} MW]")
                logger.info(f"       Net: {total_net_generation_mwh:,.0f} MWh → Gross: {gross_plant_generation_mwh:,.0f} MWh")
        
        return plf_results
    
    def calculate_unit_level_from_plant_plf(self, plant_plf_results: Dict[str, Any], unit_capacity_mw: float, unit_number: str) -> Dict[str, Any]:
        """
        Extrapolate unit-level data from plant-level PLF using Case 1 methodology
        Formula: Generation_unit_level = Unit_capacity_MW * PLF_plant_level * PAF_unit_level

        Args:
            plant_plf_results: Plant-level PLF calculation results
            unit_capacity_mw: Unit capacity in MW
            unit_number: Unit number (for identification)

        Returns:
            Dictionary with unit-level calculations
        """
        logger.info(f"🔧 Extrapolating unit-level data for Unit {unit_number} using Case 1")
        logger.info(f"   Unit Capacity: {unit_capacity_mw} MW")

        unit_results = {}
        paf_unit_level = 1.0  # Unit Availability Factor (assume 1.0 if not available)

        for year, plant_data in plant_plf_results.items():
            plant_plf = plant_data.get('plf', 0)
            auxiliary_power_percent = plant_data.get('auxiliary_power_percent', 0.07)

            if plant_plf > 0:
                # Step 3: Use PLF_plant_level to extrapolate unit-level generation
                # Generation_unit_level = Unit_capacity_MW * PLF_plant_level * PAF_unit_level

                # 🚨 DEBUG: Check variable types to prevent multiplication error
                logger.info(f"🔍 DEBUG: unit_capacity_mw = {unit_capacity_mw} (type: {type(unit_capacity_mw)})")
                logger.info(f"🔍 DEBUG: plant_plf = {plant_plf} (type: {type(plant_plf)})")
                logger.info(f"🔍 DEBUG: paf_unit_level = {paf_unit_level} (type: {type(paf_unit_level)})")

                # Ensure all variables are numeric (not lists/sequences)
                if isinstance(unit_capacity_mw, (list, tuple)):
                    unit_capacity_mw = float(unit_capacity_mw[0]) if unit_capacity_mw else None
                    logger.warning(f"⚠️ unit_capacity_mw was a sequence, using first value: {unit_capacity_mw}")
                    if not unit_capacity_mw:
                        logger.error(f"❌ No valid unit capacity found")
                        return {}

                if isinstance(plant_plf, (list, tuple)):
                    plant_plf = float(plant_plf[0]) if plant_plf else 0.5
                    logger.warning(f"⚠️ plant_plf was a sequence, using first value: {plant_plf}")

                if isinstance(paf_unit_level, (list, tuple)):
                    paf_unit_level = float(paf_unit_level[0]) if paf_unit_level else 1.0
                    logger.warning(f"⚠️ paf_unit_level was a sequence, using first value: {paf_unit_level}")

                # Convert to float to ensure numeric types
                unit_capacity_mw = float(unit_capacity_mw)
                plant_plf = float(plant_plf)
                paf_unit_level = float(paf_unit_level)

                unit_generation_mwh = unit_capacity_mw * plant_plf * paf_unit_level * 8760

                # Calculate unit gross generation (same PLF as plant)
                unit_gross_generation_mwh = unit_generation_mwh

                # Calculate unit net generation
                unit_net_generation_mwh = unit_gross_generation_mwh * (1 - auxiliary_power_percent)

                # Calculate unit PLF (same as plant PLF in Case 1)
                unit_plf = plant_plf

                unit_results[year] = {
                    'plf': unit_plf,
                    'net_generation_mwh': unit_net_generation_mwh,
                    'gross_generation_mwh': unit_gross_generation_mwh,
                    'auxiliary_power_percent': auxiliary_power_percent,
                    'paf_unit_level': paf_unit_level,
                    'plant_plf_used': plant_plf
                }

                logger.info(f"  📅 {year}: Unit PLF = {unit_plf:.1%} (from plant PLF)")
                logger.info(f"       Unit Net: {unit_net_generation_mwh:,.0f} MWh, Gross: {unit_gross_generation_mwh:,.0f} MWh")

        return unit_results

    def calculate_plf_case2_unit_level(self, unit_generation_mwh: float, unit_capacity_mw: float, paf_unit: float = 1.0) -> Dict[str, Any]:
        """
        PLF Case 2: Unit Level PLF Calculation (Direct)
        Formula: PLF_unit_level = (Gross_Unit_Level_Generation)/(Unit_Capacity_MW * 8760 *PAF_unit_level)
        """
        logger.info(f"🔧 Calculating PLF using Case 2: Unit Level Direct")

        try:
            # Calculate unit PLF directly
            plf_unit = unit_generation_mwh / (unit_capacity_mw * 8760 * paf_unit)

            logger.info(f"✅ PLF Case 2 calculated: Unit PLF={plf_unit:.3f}")

            return {
                'plf': plf_unit,
                'gross_generation_mwh': unit_generation_mwh,
                'calculation_method': 'PLF Case 2: Unit Level Direct'
            }

        except Exception as e:
            logger.error(f"❌ PLF Case 2 calculation failed: {e}")
            return {}

    def calculate_plf_case3_unit_from_plant(self, plant_gross_generation_mwh: float, unit_capacity_mw: float, plant_capacity_mw: float) -> Dict[str, Any]:
        """
        PLF Case 3: Unit Generation from Plant Generation
        Formula: Gross_Unit_Level_Generation = (Gross_Plant_Level_Generation)*(Unit_Level_Nameplate_Capacity/Plant_Level_Nameplate_Capacity)
        """
        logger.info(f"🔧 Calculating PLF using Case 3: Unit from Plant Generation")

        try:
            # Calculate unit generation from plant generation
            unit_gross_generation_mwh = plant_gross_generation_mwh * (unit_capacity_mw / plant_capacity_mw)

            # Calculate unit PLF
            paf_unit = 1.0  # Assume 1.0 if not available
            plf_unit = unit_gross_generation_mwh / (unit_capacity_mw * 8760 * paf_unit)

            logger.info(f"✅ PLF Case 3 calculated: Unit PLF={plf_unit:.3f}")

            return {
                'plf': plf_unit,
                'gross_generation_mwh': unit_gross_generation_mwh,
                'calculation_method': 'PLF Case 3: Unit from Plant Generation'
            }

        except Exception as e:
            logger.error(f"❌ PLF Case 3 calculation failed: {e}")
            return {}

    def calculate_plf_case4_net_to_gross(self, net_generation_mwh: float, auxiliary_power_percent: float) -> Dict[str, Any]:
        """
        PLF Case 4: Convert Net to Gross Generation
        Formula: Gross_Generation = Net_Generation/(1-AUX)
        """
        logger.info(f"🔧 Calculating PLF using Case 4: Net to Gross Conversion")

        try:
            # Convert net to gross generation
            gross_generation_mwh = net_generation_mwh / (1 - auxiliary_power_percent)

            logger.info(f"✅ PLF Case 4 calculated: Gross Generation={gross_generation_mwh:.0f} MWh")

            return {
                'gross_generation_mwh': gross_generation_mwh,
                'net_generation_mwh': net_generation_mwh,
                'auxiliary_power_percent': auxiliary_power_percent,
                'calculation_method': 'PLF Case 4: Net to Gross Conversion'
            }

        except Exception as e:
            logger.error(f"❌ PLF Case 4 calculation failed: {e}")
            return {}

    def determine_plf_case(self, plant_data: Dict[str, Any], unit_data: Dict[str, Any]) -> str:
        """
        Determine which PLF case to use based on available data
        """
        # Check what data is available
        has_plant_generation = bool(plant_data)
        has_unit_generation = unit_data.get('unit_generation_mwh') is not None
        has_net_generation = unit_data.get('net_generation_mwh') is not None

        logger.info(f"🔍 Determining PLF case:")
        logger.info(f"   Plant data available: {has_plant_generation}")
        logger.info(f"   Unit generation available: {has_unit_generation}")
        logger.info(f"   Net generation available: {has_net_generation}")

        # Decision logic based on available data
        if has_unit_generation:
            return "case2"  # Direct unit calculation
        elif has_plant_generation and not has_unit_generation:
            return "case1"  # Plant level → Unit level extrapolation
        elif has_net_generation:
            return "case4"  # Net to Gross conversion
        else:
            return "case1"  # Default to Case 1

    def calculate_heat_rate(self, efficiency: float) -> float:
        """
        Calculate heat rate from efficiency
        Formula from Heat Rate & Efficiency sheet: heat_rate = 860.42/plant_efficiency
        """
        if efficiency <= 0:
            return 0
        return 860.42 / efficiency

    def calculate_heat_rate_from_fuel_data(self, fuel_consumed_tons: float, gcv_kcal_per_kg: float, gross_generation_mwh: float) -> float:
        """
        Calculate heat rate from fuel consumption data
        Formula: heat_rate = (fuel_consumed_tons * GCV_kCal_per_kg) / (Gross_Plant_Level_Generation_MWh)
        """
        if gross_generation_mwh <= 0:
            return 0
        return (fuel_consumed_tons * gcv_kcal_per_kg) / gross_generation_mwh

    def calculate_efficiency_from_fuel_data(self, gross_generation_mwh: float, gcv_kcal_per_kg: float, fuel_consumed_tons: float) -> float:
        """
        Calculate efficiency from fuel consumption data
        Formula: efficiency = (Gross_Plant_Level_Generation_MWh * 860,420) / (GCV_coal_type_kCal_per_kg * Fuel_Consumed_tons *1000)
        """
        if fuel_consumed_tons <= 0 or gcv_kcal_per_kg <= 0:
            return 0
        return (gross_generation_mwh * 860420) / (gcv_kcal_per_kg * fuel_consumed_tons * 1000)

    def calculate_fuel_consumption_from_emissions(self, annual_emission_mt: float, emission_factor_kg_per_kg: float) -> float:
        """
        Calculate fuel consumption from emissions
        Formula: plant total coal consumption = Annual Plant emission (million kg CO2) / emission factor of coal
        """
        if emission_factor_kg_per_kg <= 0:
            return 0
        annual_emission_million_kg = annual_emission_mt * 1000  # Convert Mt to million kg
        return annual_emission_million_kg / emission_factor_kg_per_kg

    def calculate_emission_factor(self, annual_emission_mt: float, total_generation_mwh: float) -> float:
        """
        Calculate emission factor
        Formula: Emission factor = Annual Plant emission (million kg CO2) / total annual electricity generated (million units)
        """
        if total_generation_mwh <= 0:
            return 0
        annual_emission_million_kg = annual_emission_mt * 1000  # Convert Mt to million kg
        total_generation_million_kwh = total_generation_mwh * 1000  # Convert MWh to million kWh
        return annual_emission_million_kg / total_generation_million_kwh

    def calculate_heat_from_coal(self, coal_quantity_kg: float, gcv_kcal_per_kg: float) -> float:
        """
        Calculate heat produced from coal
        Formula: heat produced by total coal = plant coal consumption * heat produced by 1 kg coal (kWh/kg)
        """
        # Convert kcal/kg to kWh/kg: 1 kcal = 4.184 kJ, 1 kWh = 3600 kJ
        heat_kwh_per_kg = gcv_kcal_per_kg * 4.184 / 3600
        return coal_quantity_kg * heat_kwh_per_kg

    def calculate_electricity_from_heat(self, heat_kwh: float, efficiency: float) -> float:
        """
        Calculate electricity generated from heat
        Formula: total electricity generated = heat produced by total coal * efficiency
        """
        return heat_kwh * efficiency

    def get_auxiliary_power_percent(self, technology: str, capacity_mw: float) -> float:
        """
        Get auxiliary power percentage based on technology and capacity
        🚨 COMPLETELY FIXED: Based on Assumptions sheet Unit Level Auxiliary Power Consumption matrix
        """
        # Use the new auxiliary power method with capacity-based matrix
        return self.get_auxiliary_power_percentage(capacity_mw, technology)

    def _calculate_plant_capacity_from_excel(self, plant_name: str) -> float:
        """
        Calculate total plant capacity by reading from USA Details sheet

        Args:
            plant_name: Name of the plant

        Returns:
            Total plant capacity in MW
        """
        try:
            import pandas as pd

            # Read USA Details sheet for capacity information
            usa_details_df = pd.read_excel(self.usa_details_path, sheet_name='USA Details')

            # Find all records for this plant
            plant_records = usa_details_df[usa_details_df['Plant Name'].str.contains(plant_name, case=False, na=False)]

            if plant_records.empty:
                logger.warning(f"⚠️ No plant records found in USA Details for {plant_name}")
                return 0.0

            # Sum all unit capacities for this plant
            total_capacity = plant_records['Capacity'].sum()

            logger.info(f"✅ Calculated plant capacity from USA Details: {total_capacity} MW")
            logger.info(f"   Found {len(plant_records)} units for {plant_name}")

            return float(total_capacity)

        except Exception as e:
            logger.error(f"❌ Error calculating plant capacity: {e}")
            return 0.0

    def _calculate_year_specific_plant_capacity(self, plant_name: str, year: int) -> float:
        """
        Calculate plant capacity for a specific year, accounting for units that were operational in that year

        Args:
            plant_name: Name of the plant
            year: Year for which to calculate capacity

        Returns:
            Total plant capacity in MW for that year
        """
        try:
            # Get all units for this plant
            units = self.get_plant_units_from_usa_details(plant_name)

            if not units:
                logger.warning(f"⚠️ No units found for {plant_name}, using current plant capacity")
                return self._calculate_plant_capacity_from_excel(plant_name)

            year_specific_capacity = 0.0
            operational_units = 0

            for unit in units:
                unit_capacity = unit.get('capacity_mw', 0)
                operating_year = unit.get('operating_year')
                planned_retirement_year = unit.get('planned_retirement_year')
                unit_id = unit.get('unit_id', 'Unknown')

                # 🔍 DEBUG: Log unit details for troubleshooting
                logger.info(f"🔍 DEBUG Unit {unit_id}: capacity={unit_capacity} MW, operating_year={operating_year}, planned_retirement_year={planned_retirement_year}")

                # Determine if unit was operational in the given year
                unit_operational = False

                if operating_year is not None:
                    try:
                        operating_year_int = int(operating_year)

                        # 🚨 CRITICAL FIX: Check planned retirement year first
                        if planned_retirement_year is not None and not pd.isna(planned_retirement_year):
                            try:
                                retirement_year = int(planned_retirement_year)
                                unit_operational = operating_year_int <= year < retirement_year
                                logger.info(f"🔍 DEBUG Unit {unit_id}: Using planned retirement year {retirement_year}, operational={unit_operational}")
                            except (ValueError, TypeError):
                                # If planned retirement year is invalid, assume unit is still operational
                                unit_operational = year >= operating_year_int
                                logger.info(f"🔍 DEBUG Unit {unit_id}: Invalid planned retirement year '{planned_retirement_year}', assuming operational if after operating year")
                        else:
                            # No planned retirement year - assume unit is still operational if after operating year
                            unit_operational = year >= operating_year_int
                            logger.info(f"🔍 DEBUG Unit {unit_id}: No planned retirement year, assuming operational since {operating_year_int}")

                    except (ValueError, TypeError):
                        # If operating year is invalid, assume unit is operational
                        unit_operational = True
                        logger.info(f"🔍 DEBUG Unit {unit_id}: Invalid operating year '{operating_year}', assuming operational")
                else:
                    # If no operating year data, assume unit is operational
                    unit_operational = True
                    logger.info(f"🔍 DEBUG Unit {unit_id}: No operating year data, assuming operational")

                if unit_operational:
                    year_specific_capacity += unit_capacity
                    operational_units += 1
                    logger.info(f"   Unit {unit_id}: {unit_capacity} MW (operational in {year})")
                else:
                    logger.info(f"   Unit {unit_id}: {unit_capacity} MW (NOT operational in {year})")

            logger.info(f"✅ Year {year} plant capacity for {plant_name}: {year_specific_capacity} MW ({operational_units} units operational)")
            return float(year_specific_capacity)

        except Exception as e:
            logger.error(f"❌ Error calculating year-specific plant capacity: {e}")
            # Fallback to current plant capacity
            return self._calculate_plant_capacity_from_excel(plant_name)

    def get_gcv_for_coal_type(self, coal_type: str) -> float:
        """
        Get GCV (Gross Calorific Value) for coal type from Assumptions sheet

        Args:
            coal_type: Type of coal (bituminous, sub-bituminous, lignite, etc.)

        Returns:
            GCV in kcal/kg
        """
        # 🚨 FIXED: GCV values from Assumptions sheet (kcal/kg)
        gcv_values = {
            'bituminous': 6690,      # ✅ Correct from Assumptions sheet
            'sub-bituminous': 4900,  # 🚨 FIXED: Was 6690, now correct 4900
            'lignite': 3350,         # ✅ Correct from Assumptions sheet
            'anthracite': 7000       # Estimated (not in Assumptions sheet)
        }

        # Normalize coal type
        coal_normalized = coal_type.lower().replace('-', '').replace('_', '').replace(' ', '')
        coal_mapping = {
            'bituminous': 'bituminous',
            'subbituminous': 'sub-bituminous',
            'lignite': 'lignite',
            'anthracite': 'anthracite'
        }

        coal_key = coal_mapping.get(coal_normalized, 'bituminous')
        gcv = gcv_values.get(coal_key, 6690)  # Default to bituminous

        logger.info(f"GCV for {coal_type}: {gcv} kcal/kg")
        return gcv

    def calculate_unit_parameters_usa(self, plant_name: str, unit_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate all unit parameters using real USA Excel data
        
        Args:
            plant_name: Name of the plant
            unit_data: Unit data including capacity, technology, etc.
            
        Returns:
            Dictionary with calculated parameters
        """
        logger.info(f"🔧 Calculating USA unit parameters for: {plant_name}")
        
        # Extract real data from USA Excel
        plant_excel_data = self.extract_plant_data_from_usa_excel(plant_name)
        
        if not plant_excel_data:
            logger.warning(f"⚠️ No USA Excel data found for {plant_name}")
            return {}
        
        # Get unit parameters from actual unit data (NO HARDCODED VALUES)
        unit_capacity_mw = unit_data.get('capacity')
        unit_number = unit_data.get('unit_number', '1')
        technology = unit_data.get('technology', 'subcritical').lower()

        # Validate unit capacity
        if not unit_capacity_mw:
            logger.error(f"❌ No unit capacity found in unit_data for {plant_name}")
            return {}

        # Calculate plant capacity by reading from USA Details sheet
        plant_capacity_mw = self._calculate_plant_capacity_from_excel(plant_name)

        logger.info(f"   Unit {unit_number}: {unit_capacity_mw} MW")
        logger.info(f"   Plant Total: {plant_capacity_mw} MW")

        # Step 1: Determine which PLF case to use
        plf_case = self.determine_plf_case(plant_excel_data, unit_data)
        logger.info(f"🎯 Selected PLF calculation method: {plf_case.upper()}")

        # Step 2: Calculate PLF based on determined case
        if plf_case == "case1":
            # Case 1: Plant Level → Unit Level extrapolation
            plant_plf_results = self.calculate_plf_case1_plant_level(plant_excel_data, plant_capacity_mw, plant_name)
            if not plant_plf_results:
                logger.warning(f"⚠️ No plant PLF results calculated for {plant_name}")
                return {}
            unit_plf_results = self.calculate_unit_level_from_plant_plf(plant_plf_results, unit_capacity_mw, unit_number)

        elif plf_case == "case2":
            # Case 2: Direct unit calculation
            unit_generation_mwh = unit_data.get('unit_generation_mwh', 0)
            unit_plf_result = self.calculate_plf_case2_unit_level(unit_generation_mwh, unit_capacity_mw)
            if not unit_plf_result:
                logger.warning(f"⚠️ Case 2 calculation failed, falling back to Case 1")
                plant_plf_results = self.calculate_plf_case1_plant_level(plant_excel_data, plant_capacity_mw, plant_name)
                unit_plf_results = self.calculate_unit_level_from_plant_plf(plant_plf_results, unit_capacity_mw, unit_number)
            else:
                # Convert single result to time series format
                unit_plf_results = {str(year): unit_plf_result for year in [2024, 2023, 2022, 2021, 2020]}

        elif plf_case == "case4":
            # Case 4: Net to Gross conversion, then apply Case 1 or Case 2
            net_generation_mwh = unit_data.get('net_generation_mwh', 0)
            auxiliary_power_percent = self.get_auxiliary_power_percentage(plant_capacity_mw, technology)
            case4_result = self.calculate_plf_case4_net_to_gross(net_generation_mwh, auxiliary_power_percent)

            if case4_result:
                # Use the gross generation for further calculation
                gross_generation_mwh = case4_result['gross_generation_mwh']
                unit_plf_result = self.calculate_plf_case2_unit_level(gross_generation_mwh, unit_capacity_mw)
                unit_plf_results = {str(year): unit_plf_result for year in [2024, 2023, 2022, 2021, 2020]}
            else:
                logger.warning(f"⚠️ Case 4 calculation failed, falling back to Case 1")
                plant_plf_results = self.calculate_plf_case1_plant_level(plant_excel_data, plant_capacity_mw, plant_name)
                unit_plf_results = self.calculate_unit_level_from_plant_plf(plant_plf_results, unit_capacity_mw, unit_number)

        else:
            # Default fallback to Case 1
            logger.warning(f"⚠️ Unknown PLF case '{plf_case}', using Case 1")
            plant_plf_results = self.calculate_plf_case1_plant_level(plant_excel_data, plant_capacity_mw, plant_name)
            unit_plf_results = self.calculate_unit_level_from_plant_plf(plant_plf_results, unit_capacity_mw, unit_number)

        if not unit_plf_results:
            logger.warning(f"⚠️ No unit PLF results calculated for {plant_name} Unit {unit_number}")
            return {}
        
        # FIXED: Get auxiliary power percentage based on PLANT capacity and technology (not unit capacity)
        auxiliary_power_percent = self.get_auxiliary_power_percentage(plant_capacity_mw, technology)

        # Prepare time series data using unit-level results
        time_series_plf = {}
        time_series_auxiliary_power = {}
        time_series_gross_generation = {}

        for year, year_result in unit_plf_results.items():
            year_int = int(year)
            time_series_plf[year_int] = year_result['plf']
            time_series_auxiliary_power[year_int] = auxiliary_power_percent
            time_series_gross_generation[year_int] = year_result['gross_generation_mwh']
        
        # Calculate average values
        avg_plf = sum(time_series_plf.values()) / len(time_series_plf) if time_series_plf else 0
        
        # Convert time series to array format expected by unit extraction system
        plf_array = []
        auxiliary_power_array = []
        gross_generation_array = []

        for year, plf_value in time_series_plf.items():
            plf_array.append({"value": plf_value, "year": year})

        for year, aux_value in time_series_auxiliary_power.items():
            # Keep as decimal (0.08) for calculations
            auxiliary_power_array.append({"value": aux_value, "year": year})

        for year, gross_value in time_series_gross_generation.items():
            # Convert MWh to GWh for gross generation
            gross_value_gwh = gross_value / 1000
            gross_generation_array.append({"value": gross_value_gwh, "year": year})

        # Get heat rate and efficiency from Excel data if available
        heat_rate_kcal_per_kwh = None
        efficiency = None

        # Check if we have Excel data with calculated heat rate and efficiency
        if plant_excel_data:
            logger.info(f"🔍 Checking plant_excel_data for heat rate and efficiency...")
            logger.info(f"   plant_excel_data keys: {list(plant_excel_data.keys())}")

            # Look for the first year's data to get heat rate and efficiency
            for year, year_data in plant_excel_data.items():
                logger.info(f"   Checking year {year}, data type: {type(year_data)}")

                # Handle both dict and list formats
                if isinstance(year_data, dict):
                    # Direct dict format
                    record = year_data
                    logger.info(f"   Dict record keys: {list(record.keys())}")
                elif isinstance(year_data, list) and len(year_data) > 0:
                    # List format
                    record = year_data[0]
                    logger.info(f"   List record keys: {list(record.keys())}")
                else:
                    continue

                if 'heat_rate_kcal_per_kwh' in record:
                    heat_rate_kcal_per_kwh = record['heat_rate_kcal_per_kwh']
                    logger.info(f"   Found heat_rate_kcal_per_kwh: {heat_rate_kcal_per_kwh}")
                if 'coal_unit_efficiency' in record:
                    efficiency = record['coal_unit_efficiency']
                    logger.info(f"   Found coal_unit_efficiency: {efficiency}")

                # If we found both, break
                if heat_rate_kcal_per_kwh is not None and efficiency is not None:
                    break
        else:
            logger.warning(f"⚠️ No plant_excel_data available for heat rate/efficiency lookup")

        results = {
            'plf_unit': avg_plf,
            'time_series_plf': time_series_plf,
            'auxiliary_power_percent': auxiliary_power_percent,
            'time_series_auxiliary_power': time_series_auxiliary_power,
            'time_series_gross_generation': time_series_gross_generation,

            # Array format for unit extraction system
            'plf': plf_array,
            'auxiliary_power_consumed': auxiliary_power_array,
            'gross_power_generation': gross_generation_array,

            'calculation_method': 'USA Excel Data - PLF Case 1 (Plant Level → Unit Level)',
            'data_source': 'USA Details.xlsx Coal yearly sheets',
            'years_with_data': list(unit_plf_results.keys()),
            'calculation_summary': {
                'methods_used': ['USA Excel Data Extraction', 'PLF Case 1: Plant Level → Unit Level', 'Auxiliary Power Matrix'],
                'data_years': list(unit_plf_results.keys()),
                'avg_plf': avg_plf,
                'technology': technology
            }
        }

        # Add heat rate and efficiency if available from Excel calculations
        if heat_rate_kcal_per_kwh is not None:
            results['heat_rate_kcal_per_kwh'] = heat_rate_kcal_per_kwh
            logger.info(f"✅ Added heat rate from Excel data: {heat_rate_kcal_per_kwh} kCal/kWh")

        if efficiency is not None:
            results['efficiency'] = efficiency
            logger.info(f"✅ Added efficiency from Excel data: {efficiency:.4f} ({efficiency*100:.2f}%)")

        logger.info(f"✅ USA calculations completed:")
        logger.info(f"   Average PLF: {avg_plf:.1%}")
        logger.info(f"   Auxiliary Power: {auxiliary_power_percent:.1%}")
        logger.info(f"   Years with data: {list(unit_plf_results.keys())}")
        
        return results


def create_usa_excel_calculation_engine() -> USAExcelCalculationEngine:
    """Create and return a USA Excel calculation engine instance"""
    usa_details_path = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx'
    calculations_path = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/current state calculations.xlsx'

    return USAExcelCalculationEngine(usa_details_path, calculations_path)
