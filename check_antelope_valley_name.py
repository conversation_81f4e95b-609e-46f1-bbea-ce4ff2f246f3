#!/usr/bin/env python3
"""
Check the exact plant name for Antelope Valley in the Excel file
"""

import pandas as pd

def check_antelope_valley_name():
    """Check the exact plant name for Antelope Valley in USA Details.xlsx"""
    
    print("🔍 CHECKING ANTELOPE VALLEY PLANT NAME")
    print("=" * 50)
    
    try:
        excel_file = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx'
        
        # Check all coal sheets for Antelope Valley variations
        coal_sheets = ['Coal 2024', 'Coal 2023', 'Coal 2022', 'Coal 2021', 'Coal 2020']
        
        antelope_variations = set()
        
        for sheet_name in coal_sheets:
            print(f"\n📊 Checking {sheet_name}:")
            df = pd.read_excel(excel_file, sheet_name=sheet_name)
            
            # Search for any plant name containing "Antelope"
            antelope_mask = df['Plant Name'].str.contains('Antelope', na=False, case=False)
            antelope_plants = df[antelope_mask]['Plant Name'].unique()
            
            if len(antelope_plants) > 0:
                print(f"  ✅ Found {len(antelope_plants)} Antelope plants:")
                for plant in antelope_plants:
                    print(f"    • '{plant}'")
                    antelope_variations.add(plant)
            else:
                print(f"  ❌ No Antelope plants found")
        
        print(f"\n📋 ALL ANTELOPE VARIATIONS FOUND:")
        print("-" * 30)
        if antelope_variations:
            for variation in sorted(antelope_variations):
                print(f"  • '{variation}'")
        else:
            print("  ❌ No Antelope plants found in any sheet")
        
        # Also search for similar names
        print(f"\n🔍 SEARCHING FOR SIMILAR NAMES:")
        print("-" * 30)
        
        # Check Coal 2024 for all plant names containing "Valley"
        df_2024 = pd.read_excel(excel_file, sheet_name='Coal 2024')
        valley_plants = df_2024[df_2024['Plant Name'].str.contains('Valley', na=False, case=False)]['Plant Name'].unique()
        
        print(f"Plants with 'Valley' in name:")
        for plant in sorted(valley_plants)[:10]:  # Show first 10
            print(f"  • '{plant}'")
        
        if len(valley_plants) > 10:
            print(f"  ... and {len(valley_plants) - 10} more")
        
        # Check for plants starting with 'A'
        print(f"\nPlants starting with 'A':")
        a_plants = df_2024[df_2024['Plant Name'].str.startswith('A', na=False)]['Plant Name'].unique()
        for plant in sorted(a_plants)[:10]:  # Show first 10
            print(f"  • '{plant}'")
        
        if len(a_plants) > 10:
            print(f"  ... and {len(a_plants) - 10} more")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_antelope_valley_name()
