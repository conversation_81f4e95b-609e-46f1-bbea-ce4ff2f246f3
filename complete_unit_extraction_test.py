#!/usr/bin/env python3
"""
Complete Unit-Level Extraction Test
==================================

Performs COMPLETE unit-level extraction for:
1. Antelope Valley Station
2. Dry Fork Station  
3. Laramie River Station

Shows EVERY variable, assumption, and calculation step in detail.
Saves results to a comprehensive report file.
"""

import sys
import os
import json
from datetime import datetime
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')

from usa_excel_calculation_engine import USAExcelCalculationEngine
from excel_power_plant_tool import ExcelPowerPlantTool
import pandas as pd
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveUnitExtractor:
    """Performs complete unit extraction with detailed logging"""
    
    def __init__(self):
        self.usa_engine = USAExcelCalculationEngine(
            '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx',
            '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/current state calculations.xlsx'
        )
        self.excel_tool = ExcelPowerPlantTool(
            '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx', 
            'comprehensive_test'
        )
        self.results = {}
        
    def extract_complete_plant_data(self, plant_name: str) -> dict:
        """Extract complete data for a plant with detailed breakdown"""
        print(f"\n{'='*120}")
        print(f"🔍 COMPLETE UNIT EXTRACTION: {plant_name}")
        print(f"{'='*120}")
        
        plant_results = {
            "plant_name": plant_name,
            "extraction_timestamp": datetime.now().isoformat(),
            "steps": {},
            "units": {},
            "calculations": {},
            "assumptions": {},
            "errors": []
        }
        
        try:
            # STEP 1: Basic Plant Information
            print(f"\n📊 STEP 1: BASIC PLANT INFORMATION")
            print("-" * 80)
            
            step1_results = self._extract_basic_info(plant_name)
            plant_results["steps"]["step1_basic_info"] = step1_results
            
            # STEP 2: Unit Details from Excel Tool
            print(f"\n📊 STEP 2: UNIT DETAILS FROM EXCEL TOOL")
            print("-" * 80)
            
            step2_results = self._extract_unit_details(plant_name)
            plant_results["steps"]["step2_unit_details"] = step2_results
            
            # STEP 3: Excel Data Extraction
            print(f"\n📊 STEP 3: EXCEL DATA EXTRACTION")
            print("-" * 80)
            
            step3_results = self._extract_excel_data(plant_name)
            plant_results["steps"]["step3_excel_data"] = step3_results
            
            # STEP 4: Unit-Level Calculations
            print(f"\n📊 STEP 4: UNIT-LEVEL CALCULATIONS")
            print("-" * 80)
            
            if step2_results.get("units"):
                for unit_data in step2_results["units"]:
                    unit_id = unit_data.get("unit_id", "unknown")
                    print(f"\n🔧 CALCULATING FOR UNIT: {unit_id}")
                    print("-" * 60)
                    
                    unit_calc_results = self._perform_unit_calculations(
                        plant_name, unit_data, step3_results.get("excel_data", {})
                    )
                    plant_results["units"][unit_id] = unit_calc_results
            
            # STEP 5: Summary and Validation
            print(f"\n📊 STEP 5: SUMMARY AND VALIDATION")
            print("-" * 80)
            
            summary_results = self._create_summary(plant_results)
            plant_results["summary"] = summary_results
            
        except Exception as e:
            error_msg = f"Error extracting {plant_name}: {str(e)}"
            print(f"❌ {error_msg}")
            plant_results["errors"].append(error_msg)
            
        return plant_results
    
    def _extract_basic_info(self, plant_name: str) -> dict:
        """Extract basic plant information"""
        results = {}
        
        # Plant capacity from USA Details
        plant_capacity = self.usa_engine._calculate_plant_capacity_from_excel(plant_name)
        results["plant_capacity_mw"] = plant_capacity
        print(f"✅ Total Plant Capacity: {plant_capacity} MW")
        
        # Plant location and details from USA Details
        try:
            usa_details_df = pd.read_excel(
                '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx', 
                sheet_name='USA Details'
            )
            plant_records = usa_details_df[
                usa_details_df['Plant Name'].str.contains(plant_name, case=False, na=False)
            ]
            
            if not plant_records.empty:
                results["unit_count"] = len(plant_records)
                results["individual_units"] = []
                
                print(f"✅ Found {len(plant_records)} units in USA Details")
                
                for idx, record in plant_records.iterrows():
                    unit_info = {
                        "unit_id": record.get('Unit IDs', 'Unknown'),
                        "capacity_mw": record.get('Capacity', 0),
                        "plant_type": record.get('Plant Type', 'Unknown'),
                        "operating_year": record.get('Operating Year', 'Unknown'),
                        "retirement_year": record.get('Planned Retirement Year', 'Unknown')
                    }
                    results["individual_units"].append(unit_info)
                    print(f"   Unit {unit_info['unit_id']}: {unit_info['capacity_mw']} MW")
                    
        except Exception as e:
            results["error"] = f"Error reading USA Details: {str(e)}"
            print(f"❌ Error reading USA Details: {e}")
            
        return results
    
    def _extract_unit_details(self, plant_name: str) -> dict:
        """Extract detailed unit information using Excel tool"""
        results = {}
        
        try:
            unit_results = self.excel_tool.get_plant_data(plant_name)
            
            if unit_results:
                results["units"] = unit_results
                results["unit_count"] = len(unit_results)
                
                print(f"✅ Excel tool found {len(unit_results)} units")
                
                for i, unit in enumerate(unit_results, 1):
                    unit_id = unit.get('unit_id', f'Unit_{i}')
                    capacity = unit.get('capacity', 'N/A')
                    technology = unit.get('technology', 'N/A')
                    fuel_type = unit.get('fuel_type', 'N/A')
                    
                    print(f"   Unit {unit_id}:")
                    print(f"     Capacity: {capacity} MW")
                    print(f"     Technology: {technology}")
                    print(f"     Fuel Type: {fuel_type}")
                    
            else:
                results["error"] = "No unit data found by Excel tool"
                print("❌ Excel tool found no unit data")
                
        except Exception as e:
            results["error"] = f"Error using Excel tool: {str(e)}"
            print(f"❌ Error using Excel tool: {e}")
            
        return results
        
    def _extract_excel_data(self, plant_name: str) -> dict:
        """Extract Excel data for calculations"""
        results = {}
        
        try:
            # Get plant data from USA Excel engine
            excel_data = self.usa_engine.extract_plant_data_from_usa_excel(plant_name)
            
            if excel_data:
                results["excel_data"] = excel_data
                results["years_available"] = list(excel_data.keys())
                
                print(f"✅ Excel data available for years: {results['years_available']}")
                
                # Show sample data for 2024
                if '2024' in excel_data:
                    year_2024 = excel_data['2024']
                    if isinstance(year_2024, list) and year_2024:
                        sample_record = year_2024[0]
                        print(f"✅ Sample 2024 data fields:")
                        for key, value in list(sample_record.items())[:8]:
                            print(f"     {key}: {value}")
                            
                        # Extract key values
                        results["sample_2024_data"] = {
                            "net_generation_mwh": sample_record.get('Net Generation (Megawatthours)', 0),
                            "fuel_consumption": sample_record.get('Electric Fuel Consumption Quantity', 0),
                            "fuel_type": sample_record.get('Fuel', 'Unknown'),
                            "emission_factor": sample_record.get('Emission Factor', 0)
                        }
                        
                        print(f"✅ Key 2024 values:")
                        for key, value in results["sample_2024_data"].items():
                            print(f"     {key}: {value}")
                            
            else:
                results["error"] = "No Excel data found"
                print("❌ No Excel data found")
                
        except Exception as e:
            results["error"] = f"Error extracting Excel data: {str(e)}"
            print(f"❌ Error extracting Excel data: {e}")
            
        return results

    def _perform_unit_calculations(self, plant_name: str, unit_data: dict, excel_data: dict) -> dict:
        """Perform complete unit-level calculations with detailed breakdown"""
        calc_results = {
            "input_data": unit_data,
            "assumptions": {},
            "calculations": {},
            "final_values": {},
            "calculation_steps": []
        }

        try:
            # Extract unit parameters
            unit_capacity = unit_data.get('capacity', 0)
            unit_id = unit_data.get('unit_id', 'Unknown')
            technology = unit_data.get('technology', 'subcritical')
            fuel_type = unit_data.get('fuel_type', 'coal')

            print(f"🔧 Input Parameters:")
            print(f"   Unit ID: {unit_id}")
            print(f"   Capacity: {unit_capacity} MW")
            print(f"   Technology: {technology}")
            print(f"   Fuel Type: {fuel_type}")

            calc_results["input_data"] = {
                "unit_capacity_mw": unit_capacity,
                "unit_id": unit_id,
                "technology": technology,
                "fuel_type": fuel_type
            }

            # CALCULATION 1: Auxiliary Power
            print(f"\n🔧 CALCULATION 1: AUXILIARY POWER")
            aux_power_decimal = self.usa_engine.get_auxiliary_power_percentage(unit_capacity, technology)
            aux_power_percent = aux_power_decimal * 100

            print(f"   Formula: Based on capacity ({unit_capacity} MW) and technology ({technology})")
            print(f"   Result: {aux_power_percent:.1f}% (decimal: {aux_power_decimal})")

            calc_results["calculations"]["auxiliary_power"] = {
                "method": "Capacity and technology based lookup",
                "capacity_mw": unit_capacity,
                "technology": technology,
                "result_decimal": aux_power_decimal,
                "result_percent": aux_power_percent
            }

            # CALCULATION 2: GCV Values
            print(f"\n🔧 CALCULATION 2: GCV VALUES")

            # Determine coal type from fuel_type or Excel data
            coal_type = "bituminous"  # Default
            if isinstance(fuel_type, str) and fuel_type.lower() != 'coal':
                coal_type = fuel_type.lower()

            gcv_value = self.usa_engine.get_gcv_for_coal_type(coal_type)

            print(f"   Coal Type: {coal_type}")
            print(f"   GCV Value: {gcv_value} kcal/kg")

            calc_results["calculations"]["gcv"] = {
                "coal_type": coal_type,
                "gcv_kcal_per_kg": gcv_value,
                "source": "Hardcoded values from Assumptions sheet"
            }

            # CALCULATION 3: Net Generation from Excel
            print(f"\n🔧 CALCULATION 3: NET GENERATION FROM EXCEL")

            net_generation_by_year = {}
            if excel_data:
                for year, year_data in excel_data.items():
                    if isinstance(year_data, list):
                        total_net_gen = 0
                        for record in year_data:
                            net_gen = record.get('Net Generation (Megawatthours)', 0)
                            if isinstance(net_gen, (int, float)):
                                total_net_gen += net_gen
                        net_generation_by_year[year] = total_net_gen
                        print(f"   {year}: {total_net_gen:,.0f} MWh")

            calc_results["calculations"]["net_generation"] = {
                "source": "USA Details.xlsx Coal yearly sheets",
                "by_year": net_generation_by_year,
                "method": "Sum of all fuel type records for plant"
            }

            # CALCULATION 4: Gross Generation
            print(f"\n🔧 CALCULATION 4: GROSS GENERATION")

            gross_generation_by_year = {}
            for year, net_gen in net_generation_by_year.items():
                if net_gen > 0:
                    # Formula: Gross = Net / (1 - Aux%)
                    gross_gen = net_gen / (1 - aux_power_decimal)
                    gross_generation_by_year[year] = gross_gen
                    print(f"   {year}: Gross = {net_gen:,.0f} / (1 - {aux_power_decimal}) = {gross_gen:,.0f} MWh")

            calc_results["calculations"]["gross_generation"] = {
                "formula": "Gross = Net / (1 - Auxiliary_Power_Decimal)",
                "auxiliary_power_used": aux_power_decimal,
                "by_year": gross_generation_by_year
            }

            # CALCULATION 5: PLF (Plant Load Factor)
            print(f"\n🔧 CALCULATION 5: PLF (PLANT LOAD FACTOR)")

            plf_by_year = {}
            for year, gross_gen in gross_generation_by_year.items():
                if gross_gen > 0 and unit_capacity > 0:
                    # Formula: PLF = Gross_Generation / (Capacity * 8760)
                    max_generation = unit_capacity * 8760  # MWh
                    plf = gross_gen / max_generation
                    plf_percent = plf * 100
                    plf_by_year[year] = plf_percent
                    print(f"   {year}: PLF = {gross_gen:,.0f} / ({unit_capacity} * 8760) = {plf_percent:.1f}%")

            calc_results["calculations"]["plf"] = {
                "formula": "PLF = Gross_Generation / (Capacity_MW * 8760)",
                "capacity_mw": unit_capacity,
                "annual_hours": 8760,
                "by_year": plf_by_year
            }

            # CALCULATION 6: Fuel Percentages
            print(f"\n🔧 CALCULATION 6: FUEL PERCENTAGES")

            fuel_percentages = {}
            if excel_data and '2024' in excel_data:
                year_data = excel_data['2024']
                if isinstance(year_data, list):
                    total_generation = sum(
                        record.get('Net Generation (Megawatthours)', 0)
                        for record in year_data
                    )

                    for record in year_data:
                        fuel = record.get('Fuel', 'Unknown')
                        fuel_gen = record.get('Net Generation (Megawatthours)', 0)

                        if total_generation > 0:
                            percentage = (fuel_gen / total_generation) * 100
                        else:
                            percentage = 0

                        fuel_percentages[fuel] = percentage
                        print(f"   {fuel}: {percentage:.1f}% ({fuel_gen:,.0f} / {total_generation:,.0f} MWh)")

            calc_results["calculations"]["fuel_percentages"] = {
                "method": "Based on 2024 net generation by fuel type",
                "percentages": fuel_percentages
            }

            # Final summary values
            calc_results["final_values"] = {
                "unit_capacity_mw": unit_capacity,
                "auxiliary_power_percent": aux_power_percent,
                "gcv_kcal_per_kg": gcv_value,
                "latest_net_generation_mwh": net_generation_by_year.get('2024', 0),
                "latest_gross_generation_mwh": gross_generation_by_year.get('2024', 0),
                "latest_plf_percent": plf_by_year.get('2024', 0),
                "primary_fuel_type": coal_type
            }

            print(f"\n✅ Unit calculations completed for {unit_id}")

        except Exception as e:
            error_msg = f"Error in unit calculations: {str(e)}"
            print(f"❌ {error_msg}")
            calc_results["error"] = error_msg

        return calc_results

    def _create_summary(self, plant_results: dict) -> dict:
        """Create summary of all calculations"""
        summary = {
            "plant_name": plant_results["plant_name"],
            "total_units": len(plant_results.get("units", {})),
            "plant_capacity_mw": plant_results.get("steps", {}).get("step1_basic_info", {}).get("plant_capacity_mw", 0),
            "unit_summaries": {},
            "validation": {}
        }

        print(f"📋 SUMMARY FOR {plant_results['plant_name']}:")
        print(f"   Total Units: {summary['total_units']}")
        print(f"   Plant Capacity: {summary['plant_capacity_mw']} MW")

        # Summarize each unit
        for unit_id, unit_data in plant_results.get("units", {}).items():
            final_values = unit_data.get("final_values", {})
            summary["unit_summaries"][unit_id] = final_values

            print(f"\n   Unit {unit_id}:")
            print(f"     Capacity: {final_values.get('unit_capacity_mw', 0)} MW")
            print(f"     Aux Power: {final_values.get('auxiliary_power_percent', 0):.1f}%")
            print(f"     GCV: {final_values.get('gcv_kcal_per_kg', 0)} kcal/kg")
            print(f"     2024 Net Gen: {final_values.get('latest_net_generation_mwh', 0):,.0f} MWh")
            print(f"     2024 Gross Gen: {final_values.get('latest_gross_generation_mwh', 0):,.0f} MWh")
            print(f"     2024 PLF: {final_values.get('latest_plf_percent', 0):.1f}%")

        return summary

def main():
    """Run complete unit extraction test"""
    print("🚀 COMPLETE UNIT-LEVEL EXTRACTION TEST")
    print("=" * 120)
    print("Testing complete extraction for:")
    print("1. Antelope Valley Station")
    print("2. Dry Fork Station")
    print("3. Laramie River Station")
    print()
    print("Will extract and calculate:")
    print("- Basic plant information and capacities")
    print("- Unit details from Excel tool")
    print("- Excel data for calculations")
    print("- Complete unit-level calculations")
    print("- Summary and validation")
    
    extractor = ComprehensiveUnitExtractor()
    
    test_plants = [
        "Antelope Valley Station",
        "Dry Fork Station", 
        "Laramie River Station"
    ]
    
    all_results = {
        "test_info": {
            "timestamp": datetime.now().isoformat(),
            "test_type": "Complete Unit-Level Extraction",
            "plants_tested": test_plants
        },
        "plants": {}
    }
    
    for plant_name in test_plants:
        plant_results = extractor.extract_complete_plant_data(plant_name)
        all_results["plants"][plant_name] = plant_results
    
    # Save results to file
    output_file = "complete_unit_extraction_results.json"
    with open(output_file, 'w') as f:
        json.dump(all_results, f, indent=2, default=str)
    
    print(f"\n{'='*120}")
    print("🎯 COMPLETE EXTRACTION TEST SUMMARY")
    print(f"{'='*120}")
    
    for plant_name in test_plants:
        plant_data = all_results["plants"][plant_name]
        error_count = len(plant_data.get("errors", []))
        unit_count = len(plant_data.get("units", {}))
        
        if error_count == 0:
            print(f"✅ {plant_name}: {unit_count} units extracted successfully")
        else:
            print(f"⚠️ {plant_name}: {unit_count} units extracted with {error_count} errors")
    
    print(f"\n📄 Detailed results saved to: {output_file}")
    print("🔍 Review the JSON file for complete variable details and calculations")

if __name__ == "__main__":
    main()
