#!/usr/bin/env python3

import sys
import os
sys.path.append('.')

# Test non-USA plant calculations
sys.path.append('./backend/src/agent')
from excel_calculation_engine import create_excel_calculation_engine

def test_non_usa_calculations():
    """Test calculations for non-USA plants"""
    
    print("🔧 TESTING NON-USA PLANT CALCULATIONS")
    print("=" * 60)
    
    try:
        # Initialize the standard Excel calculation engine
        calc_engine = create_excel_calculation_engine('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/current state calculations.xlsx')
        print("✅ Standard Excel calculation engine initialized")
        
        # Test data for a hypothetical Indian coal plant
        unit_data = {
            'capacity': 500,
            'unit_number': '1',
            'technology': 'subcritical',
            'generation_mwh': 3000000,  # 3 TWh annual generation
            'country': 'India'
        }
        
        plant_context = {
            'plant_name': 'Test Indian Plant',
            'country': 'India',
            'plant_capacity_mw': 1000,
            'plant_generation_mwh': 6000000  # 6 TWh plant total
        }
        
        print(f"🔍 Testing calculations for: {plant_context['plant_name']}")
        print(f"📋 Unit: {unit_data['unit_number']}")
        print(f"📋 Capacity: {unit_data['capacity']} MW")
        print(f"📋 Technology: {unit_data['technology']}")
        print(f"📋 Country: {unit_data['country']}")
        
        # Run the standard calculations
        calc_results = calc_engine.calculate_unit_parameters(unit_data, plant_context)
        
        if calc_results:
            print(f"✅ Non-USA calculations successful!")
            
            # Check key fields
            key_fields = [
                'plf', 'auxiliary_power_consumed', 'gross_power_generation',
                'plf_unit', 'auxiliary_power_percent', 'calculation_method'
            ]
            
            print(f"\n📊 CALCULATION RESULTS:")
            print("-" * 40)
            
            for field in key_fields:
                value = calc_results.get(field, 'MISSING')
                if isinstance(value, list) and len(value) > 0:
                    # For time series data, show first value
                    first_value = value[0]
                    if isinstance(first_value, dict):
                        display_value = f"{first_value.get('value', 'N/A')} ({first_value.get('year', 'N/A')})"
                        print(f"  {field}: {display_value} [{len(value)} data points]")
                    else:
                        print(f"  {field}: {first_value} [{len(value)} items]")
                elif isinstance(value, (int, float)):
                    if 'plf' in field.lower() and value < 1:
                        print(f"  {field}: {value:.1%}")
                    elif 'auxiliary' in field.lower() and value < 1:
                        print(f"  {field}: {value:.1%}")
                    else:
                        print(f"  {field}: {value}")
                elif isinstance(value, str):
                    print(f"  {field}: {value}")
                else:
                    print(f"  {field}: {value}")
            
            print(f"\n🎯 OVERALL ASSESSMENT:")
            print("-" * 30)
            
            # Check if we have the critical fields
            missing_fields = []
            if not calc_results.get('plf'):
                missing_fields.append('PLF')
            if not calc_results.get('auxiliary_power_consumed'):
                missing_fields.append('Auxiliary Power')
            if not calc_results.get('gross_power_generation'):
                missing_fields.append('Gross Generation')
            
            if missing_fields:
                print(f"  ❌ Missing fields: {', '.join(missing_fields)}")
            else:
                print(f"  ✅ All critical fields present!")
                
            # Check calculation method
            calc_method = calc_results.get('calculation_method', '')
            if 'Excel' in calc_method:
                print(f"  ✅ Using Excel-based calculations")
            else:
                print(f"  📊 Calculation method: {calc_method}")
                
        else:
            print(f"❌ Non-USA calculations failed!")
            
    except Exception as e:
        print(f"❌ Error during calculations: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_non_usa_calculations()
