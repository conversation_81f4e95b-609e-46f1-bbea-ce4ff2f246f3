#!/usr/bin/env python3
"""
Test Specific Plants - Unit Level Extraction
============================================

Tests unit level extraction for:
1. <PERSON> Steam Plant
2. Sherburne County

Provides complete JSON outputs for each plant.
"""

import sys
import json
from datetime import datetime

# Add the main project directory to path
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')

def test_plant_extraction(plant_name):
    """Test unit level extraction for a specific plant"""
    print(f"\n🔥 TESTING UNIT LEVEL EXTRACTION: {plant_name}")
    print("=" * 80)
    
    try:
        from excel_power_plant_tool import ExcelPowerPlantTool
        
        # Initialize Excel tool
        excel_tool = ExcelPowerPlantTool(
            '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx', 
            'test_session'
        )
        
        print(f"🔍 Searching for plant: {plant_name}")
        
        # Get plant data
        results = excel_tool.get_plant_data(plant_name)
        
        if results:
            print(f"✅ Found {len(results)} units for {plant_name}")
            
            # Create complete JSON output
            plant_json = {
                "plant_name": plant_name,
                "extraction_timestamp": datetime.now().isoformat(),
                "total_units": len(results),
                "units": results
            }
            
            # Save to file
            filename = f"{plant_name.replace(' ', '_').lower()}_extraction.json"
            with open(filename, 'w') as f:
                json.dump(plant_json, f, indent=2, default=str)
            
            print(f"💾 Saved complete data to: {filename}")
            
            # Print summary
            print(f"\n📊 PLANT SUMMARY:")
            print(f"   Plant Name: {plant_name}")
            print(f"   Total Units: {len(results)}")
            
            total_capacity = 0
            for i, unit in enumerate(results, 1):
                capacity = unit.get('capacity', 0)
                unit_number = unit.get('unit_number', f'Unit {i}')
                commencement = unit.get('commencement_date', 'Unknown')[:10] if unit.get('commencement_date') else 'Unknown'
                retirement = unit.get('remaining_useful_life', 'Unknown')[:10] if unit.get('remaining_useful_life') else 'Unknown'
                
                print(f"   Unit {unit_number}: {capacity} MW ({commencement} to {retirement})")
                total_capacity += float(capacity) if capacity else 0
            
            print(f"   Total Plant Capacity: {total_capacity} MW")
            
            # Print key data fields for first unit
            if results:
                first_unit = results[0]
                print(f"\n🔧 KEY DATA FIELDS (Unit 1):")
                key_fields = [
                    'capacity', 'technology', 'commencement_date', 'remaining_useful_life',
                    'plf', 'auxiliary_power_consumed', 'gross_power_generation', 
                    'heat_rate', 'efficiency', 'fuel_type'
                ]
                
                for field in key_fields:
                    value = first_unit.get(field)
                    if isinstance(value, list) and value:
                        if isinstance(value[0], dict) and 'value' in value[0]:
                            # Time series data
                            print(f"   {field}: {len(value)} data points (latest: {value[0].get('value')})")
                        else:
                            print(f"   {field}: {len(value)} items")
                    elif value is not None:
                        if isinstance(value, str) and len(value) > 50:
                            print(f"   {field}: {value[:50]}...")
                        else:
                            print(f"   {field}: {value}")
                    else:
                        print(f"   {field}: None")
            
            return plant_json
            
        else:
            print(f"❌ No data found for {plant_name}")
            print("🔍 Let me check available plant names...")
            
            # Try to find similar plant names
            import pandas as pd
            df = pd.read_excel('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx', sheet_name='USA Details')
            plant_names = df['Plant Name'].unique()
            
            # Look for partial matches
            similar_plants = [name for name in plant_names if plant_name.lower() in name.lower() or any(word in name.lower() for word in plant_name.lower().split())]
            
            if similar_plants:
                print(f"🔍 Similar plant names found:")
                for name in similar_plants[:10]:  # Show first 10 matches
                    print(f"   - {name}")
            else:
                print(f"❌ No similar plant names found")
            
            return None
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Test both specific plants"""
    print("🚀 TESTING SPECIFIC PLANTS - UNIT LEVEL EXTRACTION")
    print("=" * 100)
    
    # Test plants
    test_plants = [
        "Jim Bridger Steam Plant",
        "Sherburne County"
    ]
    
    results = {}
    
    for plant_name in test_plants:
        result = test_plant_extraction(plant_name)
        if result:
            results[plant_name] = result
    
    # Final summary
    print("\n" + "=" * 100)
    print("📋 EXTRACTION SUMMARY")
    print("=" * 100)
    
    for plant_name in test_plants:
        if plant_name in results:
            unit_count = results[plant_name]['total_units']
            print(f"✅ {plant_name}: {unit_count} units extracted successfully")
        else:
            print(f"❌ {plant_name}: Extraction failed")
    
    if results:
        print(f"\n💾 JSON files created:")
        for plant_name in results:
            filename = f"{plant_name.replace(' ', '_').lower()}_extraction.json"
            print(f"   - {filename}")
        
        print(f"\n🎯 COMPLETE JSON DATA AVAILABLE IN FILES")
    else:
        print(f"\n⚠️ No successful extractions")

if __name__ == "__main__":
    main()
