#!/usr/bin/env python3
"""
Test the database search fix to ensure single-letter searches don't return wrong results
"""

import sys
import os
from dotenv import load_dotenv

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend', 'src'))

# Load environment variables
load_dotenv()

def test_database_search_fix():
    """Test that the database search fix prevents wrong plant matches"""
    
    print("🔍 TESTING DATABASE SEARCH FIX")
    print("=" * 50)
    
    try:
        from agent.database_manager import get_database_manager
        
        db_manager = get_database_manager()
        
        # Test cases that should NOT return results (because plants don't exist)
        test_cases = [
            {
                "plant_name": "D B Wilson Power Station",
                "should_find": False,
                "description": "Plant that doesn't exist - should not find random matches"
            },
            {
                "plant_name": "A Random Plant",
                "should_find": False,
                "description": "Another non-existent plant - should not find matches for 'A'"
            },
            {
                "plant_name": "X Y Z Power Plant",
                "should_find": False,
                "description": "Non-existent plant with single letters - should not find matches"
            },
            {
                "plant_name": "Antelope Valley Station",
                "should_find": True,
                "description": "Plant that exists - should find correct match"
            }
        ]
        
        print(f"🧪 Testing {len(test_cases)} search scenarios...")
        print("-" * 50)
        
        all_passed = True
        
        for i, test_case in enumerate(test_cases, 1):
            plant_name = test_case["plant_name"]
            should_find = test_case["should_find"]
            description = test_case["description"]
            
            print(f"\n{i}. {description}")
            print(f"   Plant: {plant_name}")
            print(f"   Expected: {'FOUND' if should_find else 'NOT FOUND'}")
            
            # Perform the search
            result = db_manager.check_plant_exists(plant_name)
            
            if result:
                found_plant = result["plant_name"]
                org_name = result["org_name"]
                org_id = result["org_id"]
                
                print(f"   Actual: FOUND -> '{found_plant}'")
                print(f"   Organization: {org_name}")
                print(f"   Org ID: {org_id}")
                
                if should_find:
                    print(f"   ✅ CORRECT - Plant was found as expected")
                else:
                    print(f"   ❌ INCORRECT - Plant should not have been found!")
                    print(f"   🚨 This indicates the search is still too broad")
                    all_passed = False
            else:
                print(f"   Actual: NOT FOUND")
                
                if not should_find:
                    print(f"   ✅ CORRECT - Plant was not found as expected")
                else:
                    print(f"   ❌ INCORRECT - Plant should have been found!")
                    all_passed = False
        
        print("\n" + "=" * 50)
        print("🏁 TEST RESULTS")
        print("=" * 50)
        
        if all_passed:
            print("✅ All tests passed!")
            print("🎯 Database search fix is working correctly")
            print("🔒 Single-letter searches are now prevented")
            return True
        else:
            print("❌ Some tests failed!")
            print("🔧 Database search logic needs further refinement")
            return False
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search_term_generation():
    """Test the search term generation logic"""
    
    print("\n🔍 TESTING SEARCH TERM GENERATION")
    print("=" * 50)
    
    try:
        from agent.database_manager import get_database_manager
        
        db_manager = get_database_manager()
        
        # Test different plant names and see what search terms are generated
        test_names = [
            "D B Wilson Power Station",
            "A Random Plant",
            "Antelope Valley Station",
            "Jim Bridger Power Plant",
            "X",  # Single letter
            "AB",  # Two letters
            "ABC Power Plant"  # Three letters
        ]
        
        print("🧪 Testing search term generation for various plant names:")
        print("-" * 50)
        
        for plant_name in test_names:
            print(f"\n📝 Plant: '{plant_name}'")
            
            # Simulate the search term generation logic
            clean_plant_name = plant_name.strip().lower()
            
            # Remove common suffixes
            suffixes = ['power plant', 'power station', 'generating station', 'station', 'plant', ' fossil plant']
            core_name = clean_plant_name
            for suffix in suffixes:
                if core_name.endswith(suffix):
                    core_name = core_name[:-len(suffix)].strip()
                    break
            
            # Generate search terms
            search_terms = [
                clean_plant_name,  # Exact search first
                core_name,         # Core name without suffixes
            ]
            
            # Only add first word search if it's meaningful (more than 2 characters)
            first_word = core_name.split()[0] if core_name else ""
            if first_word and len(first_word) > 2:
                search_terms.append(first_word)
            
            # Filter out very short terms
            valid_search_terms = [term for term in search_terms if term and len(term) >= 3]
            
            print(f"   Search terms: {valid_search_terms}")
            
            if len(valid_search_terms) == 0:
                print(f"   ⚠️ No valid search terms (all too short)")
            elif any(len(term) < 3 for term in valid_search_terms):
                print(f"   ❌ Some search terms are too short!")
            else:
                print(f"   ✅ All search terms are valid")
        
        return True
        
    except Exception as e:
        print(f"❌ Search term generation test failed: {e}")
        return False

def main():
    """Main test function"""
    
    print("🧪 DATABASE SEARCH FIX VALIDATION")
    print("=" * 60)
    
    # Run tests
    search_success = test_database_search_fix()
    generation_success = test_search_term_generation()
    
    print("\n" + "=" * 60)
    print("🏁 FINAL RESULTS")
    print("=" * 60)
    
    if search_success and generation_success:
        print("✅ All tests passed!")
        print("🎯 Database search fix is working correctly")
        print("🔒 UID collision bug has been resolved")
        return True
    else:
        print("❌ Some tests failed")
        print("🔧 Further fixes may be needed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
