#!/usr/bin/env python3
"""
Final verification test for all fixes
"""

import sys
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/backend/src')

from excel_power_plant_tool import ExcelPowerPlantTool
from agent.unit_extraction_stages import combine_unit_data
import json

def main():
    print("🎯 FINAL VERIFICATION TEST")
    print("=" * 80)
    print("Testing all fixes for Cross Generating Station")
    print()
    
    # Initialize Excel tool
    excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx')
    plant_name = "Cross Generating Station"
    
    all_units_data = []
    
    # Process all 4 units
    for unit_num in ['1', '2', '3', '4']:
        print(f"📊 UNIT {unit_num} VERIFICATION")
        print("-" * 30)
        
        # Get Excel data
        excel_results = excel_tool.get_plant_data(plant_name, unit_num)
        
        if excel_results:
            excel_data = excel_results[0]
            
            # Create plant context
            plant_context = {
                'excel_data': excel_data,
                'plant_name': plant_name,
                'country': 'United States',
                'plant_uid': f'test-uuid-{unit_num}',
                'plant_type': 'coal'
            }
            
            # Process unit data
            combined_data = combine_unit_data([], unit_num, plant_context)
            all_units_data.append(combined_data)
            
            # Verify all critical fields
            capacity = combined_data.get('capacity')
            plf = combined_data.get('plf', [])
            auxiliary_power = combined_data.get('auxiliary_power_consumed', [])
            gross_power = combined_data.get('gross_power_generation', [])
            emission_factor = combined_data.get('emission_factor', [])
            
            print(f"✅ Capacity: {capacity} MW")
            print(f"✅ PLF: {len(plf)} records")
            print(f"✅ Auxiliary Power: {len(auxiliary_power)} records")
            print(f"✅ Gross Power Generation: {len(gross_power)} records")
            print(f"✅ Emission Factor: {len(emission_factor)} records")
            
            # Check emission factor years
            if emission_factor:
                years = [ef.get('year') for ef in emission_factor]
                unique_years = len(set(years))
                print(f"✅ Emission Factor Years: {years} ({unique_years} unique)")
                
                if unique_years == len(years) and all(2020 <= year <= 2024 for year in years):
                    print("✅ Emission factor years are correct and unique")
                else:
                    print("❌ Emission factor years have issues")
            
            # Check gross power calculation
            if plf and gross_power and capacity:
                expected = capacity * plf[0]['value'] * 8760 / 1000
                actual = gross_power[0]['value']
                print(f"✅ Gross Power Calculation: Expected {expected:.2f}, Actual {actual}")
                
                if abs(expected - actual) < 1:
                    print("✅ Gross power calculation is correct")
                else:
                    print("❌ Gross power calculation is incorrect")
            
            print()
        
        else:
            print(f"❌ No Excel data found for Unit {unit_num}")
            print()
    
    # Summary
    print("🎯 FINAL SUMMARY")
    print("=" * 40)
    print(f"✅ Total units processed: {len(all_units_data)}")
    print(f"✅ Expected units: 4")
    
    if len(all_units_data) == 4:
        print("✅ ALL UNITS PROCESSED SUCCESSFULLY!")
        
        # Check if all units have the required fields
        all_good = True
        for i, unit_data in enumerate(all_units_data, 1):
            required_fields = ['capacity', 'plf', 'auxiliary_power_consumed', 'gross_power_generation', 'emission_factor']
            missing_fields = []
            
            for field in required_fields:
                if not unit_data.get(field) or (isinstance(unit_data.get(field), list) and len(unit_data.get(field)) == 0):
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"❌ Unit {i} missing: {missing_fields}")
                all_good = False
            else:
                print(f"✅ Unit {i} has all required fields")
        
        if all_good:
            print("\n🎉 ALL ISSUES FIXED SUCCESSFULLY!")
            print("✅ Emission factor years are correct")
            print("✅ Gross power generation is calculated")
            print("✅ All 4 units are processed")
            print("✅ All required fields are present")
        else:
            print("\n❌ Some issues remain")
    
    else:
        print("❌ Not all units were processed")

if __name__ == "__main__":
    main()
