#!/usr/bin/env python3
"""
Debug script to trace what happens during Antelope Valley Station processing
Focus on PLF, auxiliary power, and gross power generation calculations
"""

import sys
import os
sys.path.append('backend/src')

from agent.power_plant_calculation_engine import PowerPlantCalculationEngine, create_calculation_engine
import json

def debug_antelope_valley_calculations():
    """Debug the calculation process for Antelope Valley Station"""
    
    print("🔍 DEBUGGING ANTELOPE VALLEY STATION CALCULATIONS")
    print("=" * 70)
    
    # Create calculation engine
    try:
        calc_engine = create_calculation_engine('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/current state calculations.xlsx')
        print("✅ Calculation engine created successfully")
    except Exception as e:
        print(f"❌ Failed to create calculation engine: {e}")
        return
    
    # Simulate unit data for Antelope Valley Station Unit 2
    # Based on the JSON file we saw
    unit_data = {
        'capacity': 708.0,  # MW - from the JSON
        'technology': 'subcritical',  # Assume subcritical
        'coal_type': 'lignite',  # From the JSON we saw it's lignite
        'unit_number': 2,
        'plant_name': 'Antelope Valley Station'
    }
    
    plant_context = {
        'plant_name': 'Antelope Valley Station',
        'country': 'USA',
        'plant_capacity_mw': 1416.0,  # Total plant capacity (2 units × 708 MW)
        'technology': 'subcritical'
    }
    
    print(f"📊 Unit Data: {unit_data}")
    print(f"📊 Plant Context: {plant_context}")
    print()
    
    # Test the main calculation interface
    print("🧮 RUNNING MAIN CALCULATION INTERFACE")
    print("-" * 50)
    
    try:
        calc_results = calc_engine.calculate_unit_parameters(unit_data, plant_context)
        
        print("✅ Calculation completed successfully!")
        print()
        
        # Print key results
        print("📊 CALCULATION RESULTS:")
        print("-" * 30)
        
        # PLF Results
        if 'plf_unit' in calc_results:
            print(f"🔋 PLF Unit: {calc_results['plf_unit']}")
        if 'time_series_plf' in calc_results:
            print(f"🔋 PLF Time Series: {calc_results['time_series_plf']}")
        
        # Auxiliary Power Results
        if 'auxiliary_power_percent' in calc_results:
            print(f"⚡ Auxiliary Power: {calc_results['auxiliary_power_percent']}")
        if 'time_series_auxiliary_power' in calc_results:
            print(f"⚡ Auxiliary Power Time Series: {calc_results['time_series_auxiliary_power']}")
        
        # Generation Results (calculated from PLF)
        if 'time_series_plf' in calc_results and unit_data.get('capacity'):
            capacity_mw = float(unit_data['capacity'])
            print(f"🏭 Gross Power Generation Calculation:")
            for year, plf_value in calc_results['time_series_plf'].items():
                gross_power_gwh = capacity_mw * plf_value * 8760 / 1000
                print(f"  {year}: {gross_power_gwh:.2f} GWh (PLF: {plf_value:.3f})")
        
        # Calculation Methods Used
        if 'calculation_summary' in calc_results:
            print(f"🔧 Methods Used: {calc_results['calculation_summary']['methods_used']}")
        
        print()
        print("📋 FULL RESULTS:")
        print(json.dumps(calc_results, indent=2, default=str))
        
    except Exception as e:
        print(f"❌ Calculation failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_antelope_valley_calculations()
