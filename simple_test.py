#!/usr/bin/env python3
"""
Simple Test for Global Fixes
============================

Tests the key fixes without complex imports.
"""

import sys
import os

# Add the main project directory to path
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')

def test_usa_auxiliary_power():
    """Test USA auxiliary power calculation"""
    print("🔥 TESTING USA AUXILIARY POWER")
    print("=" * 40)
    
    try:
        from usa_excel_calculation_engine import create_usa_excel_calculation_engine
        
        # Initialize USA calculation engine
        usa_engine = create_usa_excel_calculation_engine()
        
        # Test auxiliary power calculation
        plant_capacity = 477.0  # Example plant capacity
        unit_capacity = 238.5   # Example unit capacity
        
        plant_aux = usa_engine.get_auxiliary_power_percentage(plant_capacity, 'subcritical')
        unit_aux = usa_engine.get_auxiliary_power_percentage(unit_capacity, 'subcritical')
        
        print(f"Plant capacity ({plant_capacity} MW): {plant_aux*100:.1f}% aux power")
        print(f"Unit capacity ({unit_capacity} MW): {unit_aux*100:.1f}% aux power")
        
        if plant_aux != unit_aux:
            print("✅ SUCCESS: Different capacities give different aux power percentages")
        else:
            print("⚠️ WARNING: Same aux power for different capacities")
            
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_plant_capacity_calculation():
    """Test plant capacity calculation"""
    print("\n🏭 TESTING PLANT CAPACITY CALCULATION")
    print("=" * 40)
    
    try:
        from usa_excel_calculation_engine import create_usa_excel_calculation_engine
        
        # Initialize USA calculation engine
        usa_engine = create_usa_excel_calculation_engine()
        
        # Test plant capacity calculation
        plant_name = "Antelope Valley"
        plant_capacity = usa_engine._calculate_plant_capacity_from_excel(plant_name)
        
        print(f"Plant capacity for {plant_name}: {plant_capacity} MW")
        
        if plant_capacity > 0:
            print("✅ SUCCESS: Plant capacity calculated successfully")
            return True
        else:
            print("❌ ERROR: Plant capacity is 0 or negative")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_year_specific_capacity():
    """Test year-specific capacity calculation"""
    print("\n📅 TESTING YEAR-SPECIFIC CAPACITY")
    print("=" * 40)
    
    try:
        from usa_excel_calculation_engine import create_usa_excel_calculation_engine
        
        # Initialize USA calculation engine
        usa_engine = create_usa_excel_calculation_engine()
        
        # Test year-specific capacity
        plant_name = "Antelope Valley"
        
        # Test different years
        years = [2020, 2024, 2030]
        capacities = []
        
        for year in years:
            capacity = usa_engine._calculate_year_specific_plant_capacity(plant_name, year)
            capacities.append(capacity)
            print(f"Year {year}: {capacity} MW")
        
        print("✅ SUCCESS: Year-specific capacity calculation completed")
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_excel_tool_basic():
    """Test basic Excel tool functionality"""
    print("\n📊 TESTING EXCEL TOOL BASIC FUNCTIONALITY")
    print("=" * 40)
    
    try:
        from excel_power_plant_tool import ExcelPowerPlantTool
        
        # Check if Excel file exists
        excel_path = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx'
        if not os.path.exists(excel_path):
            print(f"❌ ERROR: Excel file not found at {excel_path}")
            return False
        
        # Initialize Excel tool
        excel_tool = ExcelPowerPlantTool(excel_path, 'test_session')
        
        print("✅ SUCCESS: Excel tool initialized successfully")
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 RUNNING SIMPLE TESTS FOR GLOBAL FIXES")
    print("=" * 60)
    
    results = []
    
    # Test 1: USA auxiliary power
    results.append(test_usa_auxiliary_power())
    
    # Test 2: Plant capacity calculation
    results.append(test_plant_capacity_calculation())
    
    # Test 3: Year-specific capacity
    results.append(test_year_specific_capacity())
    
    # Test 4: Excel tool basic
    results.append(test_excel_tool_basic())
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
    else:
        print("⚠️ Some tests failed. Check the output above.")

if __name__ == "__main__":
    main()
