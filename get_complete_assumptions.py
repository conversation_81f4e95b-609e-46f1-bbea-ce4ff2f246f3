#!/usr/bin/env python3
"""
Get complete GCV values and other constants from Assumptions sheet
"""

import openpyxl

def get_complete_assumptions():
    """Get all GCV values and constants from Assumptions sheet"""
    print("🔍 EXTRACTING COMPLETE ASSUMPTIONS DATA")
    print("=" * 80)
    
    try:
        # Load the workbook
        wb = openpyxl.load_workbook('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/current state calculations.xlsx', data_only=False)
        ws = wb["Assumptions"]
        
        print(f"📏 Sheet dimensions: {ws.max_row} rows x {ws.max_column} columns")
        
        # Get ALL data from the sheet to find GCV values
        print(f"\n📋 COMPLETE ASSUMPTIONS SHEET:")
        print("-" * 80)
        
        for row in range(1, ws.max_row + 1):
            row_data = []
            for col in range(1, ws.max_column + 1):
                cell = ws.cell(row=row, column=col)
                cell_value = cell.value
                if cell_value:
                    row_data.append(str(cell_value))
            
            if row_data:
                print(f"Row {row:2d}: {' | '.join(row_data)}")
        
        wb.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    get_complete_assumptions()
