#!/usr/bin/env python3
"""
Read and understand the calculation methods from "current state calculations.xlsx"
"""

import pandas as pd
import sys
import os

def read_calculations_excel():
    """Read the Excel file and understand the calculation methods"""
    
    excel_file = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/current state calculations.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel file not found: {excel_file}")
        return
    
    print("📊 READING CURRENT STATE CALCULATIONS EXCEL FILE")
    print("=" * 70)
    
    try:
        # Read all sheets
        excel_data = pd.ExcelFile(excel_file)
        sheet_names = excel_data.sheet_names
        
        print(f"📋 Available sheets: {sheet_names}")
        print()
        
        for sheet_name in sheet_names:
            print(f"📄 SHEET: {sheet_name}")
            print("-" * 50)
            
            try:
                df = pd.read_excel(excel_file, sheet_name=sheet_name)
                
                print(f"Shape: {df.shape}")
                print(f"Columns: {list(df.columns)}")
                print()
                
                # Show first few rows
                print("First 10 rows:")
                print(df.head(10).to_string())
                print()
                
                # Look for calculation formulas or methods
                if 'formula' in str(df.columns).lower() or 'calculation' in str(df.columns).lower():
                    print("🔧 CALCULATION METHODS FOUND:")
                    for col in df.columns:
                        if 'formula' in col.lower() or 'calculation' in col.lower() or 'method' in col.lower():
                            print(f"  {col}:")
                            print(f"    {df[col].dropna().tolist()}")
                    print()
                
                # Look for PLF, auxiliary power, gross power generation
                relevant_keywords = ['plf', 'plant load factor', 'auxiliary', 'gross power', 'generation', 'efficiency', 'heat rate']
                
                for keyword in relevant_keywords:
                    matching_cols = [col for col in df.columns if keyword.lower() in str(col).lower()]
                    if matching_cols:
                        print(f"🔍 Found columns related to '{keyword}': {matching_cols}")
                        for col in matching_cols:
                            print(f"  {col} values:")
                            print(f"    {df[col].dropna().head(5).tolist()}")
                        print()
                
                # Check for any text that looks like formulas
                text_columns = df.select_dtypes(include=['object']).columns
                for col in text_columns:
                    formula_like = df[col].dropna().astype(str)
                    formula_like = formula_like[formula_like.str.contains('=|formula|calculation|method', case=False, na=False)]
                    if not formula_like.empty:
                        print(f"🧮 Potential formulas in {col}:")
                        for formula in formula_like.head(10):
                            print(f"  {formula}")
                        print()
                
            except Exception as e:
                print(f"❌ Error reading sheet {sheet_name}: {e}")
            
            print("=" * 70)
            print()
    
    except Exception as e:
        print(f"❌ Error reading Excel file: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    read_calculations_excel()
