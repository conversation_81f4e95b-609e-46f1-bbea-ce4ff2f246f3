import os
import uuid
import time
from typing import List

from agent.tools_and_schemas import SearchQueryList, Reflection, PowerPlantInfo, UnitLevelInfo
from dotenv import load_dotenv
from langchain_core.messages import AIMessage
from langgraph.types import Send
from langgraph.graph import StateGraph
from langgraph.graph import START, END
from langchain_core.runnables import RunnableConfig
from google.genai import Client

from agent.state import (
    OverallState,
    UnitState,
    QueryGenerationState,
    ReflectionState,
    WebSearchState,
    merge_unit_matrices,
    merge_unit_flags,
)
from agent.configuration import Configuration
from agent.prompts import (
    get_current_date,
    query_writer_instructions,
    web_searcher_instructions,
    reflection_instructions,
    answer_instructions,
    unit_query_writer_instructions,
    unit_web_searcher_instructions,
    unit_reflection_instructions,
    unit_answer_instructions,
)
from langchain_google_genai import ChatGoogleGenerativeAI
from agent.utils import (
    get_citations,
    get_research_topic,
    insert_citation_markers,
    resolve_urls,
)
from agent.image_extraction import extract_and_upload_images
from agent.json_s3_storage import (
    store_organization_data,
    store_plant_data, 
    store_unit_data,
    get_plant_s3_urls,
    check_s3_connection,
    sanitize_plant_name
)
from agent.registry_nodes import (
    check_plant_registry,
    quick_org_discovery_node,
    generate_uid_node,
    # REMOVED: trigger_financial_pipeline_node no longer needed
    populate_database_async_node,
    entity_extraction_trigger_node,
    route_after_registry_check,
    route_after_uid_generation,
    route_after_database_population
)

load_dotenv()

# Global plant counter for sequential plant IDs
# FIXED: Always use plant_id = 1 for single plant processing
def get_next_plant_id():
    """Get plant ID - always returns 1 for single plant processing"""
    return 1  # FIXED: Always return 1 instead of incrementing counter

def reset_plant_id_counter():
    """Reset the plant ID counter to 0 (so next ID will be 1)"""
    global _plant_id_counter
    _plant_id_counter = 0

def get_next_plant_id_by_technology(plant_type: str, session_id: str) -> int:
    """
    Get the next sequential plant ID for a specific technology within the current organization

    For example:
    - Organization has 3 coal plants: returns 1, 2, 3 for coal
    - Organization has 2 diesel plants: returns 1, 2 for diesel
    - Each technology has its own numbering sequence

    Args:
        plant_type: Technology type (coal, diesel, gas, etc.)
        session_id: Session ID for logging

    Returns:
        Next sequential plant ID for this technology
    """
    try:
        from agent.database_manager import get_database_manager

        # Get current organization context from state or database
        # For now, we'll use a simple counter approach
        # In AGI integration, this will query the centralized database

        print(f"[Session {session_id}] 🔍 Getting next plant_id for technology: {plant_type}")

        # TODO: When AGI Layer is implemented, this will query the master database
        # to count existing plants of this technology in the current organization

        # For now, return 1 as default (will be enhanced with AGI Layer)
        # This ensures the fix works immediately while maintaining compatibility
        next_id = 1

        print(f"[Session {session_id}] ✅ Next plant_id for {plant_type}: {next_id}")
        return next_id

    except Exception as e:
        print(f"[Session {session_id}] ❌ Error getting technology-specific plant_id: {e}")
        return 1  # Fallback to 1

def normalize_plant_name(name: str) -> str:
    suffixes = [' Power Plant', ' Generating Plant', ' Power Station', ' Generating Station', ' Energy Center', ' Center', ' Power Complex', ' Generating Complex', ' Power Facility', ' Generating Facility', ' Power Project', ' Generating Project', ' Power Site', ' Generating Site', ' Power Area', ' Generating Area', ' Electric Plant', ' Plant', ' Station', ' Generation Complex', ' Steam Station', ' Steam Electric Plant', ' Energy Complex', ' Electric Plant', ' Complex', ' Fossil Plant']
    for s in suffixes:
        if name.endswith(s):
            name = name[:-len(s)]
            break
    return name.strip()

def save_agi_org_id_directly(state: OverallState) -> OverallState:
    """
    SIMPLE: Save AGI-provided org UID directly to database
    No complex context passing - just direct database save
    """
    session_id = state.get("session_id", "unknown")

    try:
        # Get AGI-provided entity_id (org UID)
        entity_id = state.get("entity_id")
        plant_name = get_research_topic(state.get("messages", []))

        if not entity_id:
            print(f"[Session {session_id}] ❌ No entity_id provided by AGI Layer")
            return {
                **state,
                "agi_uid_saved": False,
                "error": "No entity_id from AGI Layer"
            }

        print(f"[Session {session_id}] 🔧 SIMPLE AGI UID SAVE")
        print(f"[Session {session_id}] Plant: {plant_name}")
        print(f"[Session {session_id}] AGI Org UID: {entity_id}")

        # DEBUG: Check entity extraction flags
        skip_org_discovery = state.get("skip_org_discovery", False)
        print(f"[Session {session_id}] 🔍 DEBUG save_agi_org_id_directly: skip_org_discovery = {skip_org_discovery}")

        # Save AGI UID directly as org_id
        from agent.database_manager import get_database_manager
        db_manager = get_database_manager()

        # Simple approach: Just set org_id to AGI's entity_id
        print(f"[Session {session_id}] ✅ Using AGI UID as org_id: {entity_id}")

        return {
            **state,
            "org_id": entity_id,  # SIMPLE: Use AGI UID directly
            "use_agi_org_id": True,  # Flag to tell finalize_answer to use AGI UID
            "agi_org_id": entity_id,  # Store AGI UID for finalize_answer
            "agi_uid_saved": True,
            "error": None
        }

    except Exception as e:
        print(f"[Session {session_id}] ❌ Error saving AGI UID: {e}")
        return {
            **state,
            "agi_uid_saved": False,
            "error": str(e)
        }

def get_currency_from_organization(plant_name: str) -> str:
    """Get currency from organization data in database"""
    try:
        from agent.database_manager import get_database_manager
        db_manager = get_database_manager()

        # Get plant info from database
        plant_info = db_manager.check_plant_exists(plant_name)
        if plant_info and plant_info.get("country"):
            country = plant_info["country"]

            # Simple country to currency mapping
            currency_map = {
                "Japan": "JPY",
                "India": "INR",
                "China": "CNY",
                "United States": "USD",
                "Germany": "EUR",
                "United Kingdom": "GBP",
                "Canada": "CAD",
                "Australia": "AUD",
                "South Korea": "KRW",
                "Brazil": "BRL",
                "Russia": "RUB",
                "France": "EUR",
                "Italy": "EUR",
                "Spain": "EUR",
                "Netherlands": "EUR",
                "Poland": "PLN",
                "Turkey": "TRY",
                "South Africa": "ZAR",
                "Mexico": "MXN",
                "Indonesia": "IDR",
                "Thailand": "THB",
                "Malaysia": "MYR",
                "Philippines": "PHP",
                "Vietnam": "VND",
                "Singapore": "SGD",
                "Taiwan": "TWD",
                "Hong Kong": "HKD",
                "Czech Republic": "CZK",
                "Romania": "RON",
                "Greece": "EUR",
                "Portugal": "EUR",
                "Belgium": "EUR",
                "Austria": "EUR",
                "Switzerland": "CHF",
                "Norway": "NOK",
                "Sweden": "SEK",
                "Denmark": "DKK",
                "Finland": "EUR"
            }

            return currency_map.get(country, "USD")  # Default to USD if country not found

    except Exception as e:
        print(f"Error getting currency: {e}")

    return "USD"  # Fallback to USD

def process_unit_data_formatting(unit_data: dict, session_id: str, plant_id: int = None) -> dict:
    """
    Process and format unit data according to requirements:
    1. Convert unit_number to integer
    2. Set plant_id to proper integer (from plant processing)
    3. Convert numeric fields to proper types
    4. Replace "default null" with null
    5. Handle yearly data arrays properly
    """
    try:
        import re
        
        print(f"[Session {session_id}] 🔧 Processing unit data formatting...")
        
        # 1. Handle unit_number - SIMPLE MAPPING APPROACH
        unit_number = unit_data.get("unit_number", "1")

        # 🚨 SIMPLE FIX: Just map alphanumeric unit IDs to sequential integers for JSON
        # Keep original unit_id as string, create unit_number as integer for JSON
        if "unit_id" not in unit_data:
            unit_data["unit_id"] = str(unit_number)  # Keep original as string

        # 🚨 FIXED: Handle float strings like "1.0" properly
        # Create sequential integer mapping for unit_number (for JSON output)
        if isinstance(unit_number, str):
            try:
                # Handle both "1" and "1.0" formats
                unit_data["unit_number"] = int(float(unit_number))
            except (ValueError, TypeError):
                # For alphanumeric IDs like ST2, GEN1, map to sequential integers
                unit_data["unit_number"] = 1  # Simple default, can be improved with mapping
        elif isinstance(unit_number, (int, float)):
            unit_data["unit_number"] = int(unit_number)
        else:
            unit_data["unit_number"] = 1
        
        # 2. Set plant_id to proper integer (should come from plant processing)
        if plant_id is not None:
            unit_data["plant_id"] = plant_id
        else:
            # If no plant_id provided, try to convert existing or default to 1
            current_plant_id = unit_data.get("plant_id", "1")
            if isinstance(current_plant_id, str):
                try:
                    unit_data["plant_id"] = int(current_plant_id) if current_plant_id not in ["0", "", "default null"] else 1
                except (ValueError, TypeError):
                    unit_data["plant_id"] = 1
            elif current_plant_id == 0:
                unit_data["plant_id"] = 1
        
        # 3. Ensure required fields exist with proper defaults
        # 🚨 FIXED: Don't overwrite valid remaining_useful_life values - Excel tool handles this
        required_fields = {
            "heat_rate": None,
            "heat_rate_unit": "kJ/kWh",
            "unit_lifetime": None
            # remaining_useful_life removed - Excel tool handles this
        }

        for field, default_value in required_fields.items():
            current_value = unit_data.get(field)

            # Special handling for remaining_useful_life - don't overwrite valid ISO timestamps
            if field == "remaining_useful_life":
                if current_value and isinstance(current_value, str) and "T" in current_value and "Z" in current_value:
                    # Valid ISO timestamp - keep it
                    continue
                elif current_value in [None, "", "Not available", "default null"]:
                    unit_data[field] = default_value
            else:
                # Standard handling for other fields
                if field not in unit_data or unit_data[field] in ["", "Not available", "default null"]:
                    unit_data[field] = default_value

        # 4. Convert numeric fields to proper types
        numeric_fields = {
            "capacity": float,
            "remaining_useful_life": float,
            "unit_lifetime": float,
            "coal_unit_efficiency": float,
            "heat_rate": float,
            "gcv_coal": float,
            "gcv_natural_gas": float,
            "gcv_biomass": float,
            "open_cycle_gas_turbine_efficency": float,
            "closed_cylce_gas_turbine_efficency": float,
            "combined_cycle_heat_rate": float,
            "open_cycle_heat_rate": float,
            "capex_required_retrofit_biomass": float,
            "capex_required_renovation_open_cycle": float,
            "capex_required_renovation_closed_cycle": float,
            "efficiency_loss_biomass_cofiring": float
        }
        
        for field, field_type in numeric_fields.items():
            if field in unit_data:
                value = unit_data[field]
                if isinstance(value, str) and value not in ["", "Not available", "default null"]:
                    try:
                        unit_data[field] = field_type(value)
                    except (ValueError, TypeError):
                        unit_data[field] = None
                elif value in ["Not available", "default null", ""]:
                    unit_data[field] = None

        # 5. Process yearly data arrays (PLF, PAF, etc.)
        yearly_data_fields = [
            "plf", "PAF", "auxiliary_power_consumed", 
            "gross_power_generation", "emission_factor"
        ]
        
        for field in yearly_data_fields:
            if field in unit_data and isinstance(unit_data[field], list):
                for item in unit_data[field]:
                    if isinstance(item, dict):
                        # Convert year to integer
                        if "year" in item and isinstance(item["year"], str):
                            try:
                                item["year"] = int(item["year"])
                            except (ValueError, TypeError):
                                pass
                        
                        # Convert value to appropriate numeric type
                        if "value" in item and isinstance(item["value"], str):
                            try:
                                # Try float first, then int
                                if "." in str(item["value"]):
                                    item["value"] = float(item["value"])
                                else:
                                    item["value"] = int(item["value"])
                            except (ValueError, TypeError):
                                pass
        
        # 5. Process fuel_type data - COAL ONLY (exclude biomass)
        if "fuel_type" in unit_data and isinstance(unit_data["fuel_type"], list):
            # Filter to keep only coal fuel types, exclude biomass
            coal_fuel_types = []

            for fuel_item in unit_data["fuel_type"]:
                if isinstance(fuel_item, dict):
                    fuel_name = fuel_item.get("fuel", "").lower()
                    fuel_type = fuel_item.get("type", "").lower()

                    # Only keep coal fuel types, exclude biomass
                    is_coal = (
                        "coal" in fuel_name or
                        any(coal_type in fuel_type for coal_type in [
                            "bituminous", "sub-bituminous", "lignite", "anthracite"
                        ])
                    )

                    is_biomass = (
                        "biomass" in fuel_name or
                        "wood" in fuel_type or "pellet" in fuel_type or
                        "pks" in fuel_type or "palm" in fuel_type or
                        "kernel" in fuel_type or "husk" in fuel_type or
                        "shell" in fuel_type
                    )

                    # Only include if it's coal and not biomass
                    if is_coal and not is_biomass:
                        if "years_percentage" in fuel_item:
                            years_perc = fuel_item["years_percentage"]
                            if isinstance(years_perc, dict):
                                # Convert year keys to strings and percentage values to floats
                                converted_years = {}
                                for year_key, percentage in years_perc.items():
                                    if isinstance(percentage, str) and percentage not in ["", "default null"]:
                                        try:
                                            converted_years[str(year_key)] = float(percentage)
                                        except (ValueError, TypeError):
                                            converted_years[str(year_key)] = percentage
                                    else:
                                        converted_years[str(year_key)] = percentage
                                fuel_item["years_percentage"] = converted_years

                        coal_fuel_types.append(fuel_item)
                        print(f"[Session {session_id}] ✅ Kept coal fuel type: {fuel_item.get('fuel', '')} - {fuel_item.get('type', '')}")
                    else:
                        print(f"[Session {session_id}] 🚫 Excluded fuel type: {fuel_item.get('fuel', '')} - {fuel_item.get('type', '')} (biomass or non-coal)")

            # Update with filtered coal-only fuel types
            unit_data["fuel_type"] = coal_fuel_types
            print(f"[Session {session_id}] 🔧 Filtered fuel_type: kept {len(coal_fuel_types)} coal entries, excluded biomass")
        
        # 6. Replace "default null" with actual null recursively
        # 🚨 FIXED: Don't overwrite valid remaining_useful_life values
        def replace_default_null(obj):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    # Special handling for remaining_useful_life - preserve valid ISO timestamps
                    if key == "remaining_useful_life" and isinstance(value, str) and "T" in value and "Z" in value:
                        # Valid ISO timestamp - keep it
                        continue
                    elif value == "default null":
                        obj[key] = None
                    elif isinstance(value, (dict, list)):
                        replace_default_null(value)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    if item == "default null":
                        obj[i] = None
                    elif isinstance(item, (dict, list)):
                        replace_default_null(item)

        replace_default_null(unit_data)
        
        print(f"[Session {session_id}] ✅ Unit formatting completed:")
        print(f"[Session {session_id}]    Unit Number: {unit_data.get('unit_number')}")
        print(f"[Session {session_id}]    Plant ID: {unit_data.get('plant_id')}")
        
        return unit_data
        
    except Exception as e:
        print(f"[Session {session_id}] ❌ Error in unit data formatting: {str(e)}")
        # Return original data with minimal changes if processing fails
        return unit_data

def extract_state_from_address(address: str) -> str:
    """Extract state name from plant address string"""
    if not address or address == "Not available":
        return "Unknown State"

    # Common address formats: "City, State, Country" or "District, State, Country"
    address_parts = [part.strip() for part in address.split(',')]

    # First check for known Indian states in the address
    indian_states = [
        'Andhra Pradesh', 'Arunachal Pradesh', 'Assam', 'Bihar', 'Chhattisgarh', 'Goa', 'Gujarat',
        'Haryana', 'Himachal Pradesh', 'Jharkhand', 'Karnataka', 'Kerala', 'Madhya Pradesh',
        'Maharashtra', 'Manipur', 'Meghalaya', 'Mizoram', 'Nagaland', 'Odisha', 'Punjab',
        'Rajasthan', 'Sikkim', 'Tamil Nadu', 'Telangana', 'Tripura', 'Uttar Pradesh',
        'Uttarakhand', 'West Bengal', 'Delhi', 'Jammu and Kashmir', 'Ladakh'
    ]

    address_lower = address.lower()
    for state in indian_states:
        if state.lower() in address_lower:
            return state

    # If no known state found, try address parsing
    if len(address_parts) >= 2:
        # Usually state is the second-to-last part (before country)
        potential_state = address_parts[-2].strip()

        # Handle common variations
        if potential_state and len(potential_state) > 2:
            return potential_state

    # This section is now moved up in the function

    # If no state found, return the middle part of address if available
    if len(address_parts) >= 2:
        return address_parts[-2].strip()

    return "Unknown State"

def process_plant_data_formatting(plant_data: dict, session_id: str, org_id: str = "", plant_id: str = "") -> dict:
    """
    Process and format plant data according to requirements:
    1. Generate proper SK format
    2. Assign sequential plant ID
    3. Convert plant_type to lowercase
    4. Convert units_id to integers
    5. Convert numeric fields to proper types
    6. Replace "default null" with null
    7. Handle empty fields properly
    8. Get plant_id from database and use as pk
    """
    try:
        import re
        
        print(f"[Session {session_id}] 🔧 Processing plant data formatting...")
        print(f"[Session {session_id}] 📊 INPUT SK: {plant_data.get('sk', 'NOT_FOUND')}")
        print(f"[Session {session_id}] 📊 INPUT plant_id: {plant_data.get('plant_id', 'NOT_FOUND')}")
        print(f"[Session {session_id}] 📊 INPUT plant_type: {plant_data.get('plant_type', 'NOT_FOUND')}")
        print(f"[Session {session_id}] 📊 INPUT units_id: {plant_data.get('units_id', 'NOT_FOUND')}")
        
        # 1. Convert plant_type to lowercase
        plant_type = plant_data.get("plant_type", "coal").lower()
        plant_data["plant_type"] = plant_type

        # 2. CRITICAL FIX: Get technology-specific sequential plant_id within organization
        plant_id = get_next_plant_id_by_technology(plant_type, session_id)
        plant_data["plant_id"] = plant_id
        print(f"[Session {session_id}] 🔧 Technology-specific plant_id for {plant_type}: {plant_id}")

        # 3. Generate proper SK format: "plant#coal#1", "plant#coal#2", "plant#diesel#1", etc.
        sk_value = f"plant#{plant_type}#{plant_id}"
        
        # 4. Process units_id - handle duplicates vs non-sequential units correctly
        units_id = plant_data.get("units_id", [])
        if isinstance(units_id, list):
            print(f"[Session {session_id}] 🔧 Original units_id: {units_id}")

            # Check if we have duplicates (like Taichung: [1,2,3,4,5,6,7,8,9,10,1,2,3,4])
            has_duplicates = len(units_id) != len(set(units_id))

            if has_duplicates:
                # CASE 1: Duplicates found - create sequential mapping
                # For Taichung: [1,2,3,4,5,6,7,8,9,10,1,2,3,4] → [1,2,3,4,5,6,7,8,9,10,11,12,13,14]
                sequential_units = [i+1 for i in range(len(units_id))]
                plant_data["units_id"] = sequential_units

                print(f"[Session {session_id}] ✅ Duplicates detected - Sequential mapping: {sequential_units}")
                print(f"[Session {session_id}] 🗺️ Mapping: {dict(zip(sequential_units, [str(u) for u in units_id]))}")

                # Store the original-to-sequential mapping for reference
                original_to_sequential = {i+1: str(units_id[i]) for i in range(len(units_id))}
                print(f"[Session {session_id}] 📋 Original unit mapping: {original_to_sequential}")
            else:
                # CASE 2: No duplicates - preserve original unit numbers
                # For Kosovo A: [3, 4, 5] → keep as [3, 4, 5]
                # For normal plants: ['Unit 1', 'Unit 2', 'Unit 3'] → [1, 2, 3]
                clean_units = []
                for unit in units_id:
                    if isinstance(unit, int):
                        clean_units.append(str(unit))  # Convert to string for consistency
                    elif isinstance(unit, str):
                        # Handle both "3" and "Unit 3" formats, but keep alphanumeric as strings
                        if unit.isdigit():
                            clean_units.append(unit)  # Keep as string
                        else:
                            # For "Unit 1", "Unit 2", etc. or alphanumeric like "D1", "CTG1"
                            import re
                            match = re.search(r'\d+', unit)
                            if match and unit.startswith('Unit'):
                                # Extract number from "Unit 1" format
                                clean_units.append(match.group())
                            else:
                                # Keep alphanumeric unit IDs as-is (D1, CTG1, etc.)
                                clean_units.append(unit)

                # Sort to ensure consistent order and remove duplicates
                clean_units = sorted(list(set(clean_units)))
                plant_data["units_id"] = clean_units

                print(f"[Session {session_id}] ✅ No duplicates - Preserving original units: {clean_units}")
                print(f"[Session {session_id}] 🗺️ Unit mapping: {dict(zip(clean_units, [f'Unit {u}' for u in clean_units]))}")
        
        # 5. Convert numeric fields to proper types
        numeric_fields = {
            "lat": float,
            "long": float,
            "capacity": int,
            "plant_id": int
        }
        
        for field, field_type in numeric_fields.items():
            if field in plant_data:
                value = plant_data[field]
                if isinstance(value, str) and value not in ["", "Not available", "default null"]:
                    try:
                        plant_data[field] = field_type(value)
                    except (ValueError, TypeError):
                        plant_data[field] = None if field_type in [int, float] else ""
                elif value in ["Not available", "default null"]:
                    plant_data[field] = None if field_type in [int, float] else ""
        
        # 6. Process nested numeric fields (like in ppa_details)
        if "ppa_details" in plant_data and isinstance(plant_data["ppa_details"], list):
            for ppa in plant_data["ppa_details"]:
                if isinstance(ppa, dict):
                    # Convert capacity to int
                    if "capacity" in ppa and isinstance(ppa["capacity"], str):
                        try:
                            if ppa["capacity"] not in ["", "Not available", "default null"]:
                                ppa["capacity"] = int(ppa["capacity"])
                            else:
                                ppa["capacity"] = None
                        except (ValueError, TypeError):
                            ppa["capacity"] = None
                    
                    # Process respondents
                    if "respondents" in ppa and isinstance(ppa["respondents"], list):
                        for resp in ppa["respondents"]:
                            if isinstance(resp, dict) and "capacity" in resp:
                                if isinstance(resp["capacity"], str):
                                    try:
                                        if resp["capacity"] not in ["", "Not available", "default null"]:
                                            resp["capacity"] = int(resp["capacity"])
                                        else:
                                            resp["capacity"] = None
                                    except (ValueError, TypeError):
                                        resp["capacity"] = None
        
        # 7. Process grid connectivity numeric fields
        if "grid_connectivity_maps" in plant_data and isinstance(plant_data["grid_connectivity_maps"], list):
            for grid_map in plant_data["grid_connectivity_maps"]:
                if isinstance(grid_map, dict) and "details" in grid_map:
                    for detail in grid_map["details"]:
                        if isinstance(detail, dict):
                            # Convert latitude and longitude
                            for coord_field in ["latitude", "longitude"]:
                                if coord_field in detail and isinstance(detail[coord_field], str):
                                    try:
                                        if detail[coord_field] not in ["", "Not available", "default null"]:
                                            detail[coord_field] = float(detail[coord_field])
                                        else:
                                            detail[coord_field] = None
                                    except (ValueError, TypeError):
                                        detail[coord_field] = None
        
        # 8. Replace "default null" with actual null
        def replace_default_null(obj):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if value == "default null":
                        obj[key] = None
                    elif isinstance(value, (dict, list)):
                        replace_default_null(value)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    if item == "default null":
                        obj[i] = None
                    elif isinstance(item, (dict, list)):
                        replace_default_null(item)
        
        replace_default_null(plant_data)
        
        # 9. Handle specific null fields
        null_fields = ["closure_year", "s3_url"]  # Removed mandatory_closure from null fields
        for field in null_fields:
            if field in plant_data and plant_data[field] in ["default null", "Not available", ""]:
                plant_data[field] = None

        # Handle mandatory_closure specifically - should be "Yes" or "No", never null
        if "mandatory_closure" in plant_data:
            if plant_data["mandatory_closure"] in ["default null", "Not available", "", None]:
                plant_data["mandatory_closure"] = "No"
        
        # Handle potential_reference nested nulls
        if "potential_reference" in plant_data and isinstance(plant_data["potential_reference"], dict):
            for coord in ["lat", "long"]:
                if coord in plant_data["potential_reference"]:
                    if plant_data["potential_reference"][coord] in ["default null", "Not available", ""]:
                        plant_data["potential_reference"][coord] = None
        
        # 10. Handle pk and plant_id fields - pk should be plant_id, not org_id
        # CRITICAL FIX: Use plant_id passed as parameter (from state)
        if not plant_id:
            # Fallback: try to get from database
            plant_name = plant_data.get("name", "")
            if plant_name and org_id:
                try:
                    from agent.database_manager import get_database_manager
                    db_manager = get_database_manager()

                    # Check if plant exists in database
                    existing_plant = db_manager.check_plant_exists(plant_name)
                    if existing_plant and existing_plant.get("plant_id"):
                        plant_id = existing_plant["plant_id"]
                        print(f"[Session {session_id}] ✅ Found plant_id from database: {plant_id}")
                    else:
                        # Generate new plant_id if not found
                        plant_id = db_manager.generate_plant_id(plant_name, org_id)
                        print(f"[Session {session_id}] 🔧 Generated new plant_id: {plant_id}")

                except Exception as e:
                    print(f"[Session {session_id}] ⚠️ Error getting plant_id: {e}")
                    # Fallback to org_id if plant_id retrieval fails
                    plant_id = org_id
        else:
            print(f"[Session {session_id}] ✅ Using plant_id from parameter: {plant_id}")

        # Set pk to plant_id (not org_id)
        if plant_id:
            plant_data["pk"] = plant_id
            plant_data["plant_id"] = plant_id  # Also store plant_id separately
            print(f"[Session {session_id}] 🔑 Set pk to plant_id: {plant_id}")
        else:
            plant_data["pk"] = None
            print(f"[Session {session_id}] ⚠️ No plant_id available, pk set to null")
        
        # 11. Create ordered dict with sk first
        ordered_plant_data = {"sk": sk_value}
        for key, value in plant_data.items():
            if key != "sk":  # Skip if sk already exists
                ordered_plant_data[key] = value
        
        # 12. Add the fixed fields
        ordered_plant_data["is_ppa"] = "yes"
        ordered_plant_data["is_retrofitting"] = "yes"
        ordered_plant_data["plant_transition_life"] = 5

        # 12.1. Add plant_name field (duplicate of name field)
        if "name" in ordered_plant_data:
            ordered_plant_data["plant_name"] = ordered_plant_data["name"]
            print(f"[Session {session_id}] ✅ Added plant_name field: {ordered_plant_data['plant_name']}")
        else:
            ordered_plant_data["plant_name"] = "Unknown Plant"
            print(f"[Session {session_id}] ⚠️ No name field found, using default plant_name")

        # 12.2. Add state field (extract from plant_address)
        if "plant_address" in ordered_plant_data:
            plant_address = ordered_plant_data["plant_address"]
            state = extract_state_from_address(plant_address)
            ordered_plant_data["state"] = state
            print(f"[Session {session_id}] ✅ Added state field: {state}")
        else:
            ordered_plant_data["state"] = "Unknown State"
            print(f"[Session {session_id}] ⚠️ No plant_address found, using default state")
        
        print(f"[Session {session_id}] ✅ Plant formatting completed:")
        print(f"[Session {session_id}]    SK: {sk_value}")
        print(f"[Session {session_id}]    Plant ID: {plant_id}")
        print(f"[Session {session_id}]    Plant Type: {plant_type}")
        print(f"[Session {session_id}]    Units ID: {ordered_plant_data.get('units_id', [])}")
        print(f"[Session {session_id}]    PK: {ordered_plant_data.get('pk')}")
        print(f"[Session {session_id}]    Lat: {ordered_plant_data.get('lat')} (type: {type(ordered_plant_data.get('lat'))})")
        print(f"[Session {session_id}]    Closure Year: {ordered_plant_data.get('closure_year')}")
        
        return ordered_plant_data
        
    except Exception as e:
        print(f"[Session {session_id}] ❌ Error in plant data formatting: {str(e)}")
        # Return original data with minimal changes if processing fails
        return {
            "sk": f"plant#{plant_data.get('plant_type', 'coal').lower()}#{get_next_plant_id()}",
            **plant_data,
            "plant_name": plant_data.get("name", "Unknown Plant"),  # Add plant_name field
            "state": extract_state_from_address(plant_data.get("plant_address", "")),  # Add state field
            "is_ppa": "yes",
            "is_retrofitting": "yes",
            "plant_transition_life": 5
        }

if os.getenv("GEMINI_API_KEY") is None:
    raise ValueError("GEMINI_API_KEY is not set")

# Used for Google Search API
genai_client = Client(api_key=os.getenv("GEMINI_API_KEY"))


def initialize_session(state: OverallState) -> OverallState:
    """Initialize a new research session with proper state management.
    
    This function ensures each research session starts with clean state
    and generates a unique session ID for tracking.
    """
    # Generate a unique session ID
    session_id = str(uuid.uuid4())[:8] + str(int(time.time()))[-4:]
    
    print(f"🚀 INITIALIZE_SESSION CALLED - New research session: {session_id}")
    print(f"🔍 DEBUG: Input state keys: {list(state.keys())}")
    print(f"🔍 DEBUG: Messages count: {len(state.get('messages', []))}")
    
    # Get the research topic from messages
    research_topic = get_research_topic(state["messages"]) if state.get("messages") else ""
    
    print(f"📝 Research topic: {research_topic}")
    
    # Initialize S3 JSON storage tracking
    plant_s3_urls = get_plant_s3_urls(research_topic, session_id)
    plant_name_sanitized = sanitize_plant_name(research_topic)
    
    # Test S3 connection
    s3_ready = check_s3_connection(session_id)
    
    print(f"🗂️ S3 folder will be: {plant_name_sanitized}")
    print(f"🔗 S3 connection: {'✅ Ready' if s3_ready else '❌ Failed'}")
    print(f"✅ INITIALIZE_SESSION COMPLETE - Session {session_id}")
    
    # Return clean initialized state (no progress messages in UI)
    # PRESERVE entity extraction specific fields if they exist
    initialized_state = {
        "messages": state.get("messages", []),
        "session_id": session_id,
        "search_phase": state.get("search_phase", 1),  # Preserve search_phase from entity extraction
        "research_loop_count": 0,
        "web_research_result": [],
        "search_query": [],
        "sources_gathered": [],
        "org_level_complete": state.get("org_level_complete", False),  # Preserve from entity extraction
        "continue_research": False,
        "phase_complete": False,
        "initial_search_query_count": state.get("initial_search_query_count", 5),
        "max_research_loops": state.get("max_research_loops", 3),
        "reasoning_model": state.get("reasoning_model", ""),
        # S3 JSON Storage initialization
        "plant_name_for_s3": state.get("plant_name_for_s3", research_topic),  # Preserve from entity extraction
        "s3_json_urls": state.get("s3_json_urls", plant_s3_urls),  # Preserve from entity extraction
        "json_storage_complete": state.get("json_storage_complete", {"organization": False, "plant": False, "units": {}}),
        "json_storage_errors": state.get("json_storage_errors", []),
    }

    # PRESERVE entity extraction specific fields
    entity_fields = ["skip_org_discovery", "entity_id", "org_id", "org_name", "plant_country"]
    print(f"🔍 DEBUG initialize_session: Input state keys = {list(state.keys())}")
    print(f"🔍 DEBUG initialize_session: skip_org_discovery in input = {state.get('skip_org_discovery', 'NOT_FOUND')}")

    for field in entity_fields:
        if field in state:
            initialized_state[field] = state[field]
            print(f"🔧 PRESERVED entity field: {field} = {state[field]}")
        else:
            print(f"⚠️ MISSING entity field: {field}")

    print(f"🔍 DEBUG initialize_session: Final skip_org_discovery = {initialized_state.get('skip_org_discovery', 'NOT_SET')}")

    return initialized_state


def extract_images_parallel(state: OverallState) -> OverallState:
    """
    LangGraph node that extracts images for the power plant in parallel.
    
    This function runs concurrently with the main research pipeline to gather
    relevant images for the power plant and upload them to S3.
    """
    session_id = state.get("session_id", "unknown")
    research_topic = get_research_topic(state.get("messages", []))
    
    print(f"[Session {session_id}] 🖼️ Starting parallel image extraction for: {research_topic}")
    
    try:
        # Extract and upload images
        s3_urls = extract_and_upload_images(research_topic, session_id)
        
        if s3_urls:
            print(f"[Session {session_id}] ✅ Image extraction successful: {len(s3_urls)} images uploaded")
            return {
                "image_extraction_complete": True,
                "s3_image_urls": s3_urls,
                "image_extraction_error": ""
            }
        else:
            print(f"[Session {session_id}] ⚠️ No images found or uploaded")
            return {
                "image_extraction_complete": True,
                "s3_image_urls": [],
                "image_extraction_error": "No images found"
            }
            
    except Exception as e:
        error_msg = f"Image extraction failed: {str(e)}"
        print(f"[Session {session_id}] ❌ {error_msg}")
        return {
            "image_extraction_complete": True,
            "s3_image_urls": [],
            "image_extraction_error": error_msg
        }


# Nodes
def generate_query(state: OverallState, config: RunnableConfig) -> QueryGenerationState:
    """LangGraph node that generates search queries based on the User's question.

    Uses Gemini 2.0 Flash to create optimized search queries for web research based on
    the User's question.

    Args:
        state: Current graph state containing the User's question
        config: Configuration for the runnable, including LLM provider settings

    Returns:
        Dictionary with state update, including query_list containing the generated queries
    """
    configurable = Configuration.from_runnable_config(config)

    # Check for custom initial search query count
    if state.get("initial_search_query_count") is None:
        state["initial_search_query_count"] = configurable.number_of_initial_queries
    
    # Get the search phase from the state
    search_phase = state.get("search_phase", 1)
    session_id = state.get("session_id", "unknown")
    
    print(f"[Session {session_id}] ===== QUERY GENERATION START =====")
    print(f"[Session {session_id}] Phase: {search_phase}")
    print(f"[Session {session_id}] Current state messages count: {len(state.get('messages', []))}")
    print(f"[Session {session_id}] Current state search_query: {state.get('search_query', [])}")
    print(f"[Session {session_id}] Current state web_research_result count: {len(state.get('web_research_result', []))}")
    print(f"[Session {session_id}] Current state sources_gathered count: {len(state.get('sources_gathered', []))}")
    
    # Initialize Gemini 2.0 Flash
    llm = ChatGoogleGenerativeAI(
        model=configurable.query_generator_model,
        temperature=0,
        max_retries=3,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    structured_llm = llm.with_structured_output(SearchQueryList)

    # Get current date and research topic
    current_date = get_current_date()
    research_topic = get_research_topic(state["messages"])
    
    print(f"[Session {session_id}] *** RESEARCH TOPIC EXTRACTED: '{research_topic}' ***")
    
    if search_phase == 1:
        # Organization-level query generation
        formatted_prompt = f"""Your goal is to generate sophisticated search queries to gather ONLY organization-level information about a power plant.

Instructions:
- Generate search queries to find detailed information about the power plant named in the research topic.
- Focus EXCLUSIVELY on queries that will help extract the following ORGANIZATION-LEVEL information:
  1. The ownership type (public, private, or joint-venture)
  2. The country and province/state where it's located
  3. The parent organization/company that owns or operates it
  4. The currency used in that country and its financial year reporting period
  5. The exact number of power plant sites/projects owned by the specific power plant mentioned (not the parent organization)
  6. The types of power generation technologies used by the specific power plant mentioned (not all technologies used by the parent organization)
  7. Information about Power Purchase Agreements (PPAs) structure

IMPORTANT: The plants_count should match the number of items in plant_types. These fields refer to the specific power plant mentioned in the research topic, not the parent organization.

- DO NOT include queries for plant-level details at this stage
- Don't produce more than {state.get("initial_search_query_count", 5)} queries.
- Ensure queries are diverse and cover different aspects of the organization-level information.
- The current date is {current_date}, so focus on getting the most up-to-date information.

Format: 
- Format your response as a JSON object with ALL three of these exact keys:
   - "rationale": Brief explanation of why these queries are relevant for gathering organization-level information
   - "query": A list of search queries

Research Topic: {research_topic}
"""
    elif search_phase == 2:
        # Plant-level query generation
        formatted_prompt = f"""Your goal is to generate sophisticated search queries to gather ONLY plant-level information about a power plant.

Instructions:
- Generate search queries to find detailed information about the power plant named in the research topic.
- Focus EXCLUSIVELY on queries that will help extract the following PLANT-LEVEL information:
  1. The plant's official name (name) and unique identifier (plant_id)
  2. The plant's specific technology or fuel type (plant_type)
  3. The plant's precise location coordinates (lat, long) and address (plant_address)
  4. List of operational units at the plant (units_id) - PRIORITIZE GEMWiki (gem.wiki) for accurate unit information
  5. Detailed PPA information including:
     - Capacity and capacity unit
     - Start date and end date
     - Tenure and tenure type
     - Respondent details (name, capacity, price, price unit, currency)
  6. Grid connectivity information including:
     - Substation name and type
     - Substation capacity
     - Substation coordinates (latitude, longitude)
     - Distance from substation to projects

- Include queries that might reveal detailed plant-level information from company reports, technical documents, and grid maps
- MANDATORY: Include at least one query specifically for "PLANT_NAME site:gem.wiki" to get unit specifications
- MANDATORY: Include at least one query for "PLANT_NAME units specifications capacity" to find unit information
- Search for precise geographic coordinates for both the plant and its connected substations
- Don't produce more than {state.get("initial_search_query_count", 5)} queries.
- Ensure queries are diverse and cover different aspects of the plant-level information.
- The current date is {current_date}, so focus on getting the most up-to-date information.

Format: 
- Format your response as a JSON object with ALL three of these exact keys:
   - "rationale": Brief explanation of why these queries are relevant for gathering plant-level information
   - "query": A list of search queries

Research Topic: {research_topic}
"""
    elif search_phase == 3:
        # Unit-level query generation
        unit_number = state.get("unit_number", "1")
        country_context = ""
        
        # Try to extract country from previous research for country-specific data
        messages = state.get("messages", [])
        for message in reversed(messages):
            if hasattr(message, 'content') and isinstance(message.content, str):
                content = message.content.lower()
                if "country" in content:
                    # Extract country information for country-specific queries
                    break
        
        formatted_prompt = f"""Your goal is to generate sophisticated search queries to gather comprehensive UNIT-LEVEL technical information about a specific unit of a power plant.

Instructions:
- Generate search queries to find detailed technical information about Unit {unit_number} of the power plant named in the research topic.
- Focus EXCLUSIVELY on queries that will help extract the following UNIT-LEVEL technical information:

CRITICAL UNIT DATA:
  1. Unit specifications: capacity (MW), technology type, unit_number, boiler_type, commencement_date
  2. Operational performance: PLF (Plant Load Factor) by year, PAF (Plant Availability Factor) by year, coal_unit_efficiency (%), auxiliary_power_consumed by year
  3. Technical efficiency: heat_rate and heat_rate_unit (kJ/kWh or kcal/kWh), station heat rate for the specific technology
  4. Power generation: gross_power_generation by year, emission_factor (kg CO2e/kWh) by year
  5. Fuel specifications: fuel_type details, fuel percentages by year
  6. Country-specific fuel data: gcv_coal (kCal/kg), gcv_natural_gas (10000 kcal/scm), gcv_biomass (kCal/kg) for the plant's country
  7. Technology-specific efficiency: For the plant's country - CCGT efficiency, OCGT efficiency, combined_cycle_heat_rate, open_cycle_heat_rate
  8. Conversion economics: capex_required_retrofit_biomass (biomass cofiring), capex_required_renovation_open_cycle, capex_required_renovation_closed_cycle, efficiency_loss_biomass_cofiring
  9. Lifecycle information: unit_lifetime (years), remaining_useful_life (end date)
  10. RETIREMENT & END-OF-LIFE DATA: retirement schedule, decommissioning timeline, planned closure date, asset retirement obligations, end-of-life planning, phase-out schedule

SEARCH STRATEGY:
- Include the specific unit number (Unit {unit_number}) in your queries
- Search for technical specifications, performance reports, and operational data
- Look for country-specific fuel properties and efficiency standards
- Search for technology-specific efficiency data for the plant's country
- Include queries for conversion/retrofit cost estimates
- Search for detailed technical reports and regulatory filings
- PRIORITIZE RETIREMENT DATA: Search for unit retirement schedules, decommissioning plans, closure timelines, and end-of-life dates
- Look for regulatory filings, environmental compliance documents, and utility asset retirement plans

- Don't produce more than {state.get("initial_search_query_count", 6)} queries.
- Ensure queries are diverse and cover different aspects of unit-level technical information.
- The current date is {current_date}, so focus on getting the most up-to-date technical data.

Format: 
- Format your response as a JSON object with ALL three of these exact keys:
   - "rationale": Brief explanation of why these queries are relevant for gathering unit-level technical information
   - "query": A list of search queries

Research Topic: {research_topic}
Unit Number: {unit_number}
"""
    else:
        # Default case - should not happen
        formatted_prompt = f"""Generate basic search queries for the research topic: {research_topic}"""
    
    # Generate the search queries
    result = structured_llm.invoke(formatted_prompt)
    print(f"[Session {session_id}] Generated {len(result.query)} queries for Unit {state.get('unit_number', 'unknown')} (phase {search_phase})")
    
    # Return the query list and PRESERVE ALL STATE
    return {
        "query_list": result.query,
        "research_loop_count": state.get("research_loop_count", 0),
        "number_of_ran_queries": state.get("number_of_ran_queries", 0),

        # PRESERVE MULTI-UNIT STATE - CRITICAL!
        "remaining_units": state.get("remaining_units", []),
        "current_unit": state.get("current_unit"),
        "all_units": state.get("all_units", []),
        "unit_results": state.get("unit_results", []),
        "search_phase": search_phase,
        "unit_number": state.get("unit_number"),

        # UIDs will be retrieved from database when needed - no state management required
    }


# This function has been replaced by phase-specific functions:
# - continue_to_org_web_research
# - continue_to_plant_web_research


def web_research(state: WebSearchState, config: RunnableConfig) -> OverallState:
    """LangGraph node that performs web research using the native Google Search API tool.

    Executes a web search using the native Google Search API tool in combination with Gemini 2.0 Flash.

    Args:
        state: Current graph state containing the search query and research loop count
        config: Configuration for the runnable, including search API settings

    Returns:
        Dictionary with state update, including sources_gathered, research_loop_count, and web_research_results
    """
    # Configure
    configurable = Configuration.from_runnable_config(config)
    search_phase = state.get("search_phase", 1)
    session_id = state.get("session_id", "unknown")
    
    print(f"[Session {session_id}] ===== WEB RESEARCH START =====")
    print(f"[Session {session_id}] Phase: {search_phase}")
    print(f"[Session {session_id}] Query: '{state['search_query']}'")
    print(f"[Session {session_id}] Query ID: {state.get('id', 'unknown')}")
    print(f"[Session {session_id}] State messages count: {len(state.get('messages', []))}")
    
    # Get the current research topic to ensure we're searching for the right plant
    research_topic = get_research_topic(state.get("messages", []))
    print(f"[Session {session_id}] *** RESEARCH TOPIC FROM MESSAGES: '{research_topic}' ***")
    
    # Log if there's any contamination
    if "jhajjar" in research_topic.lower() and "ho-ping" in state['search_query'].lower():
        print(f"[Session {session_id}] ⚠️  CONTAMINATION DETECTED! Research topic contains 'Jhajjar' but query is about 'Ho-Ping'")
    elif "jhajjar" in state['search_query'].lower() and "ho-ping" in research_topic.lower():
        print(f"[Session {session_id}] ⚠️  CONTAMINATION DETECTED! Query contains 'Jhajjar' but research topic is about 'Ho-Ping'")
    elif "seil" in research_topic.lower() and ("jhajjar" in state['search_query'].lower() or "ho-ping" in state['search_query'].lower()):
        print(f"[Session {session_id}] ⚠️  CONTAMINATION DETECTED! Research topic is 'Seil' but query contains other plant names")
    
    # Customize the prompt based on the search phase
    if search_phase == 1:
        # Phase 1: Focus on organization-level information
        formatted_prompt = f"""Conduct targeted Google Searches to gather ONLY organization-level information about the power plant mentioned.

Instructions:
- The current date is {get_current_date()}.
- Search for and extract ONLY the following organization-level details about the power plant:
  1. CFPP Type: Whether it's publicly owned, privately owned, or a joint venture
  2. Country Name: The full name of the country where it's located
  3. Currency: The ISO currency code used in that country (e.g., USD, EUR, INR)
  4. Financial Year: The fiscal year reporting period used in that country (in MM-MM format)
  5. Organization Name: The full legal name of the company/entity that owns/operates it
  6. Plants Count: The exact number of power plant sites/projects owned by the organization
  7. Plant Types: All types of power generation technologies used (coal, gas, hydro, etc.)
  8. PPA Flag: Whether Power Purchase Agreements are at plant-level or unit-level
  9. Province: The state/province/region where the plant is located

- DO NOT focus on plant-level details at this stage.
- Be precise and factual. Only include information that you can verify from credible sources.
- Track all sources meticulously for citation purposes.

Research Topic: {state["search_query"]}
"""
    elif search_phase == 2:
        # Phase 2: Focus on plant-level information
        formatted_prompt = f"""Conduct targeted Google Searches to gather ONLY plant-level information about the power plant mentioned.

Instructions:
- The current date is {get_current_date()}.
- Search for and extract ONLY the following plant-level details about the power plant:
  1. name: The official name of the specific power plant
  2. plant_id: A unique identifier for this plant (integer)
  3. plant_type: The specific technology or fuel type used at this plant
  4. lat: The plant's latitude coordinate in decimal degrees
  5. long: The plant's longitude coordinate in decimal degrees
  6. plant_address: Detailed address including district/city, state, country
  7. units_id: List of all operational units at this plant
  8. ppa_details: For each PPA, collect:
     - capacity: The capacity covered by this PPA (in MW)
     - capacity_unit: The unit of capacity (e.g., 'MW', 'kW')
     - start_date: The PPA's commencement date (ISO format, YYYY-MM-DD)
     - end_date: The PPA's termination date (ISO format, YYYY-MM-DD)
     - tenure: The numeric duration of the PPA (e.g., 20)
     - tenure_type: The unit for the tenure (e.g., 'Years', 'Months')
     - respondents: For each respondent, include:
       * name: The counterparty's name (utility, trader, corporate buyer, etc.)
       * capacity: The capacity allocated to this respondent (in MW)
       * currency: The currency of the contract (e.g., 'INR', 'USD')
       * price: The tariff rate (e.g., '4.50')
       * price_unit: The unit for the price (e.g., 'INR/kWh', 'USD/MWh')
  9. grid_connectivity_maps: For each grid connection, collect:
     - substation_name: The official name of the substation
     - substation_type: The classification and voltage level of the substation
     - capacity: The rated capacity of the connection at this substation (in MW)
     - latitude: The geographic latitude of the substation
     - longitude: The geographic longitude of the substation
     - projects: For each connected project, collect:
       * distance: The distance (e.g., in km) from the substation to that project

- DO NOT focus on organization-level details at this stage.
- Look for technical documents, regulatory filings, and company reports for PPA details.
- Search for grid connectivity maps and transmission planning documents.
- Try to find precise geographic coordinates for both the plant and its connected substations.
- Be precise and factual. Only include information that you can verify from credible sources.
- Track all sources meticulously for citation purposes.

Research Topic: {state["search_query"]}
"""
    elif search_phase == 3:
        # Phase 3: Focus on unit-level technical information
        unit_number = state.get("unit_number", "1")

        # Get actual currency for this plant
        plant_name = get_research_topic(state.get("messages", []))
        actual_currency = get_currency_from_organization(plant_name)
        print(f"[Session {session_id}] 💱 Using currency for unit web search: {actual_currency}")

        formatted_prompt = f"""Conduct comprehensive Google Searches to gather detailed UNIT-LEVEL technical information about a specific unit of the power plant.

Instructions:
- Search for detailed technical information about Unit {unit_number} of the power plant mentioned in the research topic.
- Focus EXCLUSIVELY on extracting the following UNIT-LEVEL technical data:

UNIT SPECIFICATIONS:
  1. capacity: Unit-wise installed capacity (MW)
  2. capacity_unit: Unit for capacity (typically 'MW')
  3. technology: Specific technology (Ultra Super Critical, Super Critical, Critical, Sub-critical for coal; Single/Open Cycle, Combined/Closed Cycle for gas; etc.)
  4. unit_number: Unit number labeling
  5. boiler_type: Type of boiler used in the unit
  6. commencement_date: Commercial operation date (yyyy-mm-ddThh:mm:ss.msZ format)

OPERATIONAL PERFORMANCE:
  7. plf: Plant Load Factor by year [{{ "value": "PLF percentage", "year": "year" }}]
  8. PAF: Plant Availability Factor by year [{{ "value": "PAF percentage", "year": "year" }}]
  9. coal_unit_efficiency: Unit specific efficiency (%)
  10. auxiliary_power_consumed: AUX energy consumption by year [{{ "value": "percentage", "year": "year" }}]
  11. gross_power_generation: Total energy generated by year [{{ "value": "generation", "year": "year" }}]

TECHNICAL EFFICIENCY:
  12. heat_rate: Station Heat Rate (kJ/kWh or kcal/kWh)
  13. heat_rate_unit: Unit for heat rate measurement
  14. emission_factor: CO2 emissions by year [{{ "value": "kg CO2e/kWh", "year": "year" }}]

FUEL SPECIFICATIONS:
  15. fuel_type: Coal fuel sources ONLY with percentages by year [{{ "fuel": "Coal", "type": "bituminous/sub-bituminous/lignite/anthracite", "years_percentage": {{"year": "percentage"}} }}] - EXCLUDE biomass details

  18. gcv_coal: Gross calorific value of coal (kCal/kg) - country-specific
  19. gcv_natural_gas: GCV of natural gas (10000 kcal/scm) - country-specific
  20. gcv_biomass: GCV of biomass (kCal/kg) - country-specific

COUNTRY-SPECIFIC EFFICIENCY DATA:
  21. closed_cylce_gas_turbine_efficency: CCGT efficiency for the country (%)
  22. open_cycle_gas_turbine_efficency: OCGT efficiency for the country (%)
  23. combined_cycle_heat_rate: CCGT heat rate for the country
  24. open_cycle_heat_rate: OCGT heat rate for the country

CONVERSION ECONOMICS:
  25. capex_required_retrofit_biomass: CAPEX for biomass cofiring retrofit (Million {actual_currency})
  26. capex_required_renovation_open_cycle: CAPEX for conversion to OCGT ({actual_currency}/MW)
  27. capex_required_renovation_closed_cycle: CAPEX for conversion to CCGT ({actual_currency}/MW)
  28. efficiency_loss_biomass_cofiring: Efficiency reduction from biomass cofiring (%)

LIFECYCLE INFORMATION:
  29. unit_lifetime: Total operational lifetime (years)
  30. remaining_useful_life: End-of-life date (yyyy-mm-ddThh:mm:ss.msZ format) - CRITICAL: Search for retirement schedules, decommissioning plans, closure dates, asset retirement obligations

INHERITED FROM PLANT LEVEL:
  31. plant_id: Unique plant identifier

SEARCH STRATEGY:
- Search for technical specifications reports, performance data, and regulatory filings
- Look for country-specific fuel properties and efficiency standards
- Search for technology-specific performance data for the plant's country
- Include environmental reports for emission factors
- Search for conversion/retrofit cost estimates and efficiency impacts
- Look for detailed unit-level operational data and performance metrics

- Be precise and factual. Only include information that you can verify from credible sources.
- Track all sources meticulously for citation purposes.
- Focus on the specific unit number when searching.

Research Topic: {state["search_query"]}
Unit Number: {unit_number}
"""
    else:
        # Default case - should not happen
        formatted_prompt = f"""Conduct basic web search for: {state["search_query"]}"""

    try:
        # Uses the google genai client as the langchain client doesn't return grounding metadata
        response = genai_client.models.generate_content(
            model=configurable.query_generator_model,
            contents=formatted_prompt,
            config={
                "tools": [{"google_search": {}}],
                "temperature": 0,
            },
        )
        
        # Get grounding chunks safely
        grounding_chunks = None
        if (response and hasattr(response, 'candidates') and response.candidates 
            and hasattr(response.candidates[0], 'grounding_metadata') 
            and response.candidates[0].grounding_metadata
            and hasattr(response.candidates[0].grounding_metadata, 'grounding_chunks')):
            grounding_chunks = response.candidates[0].grounding_metadata.grounding_chunks
        
        # resolve the urls to short urls for saving tokens and time
        resolved_urls = resolve_urls(grounding_chunks, state["id"])
        
        # Gets the citations and adds them to the generated text
        citations = get_citations(response, resolved_urls)
        modified_text = insert_citation_markers(response.text, citations)
        sources_gathered = [item for citation in citations for item in citation["segments"]]
        
        print(f"[Session {session_id}] Successfully completed web research for phase {search_phase} query: '{state['search_query']}'")
        
    except Exception as e:
        # Handle any exceptions that might occur during the API call or processing
        import traceback
        print(f"[Session {session_id}] Error in web_research: {str(e)}")
        print(f"[Session {session_id}] Traceback: {traceback.format_exc()}")
        # Return a fallback response
        modified_text = f"I encountered an error while researching '{state['search_query']}'. Error: {str(e)}"
        sources_gathered = []

    # Return the new data + PRESERVE ALL STATE
    return {
        "sources_gathered": sources_gathered,
        "search_query": [state["search_query"]],
        "web_research_result": [modified_text],

        # PRESERVE MULTI-UNIT STATE - CRITICAL!
        "remaining_units": state.get("remaining_units", []),
        "current_unit": state.get("current_unit"),
        "all_units": state.get("all_units", []),
        "unit_results": state.get("unit_results", []),
        "search_phase": state.get("search_phase"),
        "unit_number": state.get("unit_number"),

        # UIDs will be retrieved from database when needed - no state management required
    }


def reflection(state: OverallState, config: RunnableConfig) -> ReflectionState:
    """LangGraph node that identifies knowledge gaps and generates potential follow-up queries.

    Analyzes the current summary to identify areas for further research and generates
    potential follow-up queries. Uses structured output to extract
    the follow-up query in JSON format.

    Args:
        state: Current graph state containing the running summary and research topic
        config: Configuration for the runnable, including LLM provider settings

    Returns:
        Dictionary with state update, including search_query key containing the generated follow-up query
    """
    configurable = Configuration.from_runnable_config(config)
    # Increment the research loop count and get the reasoning model
    state["research_loop_count"] = state.get("research_loop_count", 0) + 1
    reasoning_model = state.get("reasoning_model") or configurable.reasoning_model
    search_phase = state.get("search_phase", 1)
    
    # Format the prompt based on the search phase
    current_date = get_current_date()
    research_topic = get_research_topic(state["messages"])
    summaries = "\n\n---\n\n".join(state["web_research_result"])
    
    if search_phase == 1:
        # Phase 1: Focus on organization-level information gaps
        reflection_prompt = f"""You are an expert analyst evaluating information gathered about a power plant. Your task is to determine if we have sufficient ORGANIZATION-LEVEL information.

Instructions:
- Review the summaries to determine if we have gathered all the required ORGANIZATION-LEVEL information about the power plant.
- The required organization-level information includes:
  1. CFPP Type (public/private/joint-venture)
  2. Country Name (full country name)
  3. Currency (ISO code)
  4. Financial Year (MM-MM format)
  5. Organization Name (full legal name)
  6. Plants Count (exact number owned by the organization)
  7. Plant Types (list of generation technologies)
  8. PPA Flag (plant-level or unit-level)
  9. Province (state/region)

- Identify any missing or unclear organization-level information and generate targeted follow-up queries.
- If all organization-level information is available and clear, indicate that the research is sufficient.
- DO NOT consider plant-level details at this stage.

Output Format:
- Format your response as a JSON object with these exact keys:
   - "is_sufficient": true or false
   - "knowledge_gap": Describe which specific organization-level data points are missing or unclear
   - "follow_up_queries": Generate specific queries to find the missing organization-level information

The current date is {current_date}.
Research Topic: {research_topic}

Summaries:
{summaries}
"""
    elif search_phase == 2:
        # Phase 2: Focus on plant-level information gaps
        reflection_prompt = f"""You are an expert analyst evaluating information gathered about a power plant. Your task is to determine if we have sufficient PLANT-LEVEL information.

Instructions:
- Review the summaries to determine if we have gathered all the required PLANT-LEVEL information about the power plant.
- The required plant-level information includes:
  1. name: The official name of the specific power plant
  2. plant_id: A unique identifier for this plant (integer)
  3. plant_type: The specific technology or fuel type used at this plant
  4. lat: The plant's latitude coordinate in decimal degrees
  5. long: The plant's longitude coordinate in decimal degrees
  6. plant_address: Detailed address including district/city, state, country
  7. units_id: List of all operational units at this plant
  8. ppa_details: For each PPA, collect:
     - capacity: The capacity covered by this PPA (in MW)
     - capacity_unit: The unit of capacity (e.g., 'MW', 'kW')
     - start_date: The PPA's commencement date (ISO format, YYYY-MM-DD)
     - end_date: The PPA's termination date (ISO format, YYYY-MM-DD)
     - tenure: The numeric duration of the PPA (e.g., 20)
     - tenure_type: The unit for the tenure (e.g., 'Years', 'Months')
     - respondents: For each respondent, collect:
       * name: The counterparty's name (utility, trader, corporate buyer, etc.)
       * capacity: The capacity volume contracted by this respondent
       * price: The contracted price per unit of energy or capacity
       * price_unit: The basis for the price (e.g., '$/MWh', 'INR/kW-year')
       * currency: The currency in which the price is denominated (e.g., 'USD', 'INR')
  9. grid_connectivity_maps: For each grid connection, collect:
     - substation_name: The official name of the substation
     - substation_type: The classification and voltage level of the substation
     - capacity: The rated capacity of the connection at this substation (in MW)
     - latitude: The geographic latitude of the substation
     - longitude: The geographic longitude of the substation
     - projects: For each connected project, collect:
       * distance: The distance (e.g., in km) from the substation to that project

- Identify any missing or unclear plant-level information and generate targeted follow-up queries.
- If all plant-level information is available and clear, indicate that the research is sufficient.
- DO NOT consider organization-level details at this stage.

Output Format:
- Format your response as a JSON object with these exact keys:
   - "is_sufficient": true or false
   - "knowledge_gap": Describe which specific plant-level data points are missing or unclear
   - "follow_up_queries": Generate specific queries to find the missing plant-level information

The current date is {current_date}.
Research Topic: {research_topic}

Summaries:
{summaries}
"""
    else:
        # Phase 3: Focus on unit-level technical information gaps
        unit_number = state.get("unit_number", "1")
        reflection_prompt = f"""You are an expert technical analyst evaluating information gathered about a specific unit of a power plant. Your task is to determine if we have sufficient UNIT-LEVEL technical information for Unit {unit_number}.

Instructions:
- Review the summaries to determine if we have gathered all the required UNIT-LEVEL technical information about Unit {unit_number}.
- The required unit-level technical information includes:

CRITICAL UNIT SPECIFICATIONS:
  1. unit_number: Unit number labeling
  2. capacity: Unit-wise installed capacity (MW)
  3. technology: Specific technology type (Ultra Super Critical, Super Critical, etc.)
  4. boiler_type: Type of boiler used in the unit
  5. commencement_date: Commercial operation date

OPERATIONAL PERFORMANCE DATA:
  6. plf: Plant Load Factor by year (historical data preferred)
  7. PAF: Plant Availability Factor by year
  8. coal_unit_efficiency: Unit specific efficiency (%)
  9. auxiliary_power_consumed: AUX energy consumption by year
  10. gross_power_generation: Total energy generated by year

TECHNICAL EFFICIENCY METRICS:
  11. heat_rate: Station Heat Rate (kJ/kWh or kcal/kWh)
  12. heat_rate_unit: Unit for heat rate measurement
  13. emission_factor: CO2 emissions by year (kg CO2e/kWh)

FUEL SPECIFICATIONS:
  14. fuel_type: Coal fuel sources ONLY with percentages by year - EXCLUDE biomass details

  17. gcv_coal: Gross calorific value of coal (country-specific)
  18. gcv_natural_gas: GCV of natural gas (10000 kcal/scm)
  19. gcv_biomass: GCV of biomass (country-specific)

COUNTRY-SPECIFIC EFFICIENCY DATA:
  20. closed_cylce_gas_turbine_efficency: CCGT efficiency for the country
  21. open_cycle_gas_turbine_efficency: OCGT efficiency for the country
  22. combined_cycle_heat_rate: CCGT heat rate for the country
  23. open_cycle_heat_rate: OCGT heat rate for the country

CONVERSION ECONOMICS:
  24. capex_required_retrofit_biomass: CAPEX for biomass cofiring retrofit
  25. capex_required_renovation_open_cycle: CAPEX for conversion to OCGT
  26. capex_required_renovation_closed_cycle: CAPEX for conversion to CCGT
  27. efficiency_loss_biomass_cofiring: Efficiency reduction from biomass cofiring

LIFECYCLE INFORMATION:
  28. unit_lifetime: Total operational lifetime (years)
  29. remaining_useful_life: End-of-life date

- Identify any missing or unclear unit-level technical information and generate targeted follow-up queries.
- If all critical unit-level information is available and clear, indicate that the research is sufficient.
- Focus specifically on Unit {unit_number} - do not consider other units or plant-level details.
- Prioritize technical specifications, operational data, and country-specific efficiency metrics.

Output Format:
- Format your response as a JSON object with these exact keys:
   - "is_sufficient": true or false
   - "knowledge_gap": Describe which specific unit-level technical data points are missing or unclear for Unit {unit_number}
   - "follow_up_queries": Generate specific technical queries to find the missing unit-level information

The current date is {current_date}.
Research Topic: {research_topic}
Unit Number: {unit_number}

Summaries:
{summaries}
"""
    
    # init Reasoning Model
    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0.7,  # Reduced temperature for more focused queries
        max_retries=3,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    try:
        # Import Reflection at the top of the try block to ensure it's available
        from agent.tools_and_schemas import Reflection
        result = llm.with_structured_output(Reflection).invoke(reflection_prompt)

        # Check if result is None or missing required attributes
        if result is None:
            print(f"[Session {state.get('session_id', 'unknown')}] ⚠️ LLM returned None for reflection, using fallback")
            from agent.tools_and_schemas import Reflection
            result = Reflection(
                is_sufficient=False,
                knowledge_gap="LLM failed to provide reflection analysis",
                follow_up_queries=["Retry research with more specific queries"]
            )
        elif not hasattr(result, 'is_sufficient'):
            print(f"[Session {state.get('session_id', 'unknown')}] ⚠️ LLM result missing is_sufficient attribute, using fallback")
            from agent.tools_and_schemas import Reflection
            result = Reflection(
                is_sufficient=False,
                knowledge_gap="LLM response was malformed",
                follow_up_queries=["Retry research with clearer prompts"]
            )

    except Exception as e:
        print(f"[Session {state.get('session_id', 'unknown')}] ❌ Reflection error: {str(e)}")
        # Create a fallback reflection result - import Reflection here to ensure it's available
        from agent.tools_and_schemas import Reflection
        result = Reflection(
            is_sufficient=False,
            knowledge_gap=f"Reflection failed due to error: {str(e)}",
            follow_up_queries=["Retry research with different approach"]
        )

    return {
        "is_sufficient": result.is_sufficient,
        "knowledge_gap": result.knowledge_gap,
        "follow_up_queries": result.follow_up_queries,
        "research_loop_count": state.get("research_loop_count", 0) + 1,  # Increment the loop count
        "number_of_ran_queries": len(state["search_query"]),
        "search_phase": search_phase,  # Maintain the current search phase

        # PRESERVE MULTI-UNIT STATE - CRITICAL!
        "remaining_units": state.get("remaining_units", []),
        "current_unit": state.get("current_unit"),
        "all_units": state.get("all_units", []),
        "unit_results": state.get("unit_results", []),
        "unit_number": state.get("unit_number"),

        # UIDs will be retrieved from database when needed - no state management required
    }


# This function has been replaced by phase-specific functions:
# - evaluate_org_research
# - evaluate_plant_research


def finalize_answer(state: OverallState, config: RunnableConfig):
    """LangGraph node that finalizes the power plant information extraction.

    Extracts structured information about a power plant from the research results
    and formats it as a standardized JSON object with specific fields.

    Args:
        state: Current graph state containing the research results and sources gathered

    Returns:
        Dictionary with state update, including messages containing the formatted JSON data
    """
    configurable = Configuration.from_runnable_config(config)
    reasoning_model = state.get("reasoning_model") or configurable.reasoning_model
    search_phase = state.get("search_phase", 1)
    session_id = state.get("session_id", "unknown")
    
    # Format the prompt based on the search phase
    current_date = get_current_date()
    research_topic = get_research_topic(state["messages"])
    summaries = "\n---\n\n".join(state["web_research_result"])
    
    # Initialize Reasoning Model
    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=3,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    if search_phase == 1:
        # Organization-level information extraction
        # CRITICAL FIX: Get the actual UID from state
        org_id = state.get("org_id", "")
        if not org_id:
            print(f"❌ No UID found in state - this should not happen!")
            return state  # Return without processing if no UID
        else:
            print(f"✅ Using UID from state: {org_id}")

        org_prompt = f"""Extract and format ONLY organization-level information about the power plant from the research summaries.

Instructions:
- The current date is {current_date}.
- Extract ONLY the following organization-level information about the power plant:
  1. cfpp_type: Classification as "Public", "Private", or "Joint-Venture"
  2. country_name: Full name of the country where the plant is located
  3. currency_in: ISO currency code of the country (e.g., USD, EUR, INR)
  4. financial_year: Fiscal year format in MM-MM (e.g., "04-03" for April-March)
  5. organization_name: Full legal name of the owning/operating company
  6. plants_count: Exact number of power plants owned by the specific power plant mentioned in the research topic (not the total owned by the parent organization)
  7. plant_types: List of power generation technologies used by the specific power plant mentioned (not all technologies used by the parent organization)
  8. ppa_flag: Whether PPAs are at "Plant-level" or "Unit-level" (always use "Unit")
  9. province: State/province/region where the plant is located

IMPORTANT: The plants_count should match the number of items in plant_types. These fields refer to the specific power plant mentioned in the research topic, not the parent organization.

- Format your response ONLY as a clean JSON object with these exact field names.
- Do not include any explanatory text before or after the JSON.
- If any information is not available in the summaries, use null for missing fields, empty strings for unavailable text, or empty arrays for lists.
- Ensure the JSON is properly formatted and valid.
- DO NOT include any plant-level fields at this stage.
- CRITICAL: Use the exact UID provided: {org_id}

Example format:
```json
{{
  "sk": "org_details",
  "cfpp_type": "Private",
  "country_name": "India",
  "currency_in": "INR",
  "financial_year": "04-03",
  "organization_name": "Adani Power Limited",
  "plants_count": 1,
  "plant_types": ["Coal"],
  "ppa_flag": "Unit",
  "province": "Gujarat",
  "pk": "{org_id}",
  "country_flag": null,
  "currency_convert_to": null,
  "currency_listed": [null],
  "off_peak_hours": 0.466,
  "peak_hours": 0.9,
  "plant_addition": null,
  "selected_method": null
}}
```

Research Topic: {research_topic}

Summaries:
{summaries}
"""
        try:
            # Generate organization-level information
            result = llm.invoke(org_prompt)
            final_content = result.content
            
            # Add a prefix to indicate this is organization-level information
            final_content = f"ORGANIZATION-LEVEL INFORMATION:\n{final_content}"
            
            print("Successfully generated organization-level information")
            
        except Exception as e:
            import traceback
            print(f"Error in organization-level generation: {str(e)}")
            print(traceback.format_exc())
            
            # Fallback organization-level response - CRITICAL FIX: Use actual UID
            fallback_org_id = state.get("org_id", "")
            if not fallback_org_id:
                # Generate emergency UID for fallback
                import hashlib
                import time
                plant_name = get_research_topic(state.get("messages", []))
                org_hash = hashlib.md5(f"Unknown_{plant_name}".encode()).hexdigest()[:6].upper()
                timestamp = str(int(time.time()))[-8:]
                fallback_org_id = f"ORG_UN_{org_hash}_{timestamp}"
                # fallback_org_id will be returned in state update
                print(f"[Session {session_id}] 🚨 Generated emergency fallback UID: {fallback_org_id}")

            final_content = f"""ORGANIZATION-LEVEL INFORMATION:
{{
  "sk": "org_details",
  "cfpp_type": "Not available",
  "country_name": "Not available",
  "currency_in": "Not available",
  "financial_year": "Not available",
  "organization_name": "Not available",
  "plants_count": 0,
  "plant_types": [],
  "ppa_flag": "Unit",
  "pk": "{fallback_org_id}",
  "org_id": "{fallback_org_id}",
  "country_flag": null,
  "currency_convert_to": null,
  "currency_listed": [null],
  "off_peak_hours": 0.466,
  "peak_hours": 0.9,
  "plant_addition": null,
  "selected_method": null
}}"""

        # Initialize S3 state variables
        current_s3_urls = state.get("s3_json_urls", {})
        current_storage_status = state.get("json_storage_complete", {})
        current_errors = state.get("json_storage_errors", [])
        
        # SAVE ORGANIZATION JSON TO S3
        try:
            session_id = state.get("session_id", "unknown")
            plant_name = state.get("plant_name_for_s3", research_topic)

            # CRITICAL FIX: Initialize org_id outside conditional block to prevent "name not defined" error
            org_id = ""

            # Extract JSON from the response
            import json
            start_idx = final_content.find('{')
            end_idx = final_content.rfind('}') + 1
            if start_idx != -1 and end_idx != -1:
                json_str = final_content[start_idx:end_idx]
                org_data = json.loads(json_str)

                # SIMPLE DATABASE LOOKUP: Get org_id from database for pk field
                plant_name = get_research_topic(state.get("messages", []))
                print(f"[Session {session_id}] 🔍 Getting org_id from database for plant: {plant_name}")

                if "organization_name" in org_data and "country_name" in org_data:
                    try:
                        from agent.database_manager import get_database_manager
                        db_manager = get_database_manager()

                        # 🚨 CRITICAL FIX: Check if we already have org_id from previous successful queries
                        if state.get("use_agi_org_id") and state.get("agi_org_id"):
                            org_id = state.get("agi_org_id")
                            print(f"[Session {session_id}] ✅ Using AGI-provided org_id: {org_id}")
                        else:
                            # Simple database lookup for org_id
                            existing_plant = db_manager.check_plant_exists(plant_name, org_data["country_name"])
                            if existing_plant and existing_plant.get("org_id"):
                                org_id = existing_plant["org_id"]
                                print(f"[Session {session_id}] ✅ Retrieved org_id from database: {org_id}")
                            else:
                                print(f"[Session {session_id}] ⚠️ No org_id found in database for {plant_name} (search may have failed)")

                    except Exception as e:
                        print(f"[Session {session_id}] ❌ Database lookup failed: {e}")

                # SIMPLE: Set pk field from database lookup
                if org_id:
                    org_data["pk"] = org_id
                    print(f"[Session {session_id}] ✅ Set pk field from database: {org_id}")
                else:
                    print(f"[Session {session_id}] ⚠️ No org_id available for pk field")

                # Process organization data formatting (replace "default null" with null)
                # BUT SKIP the pk field since we already set it correctly
                def replace_default_null(obj):
                    if isinstance(obj, dict):
                        for key, value in obj.items():
                            if key == "pk":
                                # SKIP pk field - we already set it correctly
                                continue
                            if value == "default null":
                                obj[key] = None
                            elif isinstance(value, (dict, list)):
                                replace_default_null(value)
                    elif isinstance(obj, list):
                        for i, item in enumerate(obj):
                            if item == "default null":
                                obj[i] = None
                            elif isinstance(item, (dict, list)):
                                replace_default_null(item)

                replace_default_null(org_data)
                print(f"[Session {session_id}] 🔍 DEBUG UID BEFORE STORAGE:")
                print(f"[Session {session_id}] 🔍 State keys: {list(state.keys())}")
                print(f"[Session {session_id}] 🔍 org_id from state: '{org_id}'")
                print(f"[Session {session_id}] 🔍 uid_generation_complete: {state.get('uid_generation_complete', 'NOT_SET')}")

                # CRITICAL FIX: If UID is missing, try multiple approaches to get it
                if not org_id and "organization_name" in org_data and "country_name" in org_data:
                    print(f"[Session {session_id}] 🚨 UID MISSING! Trying multiple approaches...")

                    try:
                        from agent.database_manager import get_database_manager
                        db_manager = get_database_manager()

                        # First, try to find existing UID in database
                        plant_name = get_research_topic(state.get("messages", []))
                        existing_plant = db_manager.check_plant_exists(plant_name, org_data["country_name"])

                        # 🚨 CRITICAL FIX: Check multiple sources for org_id in priority order
                        org_id = None

                        # Priority 1: AGI-provided Org UID
                        if state.get("use_agi_org_id") and state.get("agi_org_id"):
                            org_id = state.get("agi_org_id")
                            print(f"[Session {session_id}] 🔧 Using AGI-provided organization UID: {org_id}")

                        # Priority 2: Check if org_id already exists in state from previous successful queries
                        elif state.get("org_id"):
                            org_id = state.get("org_id")
                            print(f"[Session {session_id}] ✅ Using org_id from state: {org_id}")

                        # Priority 3: Database lookup (may fail due to search issues)
                        elif existing_plant and existing_plant.get("org_id"):
                            org_id = existing_plant["org_id"]
                            print(f"[Session {session_id}] ✅ Found existing UID in database: {org_id}")

                        # Priority 4: Generate new UID only if all above fail
                        else:
                            org_id = db_manager.generate_org_id(
                                org_data["organization_name"],
                                org_data["country_name"]
                            )
                            print(f"[Session {session_id}] ✅ Generated new UID: {org_id}")

                        # UID will be returned in state update

                    except Exception as e:
                        print(f"[Session {session_id}] ❌ Failed to get/generate UID: {e}")
                        # Last resort: create a simple UID
                        import hashlib
                        import time
                        org_hash = hashlib.md5(org_data["organization_name"].encode()).hexdigest()[:6].upper()
                        country_code = org_data["country_name"][:2].upper()
                        timestamp = str(int(time.time()))[-8:]
                        org_id = f"ORG_{country_code}_{org_hash}_{timestamp}"
                        print(f"[Session {session_id}] 🚨 Created emergency UID: {org_id}")
                        # org_id will be returned in state update

                # ENHANCEMENT: Set fixed values for off_peak_hours and peak_hours AFTER null replacement
                # This ensures they are not overridden by replace_default_null
                org_data["off_peak_hours"] = 0.466
                org_data["peak_hours"] = 0.9
                print(f"[Session {session_id}] ✅ Set fixed values AFTER null replacement: off_peak_hours=0.466, peak_hours=0.9")

                org_s3_url = store_organization_data(org_data, plant_name, session_id, org_id)
                
                # CRITICAL FIX: Update state with correct organization name from 3-level extraction
                if "organization_name" in org_data:
                    correct_org_name = org_data["organization_name"]
                    print(f"[Session {session_id}] 🔄 Updating org_name from '{state.get('org_name', '')}' to '{correct_org_name}'")
                    state["org_name"] = correct_org_name
                
                if org_s3_url:
                    print(f"[Session {session_id}] ✅ Organization JSON saved to S3: {org_s3_url}")
                    # Update S3 URLs in state
                    current_s3_urls = state.get("s3_json_urls", {})
                    current_s3_urls["organization"] = org_s3_url
                    
                    current_storage_status = state.get("json_storage_complete", {})
                    current_storage_status["organization"] = True
                else:
                    print(f"[Session {session_id}] ❌ Failed to save organization JSON to S3")
                    current_errors = state.get("json_storage_errors", [])
                    current_errors.append("Failed to save organization JSON to S3")
            else:
                print(f"[Session {session_id}] ⚠️ Could not extract JSON from organization response")
                current_errors = state.get("json_storage_errors", [])
                current_errors.append("Could not extract JSON from organization response")
                
        except Exception as s3_error:
            print(f"[Session {session_id}] ❌ S3 storage error for organization: {str(s3_error)}")
            current_errors = state.get("json_storage_errors", [])
            current_errors.append(f"S3 organization storage error: {str(s3_error)}")
        
        # Filter sources that were actually used in the response
        unique_sources = []
        for source in state.get("sources_gathered", []):
            if isinstance(source, dict):
                if ("short_url" in source and source["short_url"] in final_content) or \
                   ("value" in source and source["value"] in final_content):
                    unique_sources.append(source)
            elif hasattr(source, "short_url") and source.short_url in final_content:
                unique_sources.append(source)
            elif hasattr(source, "value") and source.value in final_content:
                unique_sources.append(source)
        
        # Return organization-level results with S3 updates
        return {
            "messages": [AIMessage(content=final_content)],
            "sources_gathered": unique_sources,
            "s3_json_urls": current_s3_urls,
            "json_storage_complete": current_storage_status,
            "json_storage_errors": current_errors,
            "org_id": org_id,  # CRITICAL FIX: Return org_id instead of direct state modification
        }
    
    elif search_phase == 2:
        # Plant-level information extraction
        # DEFINE UIDs AT FUNCTION SCOPE TO AVOID SCOPE CONFLICTS
        plant_name = get_research_topic(state.get("messages", []))
        plant_id = ""
        org_id = ""

        # DATABASE LOOKUP: Get UIDs from database
        try:
            from agent.database_manager import get_database_manager
            db_manager = get_database_manager()

            # Get plant_id from database
            existing_plant = db_manager.check_plant_exists(plant_name)
            if existing_plant:
                plant_id = existing_plant.get("plant_id", "")
                org_id = existing_plant.get("org_id", "")
                print(f"[Session {session_id}] ✅ Retrieved from database: plant_id='{plant_id}', org_id='{org_id}'")
            else:
                print(f"[Session {session_id}] ⚠️ Plant not found in database: {plant_name}")

        except Exception as e:
            print(f"[Session {session_id}] ❌ Database lookup failed: {e}")

        plant_prompt = f"""Extract and format ONLY plant-level information about the power plant from the research summaries.

Instructions:
- The current date is {current_date}.
- PRIORITIZE GEMWiki (gem.wiki) data for accurate unit information - look for unit tables and specifications
- Extract ONLY the following plant-level information about the power plant:
  1. name: The official name of the power plant
  2. plant_id: A unique identifier assigned to this plant (integer)
  3. plant_type: The technology or fuel type of the plant site
  4. lat: The plant's latitude coordinate in decimal degrees
  5. long: The plant's longitude coordinate in decimal degrees
  6. plant_address: District or city, State, Country
  7. units_id: List of units which are operational at this plant (CRITICAL: Look for patterns like "Unit 1", "Unit 2", "6 x 600 MW", "6 units", etc. If you find "X x Y MW" convert to [1,2,3...X]. If no specific units mentioned, use [1] as default)
  8. ppa_details: For each PPA, collect:
     - capacity: The capacity covered by this PPA (in MW)
     - capacity_unit: The unit of capacity (e.g., 'MW', 'kW')
     - start_date: The PPA's commencement date (ISO format, YYYY-MM-DD)
     - end_date: The PPA's termination date (ISO format, YYYY-MM-DD)
     - tenure: The numeric duration of the PPA (e.g., 20)
     - tenure_type: The unit for the tenure (e.g., 'Years', 'Months')
     - respondents: For each respondent, include:
       * name: The counterparty's name (utility, trader, corporate buyer, etc.)
       * capacity: The capacity volume contracted by this respondent
       * price: The contracted price per unit of energy or capacity
       * price_unit: The basis for the price (e.g., '$/MWh', 'INR/kW-year')
       * currency: The currency in which the price is denominated (e.g., 'USD', 'INR')
  9. grid_connectivity_maps: For each grid connection, collect:
     - details: Array of substation details including:
       * substation_name: The official name of the substation
       * substation_type: The classification and voltage level of the substation
       * capacity: The rated capacity of the connection at this substation (in MW)
       * latitude: The geographic latitude of the substation
       * longitude: The geographic longitude of the substation
       * projects: Array of connected projects with:
         + distance: The distance (e.g., in km) from the substation to that project
  10. plant_images: Array of S3 URLs for images related to this power plant (will be populated automatically)

- Format your response ONLY as a clean JSON object with these exact field names.
- Do not include any explanatory text before or after the JSON.
- If any information is not available in the summaries, use null for missing fields, empty strings for unavailable text, or empty arrays for lists.
- Ensure the JSON is properly formatted and valid.
- DO NOT include any organization-level fields at this stage.

Example format:
```json
{{
  "sk": "plant#coal#1",
  "name": "Mundra Thermal Power Station",
  "plant_id": 1,
  "plant_type": "Coal",
  "lat": "22.8235",
  "long": "69.5323",
  "plant_address": "Mundra, Gujarat, India",
  "units_id": ["Unit 1", "Unit 2", "Unit 3", "Unit 4", "Unit 5"],
  "ppa_details": [
    {{
      "capacity": "1000",
      "capacity_unit": "MW",
      "start_date": "2010-03-01",
      "end_date": "2035-03-01",
      "tenure": "25",
      "tenure_type": "Years",
      "respondents": [
        {{
          "name": "Gujarat Urja Vikas Nigam Limited",
          "capacity": "1000",
          "currency": "INR",
          "price": "2.35",
          "price_unit": "INR/kWh"
        }}
      ]
    }}
  ],
  "grid_connectivity_maps": [
    {{
      "details": [
        {{
          "substation_name": "Mundra HVDC Station",
          "substation_type": "HVDC 500kV",
          "capacity": "2500",
          "latitude": "22.8401",
          "longitude": "69.5501",
          "projects": [
            {{
              "distance": "3.5 km"
            }}
          ]
        }}
      ]
    }}
  ],
  "plant_images": [],
  "pk": "{org_id}",
  "closure_year": null,
  "mandatory_closure": "No",
  "potential_reference": {{
    "lat": null,
    "long": null
  }},
  "s3_url": null
}}
```

Research Topic: {research_topic}

Summaries:
{summaries}
"""
        try:
            # Generate plant-level information
            result = llm.invoke(plant_prompt)
            final_content = result.content
            
            # Add image URLs to the plant JSON if available
            try:
                import json
                import re
                
                # Extract JSON from the response
                start_idx = final_content.find('{')
                end_idx = final_content.rfind('}') + 1
                if start_idx != -1 and end_idx != -1:
                    json_str = final_content[start_idx:end_idx]
                    plant_data = json.loads(json_str)
                    
                    # Get image URLs from state (populated by parallel image extraction)
                    image_urls = state.get("s3_image_urls", [])
                    if image_urls:
                        plant_data["plant_images"] = image_urls
                        print(f"✅ Added {len(image_urls)} image URLs to plant JSON")
                    else:
                        print("ℹ️ No image URLs found in state - plant_images will remain empty")

                    # CRITICAL: Check if this is a USA plant and use Excel tool for units_id
                    plant_name = plant_data.get("name", research_topic)
                    country = plant_data.get("plant_address", "")

                    # Try to get country from database if not in plant_address
                    if not country or "united states" not in country.lower():
                        try:
                            from agent.database_manager import get_database_manager
                            db_manager = get_database_manager()
                            plant_info = db_manager.check_plant_exists(plant_name)
                            if plant_info and plant_info.get("country"):
                                country = plant_info["country"]
                                print(f"[Session {session_id}] 🌍 Retrieved country from database: {country}")
                        except Exception as e:
                            print(f"[Session {session_id}] ⚠️ Could not retrieve country: {e}")

                    print(f"[Session {session_id}] 🔍 Checking data source for {plant_name} in {country}")

                    # If USA plant, use Excel tool for units_id
                    excel_units_used = False
                    if country and ("united states" in country.lower() or "usa" in country.lower() or "us" in country.lower()):
                        print(f"[Session {session_id}] 🇺🇸 USA PLANT DETECTED - Using Excel tool for units_id")

                        try:
                            # Import and use Excel tool
                            import sys
                            sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')
                            from excel_power_plant_tool import ExcelPowerPlantTool

                            # Initialize Excel tool with correct path
                            excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx', session_id)

                            # Get all units for this plant
                            plant_name = normalize_plant_name(plant_name)
                            excel_results = excel_tool.get_plant_data(plant_name)

                            if excel_results:
                                # Extract unique unit IDs from Excel results
                                excel_units = []
                                for result in excel_results:
                                    unit_id = result.get('unit_id')
                                    if unit_id and str(unit_id) not in excel_units:
                                        excel_units.append(str(unit_id))

                                if excel_units:
                                    plant_data["units_id"] = sorted(excel_units, key=lambda x: int(x))
                                    excel_units_used = True
                                    print(f"[Session {session_id}] ✅ Excel tool found units: {plant_data['units_id']}")

                                    # Also update other plant data from Excel if available
                                    first_result = excel_results[0]
                                    if first_result.get('latitude') and first_result.get('longitude'):
                                        plant_data["lat"] = str(first_result['latitude'])
                                        plant_data["long"] = str(first_result['longitude'])
                                        print(f"[Session {session_id}] ✅ Updated coordinates from Excel")
                                else:
                                    print(f"[Session {session_id}] ❌ No valid unit IDs found in Excel results")
                            else:
                                print(f"[Session {session_id}] ❌ No Excel data found for {plant_name}")

                        except Exception as e:
                            print(f"[Session {session_id}] ❌ Excel tool failed: {e}")
                            print(f"[Session {session_id}] 🔄 Falling back to web search unit detection...")

                    # CRITICAL FIX: Check if units_id is empty and extract from research summaries (only if Excel wasn't used)
                    if not excel_units_used and (not plant_data.get("units_id") or plant_data["units_id"] == []):
                        print(f"[Session {session_id}] 🔍 units_id empty, extracting from research summaries...")

                        # Extract units from research summaries using enhanced detection
                        combined_summaries = "\n".join(state.get("web_research_result", []))
                        print(f"[Session {session_id}] 🔍 DEBUG: Research summaries length: {len(combined_summaries)}")
                        print(f"[Session {session_id}] 🔍 DEBUG: First 500 chars: {combined_summaries[:500]}...")
                        extracted_units = enhanced_unit_detection(combined_summaries, session_id)

                        if extracted_units:
                            plant_data["units_id"] = extracted_units
                            print(f"[Session {session_id}] ✅ Extracted units from research: {extracted_units}")
                        else:
                            # Fallback: Use [1] as default if no units found
                            plant_data["units_id"] = ["1"]
                            print(f"[Session {session_id}] ⚠️ No units found in research, using default: ['1']")
                    else:
                        print(f"[Session {session_id}] ✅ units_id already populated: {plant_data['units_id']}")

                    # Enhanced search for PPA and grid connectivity if they're empty
                    research_topic = get_research_topic(state["messages"])
                    plant_name = research_topic.strip()

                    # IMPROVED: Extract PPA and grid data from existing research results first
                    existing_research = state.get("web_research_result", [])
                    existing_sources = state.get("sources_gathered", [])

                    # OPTIMIZED PPA AND GRID CONNECTIVITY EXTRACTION
                    print(f"[Session {session_id}] 🎯 Starting optimized PPA and grid connectivity extraction...")

                    # Extract PPA details using optimized strategy
                    if not plant_data.get("ppa_details") or plant_data["ppa_details"] == []:
                        print(f"[Session {session_id}] 🔍 Extracting PPA details using optimized search strategy...")
                        ppa_details = extract_optimized_ppa_details(plant_name, plant_data.get("country_name", ""), session_id)
                        if ppa_details:
                            plant_data["ppa_details"] = ppa_details
                            print(f"[Session {session_id}] ✅ Extracted {len(ppa_details)} PPA contracts")
                        else:
                            plant_data["ppa_details"] = []
                            print(f"[Session {session_id}] ⚠️ No PPA details found")

                    # Extract grid connectivity using optimized strategy
                    if not plant_data.get("grid_connectivity_maps") or plant_data["grid_connectivity_maps"] == []:
                        print(f"[Session {session_id}] 🔍 Extracting grid connectivity using optimized search strategy...")
                        grid_connectivity = extract_optimized_grid_connectivity(plant_name, plant_data.get("country_name", ""), plant_data, session_id)
                        if grid_connectivity:
                            plant_data["grid_connectivity_maps"] = grid_connectivity
                            print(f"[Session {session_id}] ✅ Extracted {len(grid_connectivity)} grid connections")
                        else:
                            plant_data["grid_connectivity_maps"] = []
                            print(f"[Session {session_id}] ⚠️ No grid connectivity found")



                    # ENHANCEMENT: Search for closure_year, mandatory_closure, and potential_reference
                    print(f"[Session {session_id}] 🔍 Searching for closure_year, mandatory_closure, and potential_reference...")

                    try:
                        # Search for closure year - DIRECT QUESTION
                        closure_query = f'What is the scheduled or officially announced closure year for the {plant_name} coal power plant? Please provide only the year (e.g., 2030). If no closure date has been officially announced, state that clearly.'

                        # Search for mandatory closure - COMPREHENSIVE POLICY SEARCH
                        mandatory_closure_query = f'Does the {plant_name} have a mandatory closure requirement? Indicate whether this is due to: 1. The holding or operating company\'s internal policy, 2. Local or regional government regulation, 3. National government decarbonization or coal phase-out policy. If applicable, mention the source policy or commitment, and whether a specific closure year has been set.'

                        # Search for potential reference - SPECIFIC BENCHMARK PLANT
                        potential_reference_query = f'What is the most similar coal power plant to {plant_name} that could serve as a reference or benchmark? Please provide the name, country, and if available, the exact latitude and longitude coordinates of this reference plant. Focus on plants with similar technology, capacity, and operational characteristics.'

                        # Use existing LLM to search for all information
                        closure_result = llm.invoke(closure_query)
                        mandatory_closure_result = llm.invoke(mandatory_closure_query)
                        potential_reference_result = llm.invoke(potential_reference_query)

                        # Extract closure year (look for 4-digit year)
                        import re
                        closure_text = closure_result.content.lower()
                        year_match = re.search(r'\b(20\d{2})\b', closure_text)
                        if year_match and 'no closure' not in closure_text and 'not announced' not in closure_text:
                            plant_data["closure_year"] = int(year_match.group(1))
                            print(f"[Session {session_id}] ✅ Found closure year: {plant_data['closure_year']}")
                        else:
                            plant_data["closure_year"] = None
                            print(f"[Session {session_id}] ℹ️ No closure year found")

                        # Process mandatory closure (YES/NO only based on policy existence and specific year)
                        mandatory_text = mandatory_closure_result.content.strip().lower()

                        # Check if any policy exists and mentions a specific year
                        has_policy = False
                        has_specific_year = False

                        # Check for policy indicators
                        policy_indicators = [
                            'company policy', 'internal policy', 'holding company',
                            'government regulation', 'local regulation', 'regional regulation',
                            'decarbonization', 'coal phase-out', 'phase out', 'closure policy',
                            'mandatory closure', 'required closure', 'scheduled closure'
                        ]

                        for indicator in policy_indicators:
                            if indicator in mandatory_text:
                                has_policy = True
                                break

                        # Check for specific year mention
                        year_match = re.search(r'\b(20\d{2})\b', mandatory_text)
                        if year_match:
                            has_specific_year = True

                        # Set YES only if policy exists AND specific year is mentioned
                        if has_policy and has_specific_year:
                            plant_data["mandatory_closure"] = "Yes"
                            print(f"[Session {session_id}] ✅ Mandatory closure: Yes (policy with specific year found)")
                        else:
                            plant_data["mandatory_closure"] = "No"
                            if has_policy and not has_specific_year:
                                print(f"[Session {session_id}] ✅ Mandatory closure: No (policy exists but no specific year)")
                            else:
                                print(f"[Session {session_id}] ✅ Mandatory closure: No (no policy or specific year found)")

                        # Process potential reference plant
                        reference_text = potential_reference_result.content.strip()
                        if reference_text and len(reference_text) > 10:
                            # Extract coordinates if available
                            lat_match = re.search(r'latitude[:\s]+(-?\d+\.?\d*)', reference_text, re.IGNORECASE)
                            long_match = re.search(r'longitude[:\s]+(-?\d+\.?\d*)', reference_text, re.IGNORECASE)

                            # Alternative coordinate patterns
                            if not lat_match:
                                lat_match = re.search(r'coordinates[:\s]+(-?\d+\.?\d*)[,\s]+(-?\d+\.?\d*)', reference_text, re.IGNORECASE)

                            # Initialize potential_reference structure
                            if "potential_reference" not in plant_data or not isinstance(plant_data["potential_reference"], dict):
                                plant_data["potential_reference"] = {"lat": None, "long": None}

                            # Set coordinates if found
                            if lat_match:
                                try:
                                    plant_data["potential_reference"]["lat"] = float(lat_match.group(1))
                                except:
                                    plant_data["potential_reference"]["lat"] = None

                            if long_match:
                                try:
                                    plant_data["potential_reference"]["long"] = float(long_match.group(1))
                                except:
                                    plant_data["potential_reference"]["long"] = None

                            print(f"[Session {session_id}] ✅ Found potential reference plant: {reference_text[:100]}...")
                            print(f"[Session {session_id}] 📍 Coordinates: lat={plant_data['potential_reference']['lat']}, long={plant_data['potential_reference']['long']}")
                        else:
                            plant_data["potential_reference"] = {"lat": None, "long": None}
                            print(f"[Session {session_id}] ℹ️ No potential reference plant found")

                    except Exception as e:
                        print(f"[Session {session_id}] ❌ Error searching for plant information: {e}")
                        plant_data["closure_year"] = None
                        plant_data["mandatory_closure"] = "No"  # Default to "No" instead of None
                        plant_data["potential_reference"] = {"lat": None, "long": None}

                    # Process and normalize plant data using UIDs defined at function scope
                    ordered_plant_data = process_plant_data_formatting(plant_data, session_id, org_id, plant_id)
                    
                    print(f"✅ Plant data processing completed with proper formatting")
                    
                    # Update the final content with the modified JSON
                    updated_json = json.dumps(ordered_plant_data, indent=2)
                    print(f"[Session {session_id}] 🔄 REPLACING JSON IN FINAL CONTENT")
                    print(f"[Session {session_id}] 📊 FORMATTED JSON: {updated_json[:200]}...")
                    final_content = final_content[:start_idx] + updated_json + final_content[end_idx:]
                    print(f"[Session {session_id}] ✅ JSON replacement completed")

            except Exception as e:
                print(f"[Session {session_id}] ⚠️ Could not add image URLs to plant JSON: {e}")

            # Add a prefix to indicate this is plant-level information
            final_content = f"PLANT-LEVEL INFORMATION:\n{final_content}"
            
            print("Successfully generated plant-level information")
            
        except Exception as e:
            import traceback
            print(f"Error in plant-level generation: {str(e)}")
            print(traceback.format_exc())
            
            # Fallback plant-level response
            plant_name = research_topic.strip()
            plant_id = get_next_plant_id()
            final_content = f"""PLANT-LEVEL INFORMATION:
{{
  "sk": "plant#coal#{plant_id}",
  "name": "{plant_name}",
  "plant_id": {plant_id},
  "plant_type": "coal",
  "lat": null,
  "long": null,
  "plant_address": "",
  "units_id": [],
  "grid_connectivity_maps": [],
  "ppa_details": [],
  "plant_images": [],
  "pk": "ORG_XX_XXXXXX_XXXXXXXX",
  "closure_year": null,
  "mandatory_closure": "No",
  "potential_reference": {{
    "lat": null,
    "long": null
  }},
  "s3_url": null
}}"""

        # Initialize S3 state variables
        current_s3_urls = state.get("s3_json_urls", {})
        current_storage_status = state.get("json_storage_complete", {})
        current_errors = state.get("json_storage_errors", [])
        
        # SAVE PLANT JSON TO S3
        try:
            session_id = state.get("session_id", "unknown")
            plant_name = state.get("plant_name_for_s3", research_topic)
            
            # Extract JSON from the response
            import json
            start_idx = final_content.find('{')
            end_idx = final_content.rfind('}') + 1
            if start_idx != -1 and end_idx != -1:
                json_str = final_content[start_idx:end_idx]
                plant_data = json.loads(json_str)
                
                # Set pk field using UIDs already retrieved at function scope
                if plant_id:
                    plant_data["pk"] = plant_id
                    print(f"[Session {session_id}] ✅ Set plant pk field: {plant_id}")
                else:
                    print(f"[Session {session_id}] ⚠️ No plant_id available for pk field")

                # Process plant data formatting (no UID parameters needed)
                formatted_plant_data = process_plant_data_formatting(plant_data, session_id, org_id, plant_id)
                plant_s3_url = store_plant_data(formatted_plant_data, plant_name, session_id, org_id=org_id, plant_id=plant_id)  # plant_id here is UUID for folder structure
                
                if plant_s3_url:
                    print(f"[Session {session_id}] ✅ Plant JSON saved to S3: {plant_s3_url}")
                    # Update S3 URLs in state
                    current_s3_urls = state.get("s3_json_urls", {})
                    current_s3_urls["plant"] = plant_s3_url
                    
                    current_storage_status = state.get("json_storage_complete", {})
                    current_storage_status["plant"] = True
                else:
                    print(f"[Session {session_id}] ❌ Failed to save plant JSON to S3")
                    current_errors = state.get("json_storage_errors", [])
                    current_errors.append("Failed to save plant JSON to S3")
            else:
                print(f"[Session {session_id}] ⚠️ Could not extract JSON from plant response")
                current_errors = state.get("json_storage_errors", [])
                current_errors.append("Could not extract JSON from plant response")
                
        except Exception as s3_error:
            print(f"[Session {session_id}] ❌ S3 storage error for plant: {str(s3_error)}")
            current_errors = state.get("json_storage_errors", [])
            current_errors.append(f"S3 plant storage error: {str(s3_error)}")

        # Filter sources that were actually used in the response
        unique_sources = []
        for source in state.get("sources_gathered", []):
            if isinstance(source, dict):
                if ("short_url" in source and source["short_url"] in final_content) or \
                   ("value" in source and source["value"] in final_content):
                    unique_sources.append(source)
            elif hasattr(source, "short_url") and source.short_url in final_content:
                unique_sources.append(source)
            elif hasattr(source, "value") and source.value in final_content:
                unique_sources.append(source)

        # FINAL STEP: Ensure final_content always contains properly formatted JSON
        try:
            if 'formatted_plant_data' in locals():
                # Extract any existing text before JSON
                json_start = final_content.find('{')
                prefix = final_content[:json_start] if json_start > 0 else "PLANT-LEVEL INFORMATION:\n"
                
                # Replace with properly formatted JSON
                formatted_json = json.dumps(formatted_plant_data, indent=2)
                final_content = prefix + formatted_json
                print(f"[Session {session_id}] 🔧 FINAL: Ensured final_content contains formatted JSON")
        except Exception as e:
            print(f"[Session {session_id}] ⚠️ Could not update final_content with formatted JSON: {e}")

        # Return plant-level results with S3 updates
        # CRITICAL FIX: Use original plant_id (UUID) from database, not from formatted_plant_data
        # After our changes, formatted_plant_data["plant_id"] contains sequential number, not UUID
        plant_uuid = plant_id  # Use the UUID from database lookup (line 1815)
        return {
            "messages": [AIMessage(content=final_content)],
            "sources_gathered": unique_sources,
            "s3_json_urls": current_s3_urls,
            "json_storage_complete": current_storage_status,
            "json_storage_errors": current_errors,
            "plant_uid": plant_uuid,  # Store plant_uid (UUID) for unit processing
        }
    
    else:
        # Unit-level information extraction (search_phase == 3)
        unit_number = state.get("unit_number", "1")

        # Get actual currency for this plant
        plant_name = get_research_topic(state.get("messages", []))
        actual_currency = get_currency_from_organization(plant_name)
        print(f"[Session {session_id}] 💱 Using currency for unit level: {actual_currency}")

        unit_prompt = f"""Extract and format comprehensive UNIT-LEVEL technical information about Unit {unit_number} of the power plant from the research summaries.

Instructions:
- The current date is {current_date}.
- Extract detailed technical information for Unit {unit_number} of the power plant.
- Extract the following unit-level fields:

UNIT SPECIFICATIONS:
  1. plant_id: Unique plant identifier (inherit from plant level)
  2. unit_number: Unit number labeling
  3. capacity: Unit-wise installed capacity (MW)
  4. capacity_unit: Unit for capacity (typically 'MW')
  5. technology: Specific technology (Ultra Super Critical, Super Critical, Critical, Sub-critical for coal; Single/Open Cycle, Combined/Closed Cycle for gas)
  6. boiler_type: Type of boiler used in the unit
  7. commencement_date: Commercial operation date (yyyy-mm-ddThh:mm:ss.msZ format)

OPERATIONAL PERFORMANCE:
  8. plf: Array of Plant Load Factor by year [{{ "value": "PLF percentage", "year": "year" }}]
  9. PAF: Array of Plant Availability Factor by year [{{ "value": "PAF percentage", "year": "year" }}]
  10. coal_unit_efficiency: Unit specific efficiency (%)
  11. auxiliary_power_consumed: Array of AUX energy consumption by year [{{ "value": "percentage", "year": "year" }}]
  12. gross_power_generation: Array of total energy generated by year [{{ "value": "generation", "year": "year" }}]

TECHNICAL EFFICIENCY:
  13. heat_rate: Station Heat Rate (kJ/kWh or kcal/kWh)
  14. heat_rate_unit: Unit for heat rate measurement
  15. emission_factor: Array of CO2 emissions by year [{{ "value": "kg CO2e/kWh", "year": "year" }}]

FUEL SPECIFICATIONS:
  16. fuel_type: Array of COAL fuel sources ONLY with percentages by year [{{ "fuel": "Coal", "type": "bituminous/sub-bituminous/lignite/anthracite", "years_percentage": {{"year": "percentage"}} }}] - EXCLUDE biomass details

  19. gcv_coal: Gross calorific value of coal (kCal/kg) - country-specific
  20. gcv_coal_unit: Unit for GCV coal (kCal/kg)
  21. gcv_natural_gas: GCV of natural gas (10000) - country-specific
  22. gcv_natural_gas_unit: Unit for GCV natural gas (kcal/scm)
  23. gcv_biomass: GCV of biomass (kCal/kg) - country-specific
  24. gcv_biomass_unit: Unit for GCV biomass (kCal/kg)

COUNTRY-SPECIFIC EFFICIENCY DATA:
  25. closed_cylce_gas_turbine_efficency: CCGT efficiency for the country (%)
  26. open_cycle_gas_turbine_efficency: OCGT efficiency for the country (%)  
  27. combined_cycle_heat_rate: CCGT heat rate for the country
  28. open_cycle_heat_rate: OCGT heat rate for the country

CONVERSION ECONOMICS:
  29. capex_required_retrofit_biomass: CAPEX for biomass cofiring retrofit (Million {actual_currency})
  30. capex_required_retrofit_biomass_unit: Unit for retrofit CAPEX (Million {actual_currency})
  31. capex_required_renovation_open_cycle: CAPEX for conversion to OCGT ({actual_currency}/MW)
  32. capex_required_renovation_open_cycle_unit: Unit for open cycle renovation ({actual_currency}/MW)
  33. capex_required_renovation_closed_cycle: CAPEX for conversion to CCGT ({actual_currency}/MW)
  34. capex_required_renovation_closed_cycle_unit: Unit for closed cycle renovation ({actual_currency}/MW)
  35. efficiency_loss_biomass_cofiring: Efficiency reduction from biomass cofiring (%)

LIFECYCLE INFORMATION:
  36. unit_lifetime: Total operational lifetime (years)
  37. remaining_useful_life: End-of-life date (yyyy-mm-ddThh:mm:ss.msZ format) - PRIORITY: Look for retirement schedules, decommissioning dates, closure plans, asset retirement obligations, phase-out timelines
  38. unit: Unit efficiency measurement in percentage (%)



- Format your response ONLY as a clean JSON object with these exact field names.
- Do not include any explanatory text before or after the JSON.
- If any information is not available in the summaries, use null for missing fields, empty strings for unavailable text, or empty arrays for lists.
- Ensure the JSON is properly formatted and valid.
- For array fields with year-based data, provide multiple entries if available from different years.

Example format for unit {unit_number}:
```json
{{
  "plant_id": "Unique plant identifier",
  "unit_number": "{unit_number}",
  "capacity": "250",
  "capacity_unit": "MW",
  "technology": "Super Critical",
  "boiler_type": "Circulating Fluidized Bed",
  "commencement_date": "2012-01-01T00:00:00.000Z",
  "plf": [
    {{ "value": "75.5", "year": "2023" }},
    {{ "value": "73.2", "year": "2022" }}
  ],
  "PAF": [
    {{ "value": "85.2", "year": "2023" }},
    {{ "value": "82.1", "year": "2022" }}
  ],
  "coal_unit_efficiency": "35.5",
  "auxiliary_power_consumed": [
    {{ "value": "6.2", "year": "2023" }},
    {{ "value": "6.5", "year": "2022" }}
  ],
  "gross_power_generation": [
    {{ "value": "1650000", "year": "2023" }},
    {{ "value": "1580000", "year": "2022" }}
  ],
  "heat_rate": "2450",
  "heat_rate_unit": "kcal/kWh",
  "emission_factor": [
    {{ "value": "0.82", "year": "2023" }},
    {{ "value": "0.85", "year": "2022" }}
  ],
  "fuel_type": [
    {{
      "fuel": "Coal",
      "type": "bituminous",
      "years_percentage": {{
        "2023": "95",
        "2022": "97"
      }}
    }}
  ],

  "gcv_coal": "4200",
  "gcv_coal_unit": "kCal/kg",
  "gcv_natural_gas": "10000",
  "gcv_natural_gas_unit": "kcal/scm",
  "gcv_biomass": "Not available",
  "gcv_biomass_unit": "kCal/kg",
  "closed_cylce_gas_turbine_efficency": "Not available",
  "open_cycle_gas_turbine_efficency": "Not available",
  "combined_cycle_heat_rate": "Not available",
  "open_cycle_heat_rate": "Not available",
  "capex_required_retrofit_biomass": "Not available",
  "capex_required_retrofit_biomass_unit": "Not available",
  "capex_required_renovation_open_cycle": "Not available", 
  "capex_required_renovation_open_cycle_unit": "Not available",
  "capex_required_renovation_closed_cycle": "Not available",
  "capex_required_renovation_closed_cycle_unit": "Not available",
  "efficiency_loss_biomass_cofiring": "Not available",
  "unit_lifetime": "25",
  "remaining_useful_life": "2037-01-01T00:00:00.000Z",


  "pk": "ORG_XX_XXXXXX_XXXXXXXX",
  "annual_operational_hours": "default null",
  "blending_percentage_of_biomass": "default null",
  "capex_required_renovation": "default null",
  "capex_required_renovation_unit": "default null",
  "emission_factor_coal": "default null",
  "emission_factor_gas": "default null",
  "emission_factor_of_gas_unit": "kgCO2e/kg of fuel",
  "emission_factor_unit": "default null",
  "fgds_status": "default null",
  "ramp_down_rate": "default null",
  "ramp_up_rate": "default null"
}}
```

Research Topic: {research_topic}
Unit Number: {unit_number}

Summaries:
{summaries}
"""
        try:
            # Generate unit-level information
            result = llm.invoke(unit_prompt)
            final_content = result.content
            
            # Add a prefix to indicate this is unit-level information
            final_content = f"UNIT-{unit_number} INFORMATION:\n{final_content}"
            
            print(f"Successfully generated unit-level information for Unit {unit_number}")
            
        except Exception as e:
            import traceback
            print(f"Error in unit-level generation: {str(e)}")
            print(traceback.format_exc())
            
            # Fallback unit-level response
            final_content = f"""UNIT-{unit_number} INFORMATION:
{{
  "plant_id": "Not available",
  "unit_number": "{unit_number}",
  "capacity": "Not available",
  "capacity_unit": "MW",
  "technology": "Not available",
  "boiler_type": "Not available",
  "commencement_date": "Not available",
  "plf": [],
  "PAF": [],
  "coal_unit_efficiency": "Not available",
  "auxiliary_power_consumed": [],
  "gross_power_generation": [],
  "heat_rate": "Not available",
  "heat_rate_unit": "Not available",
  "emission_factor": [],
  "fuel_type": [],

  "gcv_coal": "Not available",
  "gcv_coal_unit": "Not available",
  "gcv_natural_gas": "Not available",
  "gcv_natural_gas_unit": "Not available",
  "gcv_biomass": "Not available",
  "gcv_biomass_unit": "kCal/kg",
  "closed_cylce_gas_turbine_efficency": "Not available",
  "open_cycle_gas_turbine_efficency": "Not available",
  "combined_cycle_heat_rate": "Not available",
  "open_cycle_heat_rate": "Not available",
  "capex_required_retrofit_biomass": "Not available",
  "capex_required_retrofit_biomass_unit": "Not available",
  "capex_required_renovation_open_cycle": "Not available",
  "capex_required_renovation_open_cycle_unit": "Not available", 
  "capex_required_renovation_closed_cycle": "Not available",
  "capex_required_renovation_closed_cycle_unit": "Not available",
  "efficiency_loss_biomass_cofiring": "Not available",
  "unit_lifetime": "Not available",
  "remaining_useful_life": "Not available",


  "pk": "ORG_XX_XXXXXX_XXXXXXXX",
  "annual_operational_hours": "default null",
  "blending_percentage_of_biomass": "default null",
  "capex_required_renovation": "default null",
  "capex_required_renovation_unit": "default null",
  "emission_factor_coal": "default null",
  "emission_factor_gas": "default null",
  "emission_factor_of_gas_unit": "kgCO2e/kg of fuel",
  "emission_factor_unit": "default null",
  "fgds_status": "default null",
  "ramp_down_rate": "default null",
  "ramp_up_rate": "default null"
}}"""

        # Filter sources that were actually used in the response
        unique_sources = []
        for source in state.get("sources_gathered", []):
            if isinstance(source, dict):
                if ("short_url" in source and source["short_url"] in final_content) or \
                   ("value" in source and source["value"] in final_content):
                    unique_sources.append(source)
            elif hasattr(source, "short_url") and source.short_url in final_content:
                unique_sources.append(source)
            elif hasattr(source, "value") and source.value in final_content:
                unique_sources.append(source)

        # Store unit results for multi-unit tracking
        unit_result = {
            "unit_number": unit_number,
            "content": final_content,
            "sources": unique_sources
        }
        
        # Accumulate unit results - preserve any existing results
        existing_unit_results = state.get("unit_results", [])
        accumulated_unit_results = existing_unit_results + [unit_result]
        
        print(f"[Unit {unit_number}] Added unit result. Total unit results now: {len(accumulated_unit_results)}")
        
        # Initialize S3 state variables
        current_s3_urls = state.get("s3_json_urls", {})
        current_storage_status = state.get("json_storage_complete", {})
        current_errors = state.get("json_storage_errors", [])
        
        # SAVE UNIT JSON TO S3
        try:
            session_id = state.get("session_id", "unknown")
            plant_name = state.get("plant_name_for_s3", get_research_topic(state.get("messages", [])))
            
            # Extract JSON from the response
            import json
            start_idx = final_content.find('{')
            end_idx = final_content.rfind('}') + 1
            if start_idx != -1 and end_idx != -1:
                json_str = final_content[start_idx:end_idx]
                unit_data = json.loads(json_str)
                
                # SIMPLE DATABASE LOOKUP: Get plant_id for unit pk field
                plant_id = ""
                org_id = ""

                try:
                    from agent.database_manager import get_database_manager
                    db_manager = get_database_manager()

                    # Get plant_id from database (units use plant_id as pk)
                    existing_plant = db_manager.check_plant_exists(plant_name)
                    if existing_plant:
                        plant_id = existing_plant.get("plant_id", "")
                        org_id = existing_plant.get("org_id", "")
                        print(f"[Session {session_id}] ✅ Retrieved for unit {unit_number}: plant_id='{plant_id}'")
                    else:
                        print(f"[Session {session_id}] ⚠️ Plant not found in database for unit: {plant_name}")

                except Exception as e:
                    print(f"[Session {session_id}] ❌ Database lookup failed for unit: {e}")

                # Set pk field from database lookup (units use plant_id as pk)
                if plant_id:
                    unit_data["pk"] = plant_id
                    print(f"[Session {session_id}] ✅ Set unit {unit_number} pk field: {plant_id}")
                else:
                    print(f"[Session {session_id}] ⚠️ No plant_id available for unit {unit_number} pk field")

                # Format unit data (no UID parameters needed)
                plant_uid = state.get("plant_uid", "")  # Get UUID from state for folder structure
                print(f"[Session {session_id}] 🔍 DEBUG UNIT STORAGE: plant_uid from state = '{plant_uid}'")
                print(f"[Session {session_id}] 🔍 DEBUG UNIT STORAGE: org_id = '{org_id}'")
                formatted_unit_data = process_unit_data_formatting(unit_data, session_id, plant_uid)
                unit_s3_url = store_unit_data(formatted_unit_data, plant_name, unit_number, session_id, org_id=org_id, plant_id=plant_uid)
                
                if unit_s3_url:
                    print(f"[Session {session_id}] ✅ Unit {unit_number} JSON saved to S3: {unit_s3_url}")
                    # Update S3 URLs in state
                    current_s3_urls = state.get("s3_json_urls", {})
                    if "units" not in current_s3_urls:
                        current_s3_urls["units"] = {}
                    current_s3_urls["units"][unit_number] = unit_s3_url
                    
                    current_storage_status = state.get("json_storage_complete", {})
                    if "units" not in current_storage_status:
                        current_storage_status["units"] = {}
                    current_storage_status["units"][unit_number] = True
                else:
                    print(f"[Session {session_id}] ❌ Failed to save Unit {unit_number} JSON to S3")
                    current_errors = state.get("json_storage_errors", [])
                    current_errors.append(f"Failed to save Unit {unit_number} JSON to S3")
            else:
                print(f"[Session {session_id}] ⚠️ Could not extract JSON from Unit {unit_number} response")
                current_errors = state.get("json_storage_errors", [])
                current_errors.append(f"Could not extract JSON from Unit {unit_number} response")
                
        except Exception as s3_error:
            print(f"[Session {session_id}] ❌ S3 storage error for Unit {unit_number}: {str(s3_error)}")
            current_errors = state.get("json_storage_errors", [])
            current_errors.append(f"S3 Unit {unit_number} storage error: {str(s3_error)}")
        
        # Return unit-level results with FULL state preservation and S3 updates
        return {
            "messages": [AIMessage(content=final_content)],
            "sources_gathered": unique_sources,
            "unit_results": accumulated_unit_results,  # Accumulate all unit results
            
            # PRESERVE MULTI-UNIT STATE - CRITICAL!
            "remaining_units": state.get("remaining_units", []),
            "current_unit": state.get("current_unit", unit_number),
            "all_units": state.get("all_units", []),
            "search_phase": state.get("search_phase", 3),
            "unit_number": unit_number,
            
            # S3 JSON Storage updates
            "s3_json_urls": current_s3_urls,
            "json_storage_complete": current_storage_status,
            "json_storage_errors": current_errors,
        }

def unit_finalize_answer_matrix(state: OverallState, config: RunnableConfig):
    """Unit-specific finalize function that only updates the unit's matrix slot."""
    unit_number = state.get("unit_number", "1")
    session_id = state.get("session_id", "unknown")
    my_matrix_slot = state.get("my_matrix_slot", unit_number)
    
    print(f"[Session {session_id}] 🏁 FINALIZING UNIT {unit_number} (MATRIX SLOT: {my_matrix_slot})")
    
    # Call the original finalize_answer to get the content
    finalize_result = finalize_answer({**state, "search_phase": 3}, config)
    
    # Extract the generated content and sources
    unit_content = ""
    unit_sources = []
    
    if "messages" in finalize_result and finalize_result["messages"]:
        unit_content = finalize_result["messages"][0].content
    
    if "sources_gathered" in finalize_result:
        unit_sources = finalize_result["sources_gathered"]
    
    # Update ONLY this unit's matrix slot
    matrix_update = {
        my_matrix_slot: {
            'status': 'completed',
            'processing_complete': True,
            'research_results': unit_content,
            'sources_gathered': unit_sources,
            'unit_specific_data': {
                'content': unit_content,
                'sources_count': len(unit_sources)
            }
        }
    }
    
    completion_update = {
        f'unit_{unit_number}_complete': True
    }
    # SAVE UNIT JSON TO S3 (Matrix-aware version)
    unit_s3_url = None
    try:
        plant_name = state.get("plant_name_for_s3", get_research_topic(state.get("messages", [])))
        
        # Extract JSON from the unit content using proper bracket matching
        import json

        def extract_json_object(text):
            start_idx = text.find('{')
            if start_idx == -1:
                return None

            bracket_count = 0
            for i, char in enumerate(text[start_idx:], start_idx):
                if char == '{':
                    bracket_count += 1
                elif char == '}':
                    bracket_count -= 1
                    if bracket_count == 0:
                        return text[start_idx:i+1]
            return None

        json_str = extract_json_object(unit_content)
        if json_str:
            unit_data = json.loads(json_str)
            
            # SIMPLE DATABASE LOOKUP: Get plant_id for unit pk field
            plant_id = ""
            org_id = ""

            try:
                from agent.database_manager import get_database_manager
                db_manager = get_database_manager()

                # Get plant_id from database (units use plant_id as pk)
                existing_plant = db_manager.check_plant_exists(plant_name)
                if existing_plant:
                    plant_id = existing_plant.get("plant_id", "")
                    org_id = existing_plant.get("org_id", "")
                    print(f"[Session {session_id}] ✅ Retrieved for unit {unit_number} (Matrix): plant_id='{plant_id}'")
                else:
                    print(f"[Session {session_id}] ⚠️ Plant not found in database for unit (Matrix): {plant_name}")

            except Exception as e:
                print(f"[Session {session_id}] ❌ Database lookup failed for unit (Matrix): {e}")

            # Set pk field from database lookup (units use plant_id as pk)
            if plant_id:
                unit_data["pk"] = plant_id
                print(f"[Session {session_id}] ✅ Set unit {unit_number} pk field (Matrix): {plant_id}")
            else:
                print(f"[Session {session_id}] ⚠️ No plant_id available for unit {unit_number} pk field (Matrix)")

            # Format unit data (no UID parameters needed)
            plant_uid = state.get("plant_uid", "")  # Get UUID from state for folder structure
            print(f"[Session {session_id}] 🔍 DEBUG MATRIX UNIT STORAGE: plant_uid from state = '{plant_uid}'")
            print(f"[Session {session_id}] 🔍 DEBUG MATRIX UNIT STORAGE: org_id = '{org_id}'")
            print(f"[Session {session_id}] 🔍 DEBUG MATRIX UNIT STORAGE: state keys = {list(state.keys())}")
            formatted_unit_data = process_unit_data_formatting(unit_data, session_id, plant_uid)
            unit_s3_url = store_unit_data(formatted_unit_data, plant_name, unit_number, session_id, org_id=org_id, plant_id=plant_uid)
            
            if unit_s3_url:
                print(f"[Session {session_id}] ✅ Unit {unit_number} JSON saved to S3 (Matrix): {unit_s3_url}")
            else:
                print(f"[Session {session_id}] ❌ Failed to save Unit {unit_number} JSON to S3 (Matrix)")
        else:
            print(f"[Session {session_id}] ⚠️ Could not extract JSON from Unit {unit_number} response (Matrix)")
            
    except Exception as s3_error:
        print(f"[Session {session_id}] ❌ S3 storage error for Unit {unit_number} (Matrix): {str(s3_error)}")
    
    print(f"[Session {session_id}] ✅ Unit {unit_number} finalized and stored in matrix slot '{my_matrix_slot}'")
    
    # Prepare S3 URL updates for the state
    s3_updates = {}
    if unit_s3_url:
        current_s3_urls = state.get("s3_json_urls", {})
        if "units" not in current_s3_urls:
            current_s3_urls["units"] = {}
        current_s3_urls["units"][unit_number] = unit_s3_url
        s3_updates["s3_json_urls"] = current_s3_urls
        
        current_storage_status = state.get("json_storage_complete", {})
        if "units" not in current_storage_status:
            current_storage_status["units"] = {}
        current_storage_status["units"][unit_number] = True
        s3_updates["json_storage_complete"] = current_storage_status
    
    
    # Return ONLY the matrix and completion updates - no shared state conflicts
    return {
        "unit_state_matrix": matrix_update,
        "unit_completion_status": completion_update,
        # Include the messages for the final collection step
        "messages": finalize_result.get("messages", []),
        "sources_gathered": unit_sources,
        # Include S3 updates if successful
        **s3_updates
    }

# PARALLEL PROCESSING FUNCTION (for image extraction) - defined before use
def spawn_parallel_processing_with_uid(state: OverallState):
    """Spawn parallel processing: main research flow + image extraction (with UID available)"""
    session_id = state.get("session_id", "unknown")
    org_id = state.get("org_id", "")
    plant_id = state.get("plant_uid", "")  # Get UUID for parallel processing

    print(f"[Session {session_id}] 🚀 Spawning parallel processing with UIDs:")
    print(f"[Session {session_id}] 🏢 org_id: {org_id}")
    print(f"[Session {session_id}] 🏭 plant_id: {plant_id}")

    # CRITICAL FIX: Always do full 3-level extraction, but ensure UIDs are preserved
    # Create state with UIDs explicitly preserved for all phases
    state_with_uids = {
        **state,
        "org_id": org_id,      # Ensure org_id persists
        "plant_uid": plant_id,  # Store UUID as plant_uid (for folder structure)
        "uids_from_database": True  # Flag to indicate UIDs are from DB
    }

    return [
        Send("org_generate_query", state_with_uids),  # Full 3-level extraction with preserved UIDs
        Send("extract_images_parallel", state_with_uids)  # Parallel image extraction
    ]

# Create our Agent Graph
builder = StateGraph(OverallState, config_schema=Configuration)

# Add registry nodes (new - before existing pipeline)
builder.add_node("check_plant_registry", check_plant_registry)
builder.add_node("quick_org_discovery", quick_org_discovery_node)
builder.add_node("generate_uid", generate_uid_node)
# REMOVED: trigger_financial_pipeline node no longer needed
builder.add_node("populate_database_async", populate_database_async_node)
builder.add_node("entity_extraction_trigger", entity_extraction_trigger_node)
# Add a wrapper node that triggers parallel processing
def start_parallel_processing_node(state: OverallState):
    """Node wrapper that triggers parallel processing"""
    session_id = state.get("session_id", "unknown")
    org_id = state.get("org_id", "")
    print(f"[Session {session_id}] 🚀 Starting parallel processing with UID: {org_id}")

    # Return state update (nodes must return dict)
    return {
        "parallel_processing_started": True
    }

builder.add_node("spawn_parallel_processing_with_uid", start_parallel_processing_node)

# Add session initialization node
builder.add_node("initialize_session", initialize_session)

# Add parallel image extraction node
builder.add_node("extract_images_parallel", extract_images_parallel)

# Define organization-level research nodes
builder.add_node("org_generate_query", lambda state, config: generate_query({**state, "search_phase": 1}, config))
builder.add_node("org_web_research", web_research)
builder.add_node("org_reflection", reflection)
builder.add_node("org_finalize_answer", lambda state, config: finalize_answer({**state, "search_phase": 1}, config))

# Define plant-level research nodes
builder.add_node("plant_generate_query", lambda state, config: generate_query({**state, "search_phase": 2}, config))
builder.add_node("plant_web_research", web_research)
builder.add_node("plant_reflection", reflection)
def plant_finalize_answer_debug(state, config):
    session_id = state.get("session_id", "unknown")
    print(f"[Session {session_id}] 🔍 DEBUG: plant_finalize_answer called")
    result = finalize_answer({**state, "search_phase": 2}, config)
    print(f"[Session {session_id}] 🔍 DEBUG: plant_finalize_answer completed, should go to process_all_units next")
    return result

builder.add_node("plant_finalize_answer", plant_finalize_answer_debug)

# Define unit-level research nodes (with matrix approach)
builder.add_node("unit_generate_query", lambda state, config: generate_query({**state, "search_phase": 3}, config))
builder.add_node("unit_web_research", web_research)
builder.add_node("unit_reflection", reflection)
builder.add_node("unit_finalize_answer", unit_finalize_answer_matrix)  # Use matrix-aware function

# Define a clear state transition node between organization and plant level
def clear_state_for_plant_level(state: OverallState) -> OverallState:
    """Clear the state for plant-level research after organization-level is complete.
    
    This node is called after organization-level research is complete.
    It resets the research state for plant-level research while preserving messages and session info.
    """
    session_id = state.get("session_id", "unknown")
    print(f"[Session {session_id}] ===== STATE CLEARING FOR PLANT LEVEL =====")
    
    # Log current state before clearing
    current_research_topic = get_research_topic(state.get("messages", []))
    print(f"[Session {session_id}] Current research topic: '{current_research_topic}'")
    print(f"[Session {session_id}] Current search_query: {state.get('search_query', [])}")
    print(f"[Session {session_id}] Current web_research_result count: {len(state.get('web_research_result', []))}")
    print(f"[Session {session_id}] Current sources_gathered count: {len(state.get('sources_gathered', []))}")
    
    # Check for contamination in current state
    search_queries = state.get('search_query', [])
    web_results = state.get('web_research_result', [])
    
    for query in search_queries:
        if "jhajjar" in str(query).lower():
            print(f"[Session {session_id}] ⚠️  FOUND JHAJJAR in search_query: {query}")
    
    for result in web_results:
        if "jhajjar" in str(result).lower():
            print(f"[Session {session_id}] ⚠️  FOUND JHAJJAR in web_research_result")
    
    # Preserve messages, session information, and CRITICAL UIDs
    messages = state.get("messages", [])
    session_id = state.get("session_id", "")
    initial_search_query_count = state.get("initial_search_query_count", 5)
    max_research_loops = state.get("max_research_loops", 3)
    reasoning_model = state.get("reasoning_model", "")

    # UIDs no longer need state preservation - will be retrieved from database when needed
    print(f"[Session {session_id}] 🔑 UIDs will be retrieved from database when needed")
    
    # No progress messages in UI - keep messages clean
    
    # Generate a NEW session ID for plant level to ensure complete isolation
    new_session_id = f"plant-{uuid.uuid4().hex[:8]}"  
    print(f"[Session {session_id}] Creating NEW session for plant level: {new_session_id}")
    
    # Return a completely clean state for plant-level research but preserve UIDs
    new_state = {
        "messages": messages,  # Keep only the messages
        "session_id": new_session_id,  # NEW session ID for complete isolation
        "search_phase": 2,  # Set to plant-level phase
        "research_loop_count": 0,  # Reset research loop count
        "web_research_result": [],  # Clear previous research results
        "search_query": [],  # Clear previous search queries
        "sources_gathered": [],  # Clear previous sources
        "org_level_complete": True,  # Mark organization-level as complete
        "initial_search_query_count": initial_search_query_count,
        "max_research_loops": max_research_loops,
        # CRITICAL: Preserve UIDs from previous state
        "org_id": state.get("org_id", ""),  # Preserve org UID
        "plant_uid": state.get("plant_uid", ""),  # Preserve plant UID
        "reasoning_model": reasoning_model,
        "continue_research": False,
        "phase_complete": False,
        # UIDs will be retrieved from database when needed - no state preservation required
    }
    
    print(f"[Session {new_session_id}] *** PLANT LEVEL STATE CREATED - COMPLETELY CLEAN ***")
    print(f"[Session {new_session_id}] Research topic after state clearing: '{get_research_topic(messages)}'")
    print(f"[Session {new_session_id}] 🔍 DEBUG: UIDs in new state: org_id='{new_state.get('org_id')}', plant_uid='{new_state.get('plant_uid')}'")

    return new_state

builder.add_node("clear_state_for_plant_level", clear_state_for_plant_level)

# Define unit-level state management functions
def enhanced_unit_detection(text: str, session_id: str) -> List[str]:
    """Enhanced unit detection with original unit name preservation.

    For complex cases like CC1, CC2, CC3, CC4, CC5, Unit 3, Unit 4:
    - Extracts original names: ['CC1', 'CC2', 'CC3', 'CC4', 'CC5', 'Unit 3', 'Unit 4']
    - Maps to sequential numbers: ['1', '2', '3', '4', '5', '6', '7']
    - Stores mapping in state for later use

    Returns:
        List[str]: Sequential unit numbers for processing
    """
    global _current_unit_name_mapping  # Declare global at the top of function

    original_unit_names = []
    units_found = set()

    print(f"[Session {session_id}] 🔍 Enhanced unit detection with name mapping on text length: {len(text)}")

    import re

    # CONSERVATIVE CHECK: Look for explicit single unit mentions first
    single_unit_patterns = [
        r'(?:only|just|single|one)\s+unit',
        r'unit\s+1\s+(?:only|alone)',
        r'(?:has|contains|comprises)\s+(?:only\s+)?(?:one|1)\s+unit',
        r'single\s+(?:generating\s+)?unit',
        r'(?:one|1)\s+(?:coal|thermal|power|generating)\s+unit'
    ]

    text_lower = text.lower()
    for pattern in single_unit_patterns:
        if re.search(pattern, text_lower):
            print(f"[Session {session_id}] 🎯 SINGLE UNIT DETECTED: Pattern '{pattern}' matched")
            print(f"[Session {session_id}] ✅ Conservative result: ['1'] (single unit plant)")
            return ["1"]

    # NEW: Enhanced unit name extraction with original name preservation
    print(f"[Session {session_id}] 🔍 Extracting original unit names...")

    # CRITICAL FIX: PRIORITIZE GEM WIKI DATA for accurate unit information
    gem_wiki_units = []

    # PRIORITY 1: Extract from GEM Wiki data (most accurate for unit specifications)
    try:
        # Look for GEM Wiki references and unit tables (more flexible detection)
        gem_wiki_indicators = ['gem.wiki', 'global energy monitor', 'gem wiki', 'globalenergymonitor']
        has_gem_wiki = any(indicator in text.lower() for indicator in gem_wiki_indicators)

        # Also check for typical GEMWiki unit table patterns even without explicit mention
        has_unit_table = bool(re.search(r'Unit\s+\d+.*?\d+\s*MW', text, re.IGNORECASE))

        if has_gem_wiki or has_unit_table:
            print(f"[Session {session_id}] 🎯 GEM WIKI DATA DETECTED - Prioritizing for unit extraction")
            print(f"[Session {session_id}] 🔍 GEM Wiki indicators: {has_gem_wiki}, Unit table: {has_unit_table}")

            # Look for unit table patterns from GEM Wiki and other sources
            unit_table_patterns = [
                r'Unit\s+(\d+).*?(\d+)\s*MW.*?supercritical',
                r'Unit\s+(\d+).*?(\d+)\s*MW.*?coal',
                r'Unit\s+(\d+).*?Operating.*?(\d+)',
                r'Unit\s+name.*?Unit\s+(\d+)',
                r'(\d+)\s+x\s+(\d+)\s*MW',  # Pattern like "7 x 600 MW"
                r'Unit\s+(\d+)',  # Simple "Unit 1", "Unit 2" pattern
                r'(\d+)\s+units.*?(\d+)\s*MW',  # "6 units of 600 MW"
                r'(\d+)\s*MW\s+Unit\s+(\d+)',  # "600 MW Unit 1"
                r'Unit\s+(\d+).*?(\d+)\s*MW',  # General "Unit X ... Y MW"
            ]

            for pattern in unit_table_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    print(f"[Session {session_id}] 🎯 GEM Wiki pattern matched: {pattern} → {matches}")

                    # Handle "7 x 600 MW" pattern
                    if 'x' in pattern:
                        for match in matches:
                            if len(match) == 2:
                                num_units, capacity = match
                                try:
                                    unit_count = int(num_units)
                                    gem_wiki_units = [str(i) for i in range(1, unit_count + 1)]
                                    print(f"[Session {session_id}] 🎯 GEM Wiki: {unit_count} x {capacity}MW units → {gem_wiki_units}")
                                    break
                                except ValueError:
                                    continue
                    else:
                        # Handle individual unit patterns
                        for match in matches:
                            if isinstance(match, tuple) and len(match) >= 1:
                                unit_num = match[0]
                                if unit_num.isdigit():
                                    gem_wiki_units.append(unit_num)

                    if gem_wiki_units:
                        break

            # If we found GEM Wiki units, use them as the authoritative source
            if gem_wiki_units:
                print(f"[Session {session_id}] ✅ GEM WIKI AUTHORITATIVE UNITS: {gem_wiki_units}")
                # Create mapping for original names
                _current_unit_name_mapping = {i+1: f"Unit {unit}" for i, unit in enumerate(gem_wiki_units)}
                return gem_wiki_units

    except Exception as e:
        print(f"[Session {session_id}] ⚠️ GEM Wiki extraction error: {e}")

    # FALLBACK: Try unit extraction from any text (not just GEMWiki)
    if not gem_wiki_units:
        print(f"[Session {session_id}] 🔍 No GEMWiki units found, trying general unit extraction...")

        # General unit patterns that work on any text
        general_unit_patterns = [
            r'Unit\s+(\d+)',  # Simple "Unit 1", "Unit 2"
            r'(\d+)\s+x\s+\d+\s*MW',  # "6 x 600 MW"
            r'(\d+)\s+units',  # "6 units"
            r'Unit\s+(\d+).*?\d+\s*MW',  # "Unit 1 ... 600 MW"
        ]

        for pattern in general_unit_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                print(f"[Session {session_id}] 🎯 General pattern matched: {pattern} → {matches}")

                # Handle "X x Y MW" pattern
                if 'x' in pattern:
                    for match in matches:
                        try:
                            unit_count = int(match)
                            gem_wiki_units = [str(i) for i in range(1, unit_count + 1)]
                            print(f"[Session {session_id}] 🎯 General: {unit_count} units → {gem_wiki_units}")
                            break
                        except ValueError:
                            continue
                else:
                    # Handle individual unit numbers
                    for match in matches:
                        if isinstance(match, str) and match.isdigit():
                            gem_wiki_units.append(match)
                        elif isinstance(match, tuple) and len(match) >= 1:
                            unit_num = match[0]
                            if unit_num.isdigit():
                                gem_wiki_units.append(unit_num)

                if gem_wiki_units:
                    print(f"[Session {session_id}] ✅ GENERAL UNIT EXTRACTION: {gem_wiki_units}")
                    break

    # PRIORITY 2: Extract unit names from PLANT-LEVEL JSON data (second most reliable)
    json_extracted_units = []

    # Try to extract from PLANT-LEVEL JSON first
    try:
        # Look for PLANT-LEVEL INFORMATION section specifically
        plant_start = text.find('PLANT-LEVEL INFORMATION:')
        if plant_start != -1:
            # Extract from plant level section using proper bracket matching
            plant_section = text[plant_start:]

            def extract_json_object(text):
                start_idx = text.find('{')
                if start_idx == -1:
                    return None

                bracket_count = 0
                for i, char in enumerate(text[start_idx:], start_idx):
                    if char == '{':
                        bracket_count += 1
                    elif char == '}':
                        bracket_count -= 1
                        if bracket_count == 0:
                            return text[start_idx:i+1]
                return None

            json_str = extract_json_object(plant_section)
            if json_str:
                import json
                plant_data = json.loads(json_str)

                # Look for units_id field specifically in plant data
                if 'units_id' in plant_data and isinstance(plant_data['units_id'], list):
                    json_extracted_units = plant_data['units_id']
                    print(f"[Session {session_id}] 🎯 Found units_id in PLANT JSON: {json_extracted_units}")

        # Fallback: Try to extract from any JSON in the text
        if not json_extracted_units:
            # Use proper bracket matching to avoid "Extra data" errors
            def extract_json_object(text):
                start_idx = text.find('{')
                if start_idx == -1:
                    return None

                bracket_count = 0
                for i, char in enumerate(text[start_idx:], start_idx):
                    if char == '{':
                        bracket_count += 1
                    elif char == '}':
                        bracket_count -= 1
                        if bracket_count == 0:
                            return text[start_idx:i+1]
                return None

            json_str = extract_json_object(text)
            if json_str:
                import json
                try:
                    data = json.loads(json_str)
                    # Look for units_id field in any JSON
                    if 'units_id' in data and isinstance(data['units_id'], list):
                        json_extracted_units = data['units_id']
                        print(f"[Session {session_id}] 🎯 Found units_id in fallback JSON: {json_extracted_units}")
                except json.JSONDecodeError as e:
                    print(f"[Session {session_id}] ⚠️ JSON parsing failed in fallback: {e}")

    except Exception as e:
        print(f"[Session {session_id}] ⚠️ JSON extraction failed: {e}")

    # If we found units in JSON, use them to create the mapping
    if json_extracted_units:
        # Convert all to strings and create sequential mapping
        original_unit_names = [str(unit) for unit in json_extracted_units]

        # Create sequential mapping preserving ALL units (including duplicates)
        sequential_numbers = [str(i+1) for i in range(len(original_unit_names))]
        unit_name_mapping = {i+1: f"Unit {original_unit_names[i]}" for i in range(len(original_unit_names))}

        print(f"[Session {session_id}] 🎯 JSON-based unit extraction:")
        print(f"[Session {session_id}] 🎯 Original units: {original_unit_names}")
        print(f"[Session {session_id}] 🎯 Sequential mapping: {sequential_numbers}")
        print(f"[Session {session_id}] 🗺️ Unit name mapping: {unit_name_mapping}")

        # Store mapping in global variable
        _current_unit_name_mapping = unit_name_mapping

        return sequential_numbers

    # Fallback: Pattern-based extraction (for cases without JSON)
    unit_name_patterns = [
        r'\b(CC\d+)\b',           # CC1, CC2, CC3, etc.
        r'\b(GT\d+)\b',           # GT1, GT2, etc.
        r'\b(ST\d+)\b',           # ST1, ST2, etc.
        r'\b(Unit\s+\d+)\b',      # Unit 1, Unit 2, etc.
        r'\b(U\d+)\b',            # U1, U2, etc.
        r'\b(SG\d+)\b',           # SG1, SG2, etc.
        r'\b(Block\s+\d+)\b',     # Block 1, Block 2, etc.
        r'\b(Generator\s+\d+)\b', # Generator 1, Generator 2, etc.
    ]

    # Extract all unit names from text
    for pattern in unit_name_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            original_unit_names.append(match.strip())

    # For pattern-based extraction, remove duplicates while preserving order
    seen = set()
    unique_unit_names = []
    for name in original_unit_names:
        if name.lower() not in seen:
            seen.add(name.lower())
            unique_unit_names.append(name)

    if unique_unit_names:
        print(f"[Session {session_id}] 🎯 Pattern-based extraction found: {unique_unit_names}")

        # Create sequential mapping
        sequential_numbers = [str(i+1) for i in range(len(unique_unit_names))]
        unit_name_mapping = {i+1: name for i, name in enumerate(unique_unit_names)}

        print(f"[Session {session_id}] 🗺️ Unit name mapping: {unit_name_mapping}")
        print(f"[Session {session_id}] ✅ Sequential numbers: {sequential_numbers}")

        # Store mapping in global variable
        _current_unit_name_mapping = unit_name_mapping

        return sequential_numbers
    
    # Method 1: JSON-based unit detection using proper bracket matching
    try:
        def extract_json_object(text):
            start_idx = text.find('{')
            if start_idx == -1:
                return None

            bracket_count = 0
            for i, char in enumerate(text[start_idx:], start_idx):
                if char == '{':
                    bracket_count += 1
                elif char == '}':
                    bracket_count -= 1
                    if bracket_count == 0:
                        return text[start_idx:i+1]
            return None

        json_str = extract_json_object(text)
        if json_str:
            import json
            plant_data = json.loads(json_str)
            
            # Look for various unit-related fields
            unit_fields = ['units_id', 'units', 'unit_list', 'unit_data', 'unit_details']
            for field in unit_fields:
                if field in plant_data:
                    unit_data = plant_data[field]
                    print(f"[Session {session_id}] Found {field}: {unit_data}")
                    
                    if isinstance(unit_data, list):
                        for unit in unit_data:
                            if isinstance(unit, str):
                                # Extract number from "Unit 1", "Unit 2", etc.
                                unit_match = re.search(r'unit[\s_]*(\d+)', unit.lower())
                                if unit_match:
                                    units_found.add(unit_match.group(1))
                                else:
                                    # Handle alphanumeric unit IDs like "SG01", "GT01", "U1", "D1", etc.
                                    # Keep them as-is instead of extracting just the number
                                    if len(unit) <= 10 and unit.strip():  # Reasonable length alphanumeric unit ID
                                        units_found.add(unit)
                                        print(f"[Session {session_id}] 🔧 Added alphanumeric unit ID: '{unit}'")
                            elif isinstance(unit, (int, float)):
                                units_found.add(str(int(unit)))
                            elif isinstance(unit, dict):
                                # Look for unit numbers in dict keys/values
                                for key, value in unit.items():
                                    if 'unit' in key.lower() and isinstance(value, (str, int)):
                                        unit_match = re.search(r'(\d+)', str(value))
                                        if unit_match:
                                            units_found.add(unit_match.group(1))
                    elif isinstance(unit_data, (str, int)):
                        unit_match = re.search(r'(\d+)', str(unit_data))
                        if unit_match:
                            units_found.add(unit_match.group(1))
                    
                    if units_found:
                        print(f"[Session {session_id}] ✅ Extracted units from {field}: {sorted(units_found)}")
                        break
    except Exception as e:
        print(f"[Session {session_id}] JSON unit detection failed: {e}")
    
    # Method 2: Regex pattern matching for various unit formats
    if not units_found:
        print(f"[Session {session_id}] 🔍 Trying regex pattern matching...")
        
        # Common unit patterns (ordered by specificity)
        patterns = [
            r'unit[\s_]*(\d+)',  # "Unit 1", "Unit_2" - most specific
            r'(\d+)[\s_]*x[\s_]*(\d+)[\s_]*mw',  # "6x660 MW" format - extract first number (unit count)
            r'units?[\s_]*(\d+)[\s_]*(?:and|to|through|-)[\s_]*(\d+)',  # "Units 1 to 6", "Units 1-6"
            r'(\d+)[\s_]*units?\s*of\s*(\d+)\s*mw\s*each',  # "2 units of 660 MW each" - more specific
            r'(?:two|2)[\s_]*units?',  # "two units", "2 units" - simple count
            r'(?:three|3)[\s_]*units?',  # "three units", "3 units"
            r'(?:four|4)[\s_]*units?',  # "four units", "4 units"
            r'(?:five|5)[\s_]*units?',  # "five units", "5 units"
            r'(?:six|6)[\s_]*units?',  # "six units", "6 units"
            r'block[\s_]*(\d+)',  # "Block 1", "Block 2"
            r'phase[\s_]*(\d+)',  # "Phase 1", "Phase 2"
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text.lower())
            if matches:
                for match in matches:
                    if isinstance(match, tuple):
                        if pattern == r'(\d+)[\s_]*x[\s_]*(\d+)[\s_]*mw':
                            # "6x660 MW" - first number is unit count
                            try:
                                unit_count = int(match[0])
                                if 1 <= unit_count <= 8:  # More restrictive range
                                    for i in range(1, unit_count + 1):
                                        units_found.add(str(i))
                                    print(f"[Session {session_id}] ✅ Found {unit_count} units from pattern: {pattern}")
                            except ValueError:
                                pass
                        elif pattern == r'(\d+)[\s_]*units?\s*of\s*(\d+)\s*mw\s*each':
                            # "2 units of 660 MW each" - extract unit count and create unit list
                            try:
                                unit_count = int(match[0])
                                if 1 <= unit_count <= 8:  # More restrictive range
                                    for i in range(1, unit_count + 1):
                                        units_found.add(str(i))
                                    print(f"[Session {session_id}] ✅ Found {unit_count} units from pattern: {pattern}")
                            except ValueError:
                                pass
                        else:
                            # Handle range patterns like "Units 1 to 6"
                            try:
                                start_unit = int(match[0])
                                end_unit = int(match[1]) if len(match) > 1 else start_unit
                                # Only accept reasonable unit ranges
                                if 1 <= start_unit <= 20 and 1 <= end_unit <= 20 and end_unit >= start_unit:
                                    for unit_num in range(start_unit, end_unit + 1):
                                        units_found.add(str(unit_num))
                            except ValueError:
                                # Try to add individual numbers if they're reasonable
                                for m in match:
                                    if str(m).isdigit() and 1 <= int(m) <= 20:
                                        units_found.add(str(m))
                    else:
                        # Single match - handle word-based patterns
                        if pattern in [r'(?:two|2)[\s_]*units?', r'(?:three|3)[\s_]*units?', r'(?:four|4)[\s_]*units?', 
                                     r'(?:five|5)[\s_]*units?', r'(?:six|6)[\s_]*units?']:
                            # Word-based unit counts
                            word_to_number = {
                                'two': 2, '2': 2,
                                'three': 3, '3': 3,
                                'four': 4, '4': 4,
                                'five': 5, '5': 5,
                                'six': 6, '6': 6
                            }
                            for word, count in word_to_number.items():
                                if word in match.lower():
                                    for i in range(1, count + 1):
                                        units_found.add(str(i))
                                    print(f"[Session {session_id}] ✅ Found {count} units from word pattern: {pattern}")
                                    break
                        elif pattern == r'(\d+)[\s_]*units?\s*of\s*\d+\s*mw':
                            # "6 units of 660 MW" - create unit list from count
                            try:
                                unit_count = int(match)
                                if 1 <= unit_count <= 20:  # Reasonable range
                                    for i in range(1, unit_count + 1):
                                        units_found.add(str(i))
                            except ValueError:
                                pass
                        else:
                            # Single match - only accept if it's a reasonable unit number
                            if str(match).isdigit() and 1 <= int(match) <= 20:
                                units_found.add(str(match))
                
                if units_found:
                    print(f"[Session {session_id}] ✅ Pattern '{pattern}' found units: {sorted(units_found)}")
                    break
    
    # Method 3: Capacity-based unit inference
    if not units_found:
        print(f"[Session {session_id}] 🔍 Trying capacity-based unit inference...")
        
        # Look for capacity mentions that might indicate multiple units
        capacity_patterns = [
            r'(\d+)\s*x\s*(\d+)\s*mw',  # "2x660 MW" = 2 units of 660 MW each
            r'(\d+)\s*units?\s*of\s*(\d+)\s*mw',  # "6 units of 210 MW"
            r'total\s*capacity[:\s]*(\d+)\s*mw.*?unit[:\s]*(\d+)\s*mw',  # Total vs unit capacity
        ]
        
        for pattern in capacity_patterns:
            matches = re.findall(pattern, text.lower())
            for match in matches:
                try:
                    if len(match) == 2:
                        unit_count = int(match[0])
                        if 1 <= unit_count <= 20:  # Reasonable range
                            for i in range(1, unit_count + 1):
                                units_found.add(str(i))
                            print(f"[Session {session_id}] ✅ Capacity pattern found {unit_count} units")
                            break
                except ValueError:
                    continue
    
    # Method 4: Contextual unit detection
    if not units_found:
        print(f"[Session {session_id}] 🔍 Trying contextual unit detection...")
        
        # Look for mentions of multiple units in descriptive text
        context_patterns = [
            r'(\d+)\s*(?:coal|thermal|power|generating)(?:[\s\-]*(?:fired|based))?(?:[\s\-]*(?:generating))?\s*units?',
            r'consists?\s*of\s*(\d+)\s*(?:coal|thermal|power|generating)?(?:[\s\-]*(?:fired|based))?(?:[\s\-]*(?:generating))?\s*units?',
            r'comprises?\s*(\d+)\s*(?:coal|thermal|power|generating)?(?:[\s\-]*(?:fired|based))?(?:[\s\-]*(?:generating))?\s*units?',
            r'has\s*(\d+)\s*(?:coal|thermal|power|generating)(?:[\s\-]*(?:fired|based))?(?:[\s\-]*(?:generating))?\s*units?',
            r'(\d+)\s*units?\s*(?:are|were)?\s*(?:operational|commissioned|installed)',
        ]
        
        for pattern in context_patterns:
            matches = re.findall(pattern, text.lower())
            for match in matches:
                try:
                    unit_count = int(match)
                    if 1 <= unit_count <= 20:  # Reasonable range
                        for i in range(1, unit_count + 1):
                            units_found.add(str(i))
                        print(f"[Session {session_id}] ✅ Context pattern '{pattern}' found {unit_count} units: {sorted([str(i) for i in range(1, unit_count + 1)])}")
                        break
                except ValueError:
                    continue
                    
            if units_found:
                break
    
    # Convert to sorted list
    if units_found:
        # If we found units but didn't extract original names, create a simple mapping
        if not original_unit_names:
            # Convert to sorted list of integers
            sorted_units = sorted([int(x) for x in units_found if x.isdigit()])

            # Create sequential mapping
            sequential_numbers = [str(i) for i in sorted_units]

            # Create simple mapping (Unit 1, Unit 2, etc.)
            unit_name_mapping = {i: f"Unit {i}" for i in sorted_units}

            print(f"[Session {session_id}] 🗺️ Simple unit name mapping: {unit_name_mapping}")

            # Store mapping in global variable
            _current_unit_name_mapping = unit_name_mapping

            print(f"[Session {session_id}] 🎯 Enhanced detection final result: {sequential_numbers}")
            return sequential_numbers
        else:
            # This shouldn't happen, but just in case
            print(f"[Session {session_id}] ⚠️ Unexpected state: units_found but no original_unit_names")
            result = sorted(list(units_found), key=lambda x: int(x) if x.isdigit() else 0)
            return result
    else:
        print(f"[Session {session_id}] ⚠️ No units detected, will use fallback")
        return []


def filter_operational_units(content: str, units_list: List[str], session_id: str) -> List[str]:
    """Filter out retired/decommissioned units - CONSERVATIVE approach trusting GEMWiki data."""
    print(f"[Session {session_id}] 🔍 CONSERVATIVE unit filtering for: {units_list}")

    # CRITICAL FIX: Trust GEMWiki data and be very conservative
    # Only filter units with EXPLICIT retirement status in GEMWiki format

    content_lower = content.lower()
    operational_units = []

    # Check if we have GEMWiki data (most reliable source)
    gem_wiki_indicators = ['gem.wiki', 'global energy monitor', 'gem wiki', 'globalenergymonitor']
    has_gem_wiki = any(indicator in content_lower for indicator in gem_wiki_indicators)

    if has_gem_wiki:
        print(f"[Session {session_id}] 🎯 GEMWiki data detected - using CONSERVATIVE filtering")

        # For GEMWiki data, only filter units with explicit status indicators
        for unit_num in units_list:
            unit_operational = True

            # Look for EXPLICIT GEMWiki status patterns only
            gemwiki_retirement_patterns = [
                f"unit {unit_num}.*retired",
                f"unit {unit_num}.*cancelled",
                f"unit {unit_num}.*decommissioned",
                f"unit {unit_num}.*status.*retired",
                f"unit {unit_num}.*status.*cancelled"
            ]

            import re
            for pattern in gemwiki_retirement_patterns:
                if re.search(pattern, content_lower):
                    print(f"[Session {session_id}] ❌ Unit {unit_num} explicitly marked as retired in GEMWiki")
                    unit_operational = False
                    break

            if unit_operational:
                operational_units.append(unit_num)
                print(f"[Session {session_id}] ✅ Unit {unit_num} operational (GEMWiki trusted)")

    else:
        print(f"[Session {session_id}] ⚠️ No GEMWiki data - assuming all units operational")
        # Without GEMWiki data, assume all units are operational
        # This prevents false positives from generic retirement mentions
        operational_units = units_list.copy()
        for unit_num in units_list:
            print(f"[Session {session_id}] ✅ Unit {unit_num} assumed operational (no GEMWiki)")

    print(f"[Session {session_id}] 📊 CONSERVATIVE filtering result: {operational_units} (from {units_list})")
    return operational_units

def extract_units_from_plant_data(state: OverallState) -> List[str]:
    """Extract unit IDs from plant-level data using enhanced detection."""
    session_id = state.get("session_id", "unknown")
    print(f"[Session {session_id}] ===== ENHANCED UNIT EXTRACTION =====")
    
    messages = state.get("messages", [])
    units_list = []
    
    print(f"[Session {session_id}] Analyzing {len(messages)} messages for unit information")
    
    # Combine all message content for comprehensive analysis
    combined_content = ""
    for message in messages:
        if hasattr(message, 'content') and isinstance(message.content, str):
            combined_content += message.content + "\n"
    
    print(f"[Session {session_id}] Total content length for analysis: {len(combined_content)}")
    
    # Use enhanced unit detection
    units_list = enhanced_unit_detection(combined_content, session_id)

    # Fallback: if no units found, try individual message analysis
    if not units_list:
        print(f"[Session {session_id}] No units from combined analysis, trying individual messages...")

        for i, message in enumerate(reversed(messages)):
            if hasattr(message, 'content') and isinstance(message.content, str):
                content = message.content
                print(f"[Session {session_id}] Analyzing message {i} (length: {len(content)})")

                units_from_message = enhanced_unit_detection(content, session_id)
                if units_from_message:
                    units_list = units_from_message
                    print(f"[Session {session_id}] Found units in message {i}: {units_list}")
                    break

    # CRITICAL: Filter out retired/decommissioned units
    if units_list:
        print(f"[Session {session_id}] 🔍 FILTERING RETIRED UNITS")
        print(f"[Session {session_id}] Units before filtering: {units_list}")

        operational_units = filter_operational_units(combined_content, units_list, session_id)

        if operational_units != units_list:
            print(f"[Session {session_id}] ⚠️ FILTERED OUT RETIRED UNITS")
            print(f"[Session {session_id}] Original units: {units_list}")
            print(f"[Session {session_id}] Operational units: {operational_units}")
            units_list = operational_units
        else:
            print(f"[Session {session_id}] ✅ All units appear to be operational")

    # If all units are retired, return empty list
    if not units_list:
        print(f"[Session {session_id}] ⚠️ NO OPERATIONAL UNITS FOUND - all units may be retired/decommissioned")
        return []
    
    # Final fallback: if still no units, make an intelligent guess
    if not units_list:
        print(f"[Session {session_id}] No units detected, making intelligent fallback...")
        
        # Try to infer from plant capacity if available
        total_capacity = 0
        unit_capacity = 0
        
        import re
        capacity_matches = re.findall(r'(\d+(?:\.\d+)?)\s*mw', combined_content.lower())
        if capacity_matches:
            capacities = [float(cap) for cap in capacity_matches]
            total_capacity = max(capacities) if capacities else 0
            
            # Common unit sizes for inference
            common_unit_sizes = [660, 600, 500, 250, 210, 200, 150, 110, 100]
            
            for unit_size in common_unit_sizes:
                if total_capacity >= unit_size * 1.5:  # Allow for some tolerance
                    estimated_units = round(total_capacity / unit_size)
                    # More restrictive validation
                    if 1 <= estimated_units <= 8 and abs(total_capacity - (estimated_units * unit_size)) <= unit_size * 0.2:
                        units_list = [str(i) for i in range(1, estimated_units + 1)]
                        print(f"[Session {session_id}] 💡 Intelligent fallback: {total_capacity}MW ÷ {unit_size}MW = {estimated_units} units (error: {abs(total_capacity - (estimated_units * unit_size))}MW)")
                        break
        
        # Ultimate fallback
        if not units_list:
            units_list = ["1"]
            print(f"[Session {session_id}] ⚠️ Using ultimate fallback: single unit")
    
    # Validate and clean the results (UPDATED: Accept alphanumeric unit IDs)
    validated_units = []
    for unit in units_list:
        if unit.isdigit() and 1 <= int(unit) <= 20:  # Numeric units in reasonable range
            validated_units.append(unit)
        elif isinstance(unit, str) and len(unit) <= 10 and unit.strip():  # Alphanumeric units (reasonable length)
            validated_units.append(unit)

    if validated_units:
        units_list = validated_units
    else:
        units_list = ["1"]  # Safety fallback
    
    # Remove duplicates and sort (FIXED: Handle non-numeric unit IDs)
    def safe_sort_key(x):
        """Safe sorting key that handles both numeric and alphanumeric unit IDs"""
        try:
            return (0, int(x))  # Numeric units first
        except (ValueError, TypeError):
            return (1, str(x))  # Alphanumeric units second

    units_list = sorted(list(set(units_list)), key=safe_sort_key)

    print(f"[Session {session_id}] 🎯 FINAL ENHANCED UNIT EXTRACTION RESULT: {units_list}")
    print(f"[Session {session_id}] Total units detected: {len(units_list)}")

    return units_list

def clear_state_for_unit_level(state: OverallState, unit_number: str) -> OverallState:
    """Clear the state for unit-level research for a specific unit.
    
    This function creates a completely clean state for researching a specific unit
    while preserving messages, session info, and PPA details from plant level.
    """
    session_id = state.get("session_id", "unknown")
    print(f"[Session {session_id}] ===== STATE CLEARING FOR UNIT {unit_number} =====")
    
    # 🔍 DEBUG: Log the incoming state
    print(f"[Session {session_id}] 🔍 DEBUG: Incoming state keys: {list(state.keys())}")
    print(f"[Session {session_id}] 🔍 DEBUG: remaining_units in incoming state: {state.get('remaining_units', 'NOT_FOUND')}")
    print(f"[Session {session_id}] 🔍 DEBUG: all_units in incoming state: {state.get('all_units', 'NOT_FOUND')}")
    print(f"[Session {session_id}] 🔍 DEBUG: current_unit in incoming state: {state.get('current_unit', 'NOT_FOUND')}")
    
    # Log current state before clearing
    current_research_topic = get_research_topic(state.get("messages", []))
    print(f"[Session {session_id}] Current research topic: '{current_research_topic}'")
    print(f"[Session {session_id}] Preparing research for Unit {unit_number}")
    

    
    # Generate a NEW session ID for this specific unit
    new_session_id = f"unit{unit_number}-{uuid.uuid4().hex[:8]}"
    print(f"[Session {session_id}] Creating NEW session for Unit {unit_number}: {new_session_id}")
    
    # Preserve essential information
    messages = state.get("messages", [])  # Preserve conversation history
    initial_search_query_count = state.get("initial_search_query_count", 6)  # More queries for unit level
    max_research_loops = state.get("max_research_loops", 3)
    reasoning_model = state.get("reasoning_model", "")
    
    # Preserve multi-unit processing information
    remaining_units = state.get("remaining_units", [])
    all_units = state.get("all_units", [])
    unit_results = state.get("unit_results", [])
    
    # 🔍 DEBUG: Log what we extracted
    print(f"[Session {session_id}] 🔍 DEBUG: Extracted remaining_units: {remaining_units}")
    print(f"[Session {session_id}] 🔍 DEBUG: Extracted all_units: {all_units}")
    print(f"[Session {session_id}] 🔍 DEBUG: Extracted unit_results: {unit_results}")
    
    # Return a clean state for unit-level research but preserve multi-unit info
    new_state = {
        "messages": messages,  # Keep the conversation history
        "session_id": new_session_id,  # NEW session ID for complete isolation
        "search_phase": 3,  # Set to unit-level phase
        "unit_number": unit_number,  # Track which unit we're researching
        "research_loop_count": 0,  # Reset research loop count
        "web_research_result": [],  # Clear previous research results
        "search_query": [],  # Clear previous search queries
        "sources_gathered": [],  # Clear previous sources
        "org_level_complete": True,  # Mark organization-level as complete
        "plant_level_complete": True,  # Mark plant-level as complete
        "initial_search_query_count": initial_search_query_count,
        "max_research_loops": max_research_loops,
        "reasoning_model": reasoning_model,
        "continue_research": False,
        "phase_complete": False,

        
        # PRESERVE MULTI-UNIT PROCESSING INFO
        "remaining_units": remaining_units,  # Keep track of remaining units
        "all_units": all_units,  # Keep track of all units
        "unit_results": unit_results,  # Keep track of completed unit results
        "current_unit": unit_number,  # Set current unit being processed

        # CRITICAL: Preserve UIDs from previous state
        "org_id": state.get("org_id", ""),  # Preserve org UID
        "plant_uid": state.get("plant_uid", ""),  # Preserve plant UID
        "plant_technology": state.get("plant_technology", "coal"),  # Preserve plant technology
    }
    
    print(f"[Session {new_session_id}] *** UNIT {unit_number} STATE CREATED - COMPLETELY CLEAN ***")
    print(f"[Session {new_session_id}] Research topic: '{get_research_topic(messages)}'")
    print(f"[Session {new_session_id}] 🔍 DEBUG: UIDs in unit state: org_id='{new_state.get('org_id')}', plant_uid='{new_state.get('plant_uid')}'")

    return new_state



def simplified_plant_processing(state: OverallState) -> OverallState:
    """Simplified function to proceed directly to unit processing."""
    session_id = state.get("session_id", "unknown")
    print(f"[Session {session_id}] ===== PROCEEDING TO UNIT PROCESSING =====")

    # Get the research topic from the original query
    research_topic = get_research_topic(state["messages"]) if state.get("messages") else ""
    print(f"[Session {session_id}] 🔍 Processing plant: {research_topic}")

    # Always proceed to single plant processing
    return {
        **state,
        "plants_detected": 1,
        "plant_names": [research_topic] if research_topic else ["Unknown Plant"],
        "current_plant_index": 0
    }


# Multi-plant processing removed as requested


# Multi-plant isolated processing removed as requested


# Multi-plant extraction removed as requested


# Multi-plant results collection removed as requested


def process_all_units(state: OverallState):
    """FRESH START: Extract units using unit_level.json specification."""
    session_id = state.get("session_id", "unknown")
    print(f"[Session {session_id}] ===== FRESH UNIT EXTRACTION FROM SCRATCH =====")
    
    # Load unit_level.json template
    import json
    import os
    
    unit_template_path = "/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/unit_level.json"
    try:
        with open(unit_template_path, 'r') as f:
            unit_template = json.load(f)
        print(f"[Session {session_id}] ✅ Loaded unit_level.json template with {len(unit_template)} fields")
    except Exception as e:
        print(f"[Session {session_id}] ❌ Failed to load unit_level.json: {e}")
        return {**state, "no_units_found": True}
    
    # CRITICAL FIX: Use plant-level units_id instead of re-extracting
    plant_data = state.get("plant_data", {})
    units_list = []

    if plant_data and "units_id" in plant_data:
        # Use the correctly formatted units_id from plant-level processing
        plant_units_id = plant_data["units_id"]
        if isinstance(plant_units_id, list) and len(plant_units_id) > 0:
            units_list = [str(unit) for unit in plant_units_id]
            print(f"[Session {session_id}] ✅ Using plant-level units_id: {units_list}")
        else:
            print(f"[Session {session_id}] ⚠️ Plant-level units_id empty, falling back to extraction")
            units_list = extract_units_from_plant_data(state)
    else:
        print(f"[Session {session_id}] ⚠️ No plant_data or units_id, falling back to extraction")
        units_list = extract_units_from_plant_data(state)

    if not units_list:
        print(f"[Session {session_id}] No units found")
        return {
            **state,
            "extracted_units": [],
            "no_units_found": True,
        }

    print(f"[Session {session_id}] 🎯 Final units for processing: {units_list}")

    # DO NOT override plant_data units_id - preserve the correct plant-level formatting

    # SIMPLE SEQUENTIAL PROCESSING - no batching complexity
    result_state = {
        **state,
        "extracted_units": units_list,
        "unit_template": unit_template,
        "no_units_found": False,
        "ready_for_unit_processing": True,
        "plant_data": plant_data,  # Include updated plant_data
    }
    
    print(f"[Session {session_id}] 🔍 DEBUG: process_all_units returning unit_template with {len(unit_template)} keys")
    print(f"[Session {session_id}] 🔍 DEBUG: process_all_units returning ready_for_unit_processing = {result_state.get('ready_for_unit_processing')}")
    
    return result_state

def extract_unit_data_fresh(state: OverallState):
    """OPTIMIZED: Extract unit data using BATCH field extraction + PRODUCTION MODELS.
    
    PERFORMANCE IMPROVEMENT:
    - OLD APPROACH: 60+ API calls per unit (1 per field) + EXPERIMENTAL MODEL = SEVERE RATE LIMITS
    - NEW APPROACH: 2 API calls per unit (1 research + 1 batch extraction) + PRODUCTION MODEL
    
    RATE LIMIT IMPROVEMENT:
    - OLD: gemini-2.0-flash-exp (10 RPM) + 240+ calls = IMMEDIATE FAILURE
    - NEW: gemini-2.0-flash (2,000 RPM) + 8 calls = SMOOTH PROCESSING
    
    For a 4-unit plant:
    - OLD: 4 × 60+ = 240+ API calls on 10 RPM limit = 24+ minutes + failures
    - NEW: 4 × 2 = 8 API calls on 2,000 RPM limit = <1 minute + reliable
    """
    session_id = state.get("session_id", "unknown")
    print(f"[Session {session_id}] ===== FRESH UNIT DATA EXTRACTION =====")
    
    units_list = state.get("extracted_units", [])
    
    # Load unit_level.json template directly (since LangGraph state isn't passing it correctly)
    import json
    import os
    
    unit_template_path = "/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/unit_level.json"
    try:
        with open(unit_template_path, 'r') as f:
            unit_template = json.load(f)
        print(f"[Session {session_id}] ✅ Loaded unit_level.json template directly with {len(unit_template)} fields")
    except Exception as e:
        print(f"[Session {session_id}] ❌ Failed to load unit_level.json: {e}")
        return {**state, "no_units_found": True}
    
    print(f"[Session {session_id}] 🔍 DEBUG: units_list = {units_list}")
    print(f"[Session {session_id}] 🔍 DEBUG: unit_template keys = {list(unit_template.keys())}")
    
    if not units_list or not unit_template:
        print(f"[Session {session_id}] Missing units or template")
        return {**state, "no_units_found": True}
    
    # Get plant data from messages
    messages = state.get("messages", [])
    plant_data_content = ""
    
    for msg in messages:
        if hasattr(msg, 'content') and isinstance(msg.content, str):
            plant_data_content += msg.content + "\n"
    
    print(f"[Session {session_id}] 📄 Plant data content length: {len(plant_data_content)}")
    

    
    # Extract plant data for unit sk generation using proper bracket matching
    plant_data = {"plant_type": "coal", "plant_id": 1}
    try:
        import json

        def extract_json_object(text):
            start_idx = text.find('{')
            if start_idx == -1:
                return None

            bracket_count = 0
            for i, char in enumerate(text[start_idx:], start_idx):
                if char == '{':
                    bracket_count += 1
                elif char == '}':
                    bracket_count -= 1
                    if bracket_count == 0:
                        return text[start_idx:i+1]
            return None

        json_str = extract_json_object(plant_data_content)
        if json_str:
            parsed_plant_data = json.loads(json_str)
            if isinstance(parsed_plant_data, dict):
                plant_data["plant_type"] = parsed_plant_data.get("plant_type", "coal")
                plant_data["plant_id"] = parsed_plant_data.get("plant_id", 1)
                print(f"[Session {session_id}] ✅ Extracted plant data: {plant_data}")
    except Exception as e:
        print(f"[Session {session_id}] ⚠️ Could not extract plant data for unit sk: {e}")
    
    # Get plant name from state (same approach as org/plant levels) - BEFORE the loop
    research_topic = get_research_topic(state.get("messages", []))
    plant_name = state.get("plant_name_for_s3", research_topic)

    # Get actual country from database for proper currency handling
    country = "Unknown"
    try:
        from agent.database_manager import get_database_manager
        db_manager = get_database_manager()
        plant_info = db_manager.check_plant_exists(plant_name)
        if plant_info and plant_info.get("country"):
            country = plant_info["country"]
            print(f"[Session {session_id}] 🌍 Retrieved country for unit extraction: {country}")
    except Exception as e:
        print(f"[Session {session_id}] ⚠️ Could not retrieve country: {e}")

    # CRITICAL FIX: Check if this is a USA plant and use Excel tool ONCE for ALL units
    print(f"[Session {session_id}] 🔍 Checking data source for {plant_name} in {country}")

    # If USA plant, use Excel tool instead of web search
    if country and ("united states" in country.lower() or "usa" in country.lower() or "us" in country.lower()):
        print(f"[Session {session_id}] 🇺🇸 USA PLANT DETECTED - Using Excel tool for {plant_name}")

        try:
            # Import and use Excel tool
            import sys
            import os
            sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')
            from excel_power_plant_tool import ExcelPowerPlantTool

            # 🚨 CRITICAL FIX: Normalize plant name BEFORE Excel tool initialization
            plant_name = normalize_plant_name(plant_name)
            print(f"[Session {session_id}] 🔧 Normalized plant name: '{plant_name}'")

            # 🚨 CRITICAL FIX: Initialize Excel tool ONCE and get ALL units data in single call
            print(f"[Session {session_id}] 🔧 Initializing Excel tool...")
            excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx', session_id)
            print(f"[Session {session_id}] ✅ Excel tool initialized successfully")

            # 🚨 CRITICAL FIX: Get ALL units data in ONE call instead of per-unit calls
            print(f"[Session {session_id}] 📊 Getting ALL units data for plant: '{plant_name}'")
            all_excel_results = excel_tool.get_plant_data(plant_name)

            if all_excel_results:
                print(f"[Session {session_id}] ✅ Found {len(all_excel_results)} units from Excel")
                all_units_data = all_excel_results
            else:
                print(f"[Session {session_id}] ❌ No Excel data found for {plant_name}")


            if all_units_data:
                print(f"[Session {session_id}] ✅ Excel tool extracted data for {len(all_units_data)} units")
                print(f"[Session {session_id}] 🔄 Continuing to unit processing pipeline to generate JSON files...")
                # Store Excel data in state for use in unit processing loop
                state["excel_units_data"] = all_units_data
                state["excel_tool_used"] = True
            else:
                print(f"[Session {session_id}] ❌ No Excel data found for any units, falling back to web search")

        except Exception as e:
            print(f"[Session {session_id}] ❌ Excel tool failed: {e}")
            print(f"[Session {session_id}] 🔄 Falling back to web search extraction...")

    # Process each unit individually with API call tracking
    unit_responses = []
    total_api_calls = 0

    print(f"[Session {session_id}] 🚨 CRITICAL DEBUG: Starting unit processing loop")
    print(f"[Session {session_id}] 🚨 Units to process: {units_list}")
    print(f"[Session {session_id}] 🚨 Total units count: {len(units_list)}")

    for unit_num in sorted(units_list, key=int):  # Sort to ensure Unit 1 first
        print(f"[Session {session_id}] 🚨 CRITICAL DEBUG: ===== STARTING UNIT {unit_num} =====")
        print(f"[Session {session_id}] 🔍 Starting Unit {unit_num} processing...")
        unit_start_calls = total_api_calls

        # Check if Excel data is available for this unit
        excel_data_for_unit = None
        if state.get("excel_tool_used") and state.get("excel_units_data"):
            # Find Excel data for this specific unit
            print(f"[Session {session_id}] 🔍 DEBUG: Looking for Unit {unit_num} in Excel data")
            print(f"[Session {session_id}] 🔍 DEBUG: Available Excel units: {len(state['excel_units_data'])}")

            for i, excel_unit in enumerate(state["excel_units_data"]):
                unit_id_in_excel = excel_unit.get("unit_id")
                print(f"[Session {session_id}] 🔍 DEBUG: Excel unit {i}: unit_id='{unit_id_in_excel}' (type: {type(unit_id_in_excel)})")
                print(f"[Session {session_id}] 🔍 DEBUG: Comparing '{unit_id_in_excel}' == '{unit_num}' (type: {type(unit_num)})")

                # 🚨 CRITICAL FIX: Normalize both unit IDs for comparison (handle 1.0 vs 1)
                def normalize_unit_id(uid):
                    """Convert unit ID to clean string format (1.0 -> 1)"""
                    try:
                        # Handle float strings like "1.0" -> "1"
                        return str(int(float(str(uid))))
                    except (ValueError, TypeError):
                        # For non-numeric IDs, keep as string
                        return str(uid)

                normalized_excel_id = normalize_unit_id(unit_id_in_excel)
                normalized_unit_num = normalize_unit_id(unit_num)

                print(f"[Session {session_id}] 🔧 Normalized comparison: '{normalized_excel_id}' == '{normalized_unit_num}'")

                if normalized_excel_id == normalized_unit_num:
                    excel_data_for_unit = excel_unit
                    print(f"[Session {session_id}] ✅ Found Excel data for Unit {unit_num} (matched {unit_id_in_excel})")
                    break
                else:
                    print(f"[Session {session_id}] ❌ No match: '{normalized_excel_id}' != '{normalized_unit_num}'")

            if not excel_data_for_unit:
                print(f"[Session {session_id}] 🚨 CRITICAL: No Excel data matched for Unit {unit_num}")
                print(f"[Session {session_id}] 🚨 Available unit_ids: {[excel_unit.get('unit_id') for excel_unit in state['excel_units_data']]}")

        if excel_data_for_unit:
            print(f"[Session {session_id}] 📊 Using Excel data for Unit {unit_num} (skipping web research)")
            # Use Excel data as input to the processing pipeline
            unit_research_data = f"Excel data for {plant_name} Unit {unit_num}"
        else:
            print(f"[Session {session_id}] 🔍 No Excel data found, using web research for Unit {unit_num}")
            # Do dedicated web research for this unit (1 API call)
            unit_research_data = perform_unit_web_research(unit_num, plant_data_content, plant_name, session_id)
            total_api_calls += 1
        
        # Check research data quality
        if not unit_research_data or len(unit_research_data.strip()) < 100:
            print(f"[Session {session_id}] ⚠️ Very limited research data for Unit {unit_num} ({len(unit_research_data)} chars)")
        else:
            # Check for common data quality indicators
            quality_indicators = [
                "capacity" in unit_research_data.lower(),
                "mw" in unit_research_data.lower() or "megawatt" in unit_research_data.lower(),
                "unit " + str(unit_num) in unit_research_data.lower(),
                plant_name.lower() in unit_research_data.lower(),
                any(word in unit_research_data.lower() for word in ["coal", "gas", "thermal", "power"])
            ]
            quality_score = sum(quality_indicators)
            print(f"[Session {session_id}] 📊 Unit {unit_num} research quality: {quality_score}/5 indicators found")
        
        # MULTI-STAGE EXTRACTION for better data quality (5 focused API calls instead of 1 overwhelming call)
        print(f"[Session {session_id}] 🔄 Starting multi-stage extraction for Unit {unit_num}...")
        
        # Import the multi-stage extraction functions
        from agent.unit_extraction_stages import (
            extract_basic_unit_info,
            extract_performance_metrics, 
            extract_fuel_and_emissions,
            extract_economic_data,
            extract_technical_parameters,
            combine_unit_data
        )
        
        # DEBUG: Check what's in plant_data for plant_type
        print(f"[Session {session_id}] 🔍 DEBUG PLANT DATA for plant_type:")
        print(f"[Session {session_id}]   - plant_data keys: {list(plant_data.keys()) if isinstance(plant_data, dict) else 'Not dict'}")
        print(f"[Session {session_id}]   - plant_data.get('plant_type'): '{plant_data.get('plant_type', 'NOT_FOUND')}'")
        print(f"[Session {session_id}]   - plant_data.get('technology'): '{plant_data.get('technology', 'NOT_FOUND')}'")
        print(f"[Session {session_id}]   - plant_data.get('plant_technology'): '{plant_data.get('plant_technology', 'NOT_FOUND')}'")

        # UPDATED PLANT CONTEXT: Separate plant_id (sequential) and plant_uid (UUID)
        plant_context = {
            "plant_name": plant_name,
            "plant_type": plant_data.get("plant_type", "coal"),  # Use plant_type for SK generation
            "plant_technology": plant_data.get("plant_type", "coal"),  # Keep for backward compatibility
            "plant_id": str(plant_data.get("plant_id", "1")),  # Sequential number (1, 2, 3, etc.)
            "plant_uid": state.get("plant_uid", plant_data.get("pk", "")),  # Plant UUID from state/database
            "country": country,  # Use actual country instead of hardcoded "Unknown"
            "org_id": state.get("org_id", ""),  # Organization UID
        }

        print(f"[Session {session_id}] 🔧 PLANT CONTEXT: plant_type='{plant_context['plant_type']}', plant_id='{plant_context['plant_id']}', plant_uid='{plant_context['plant_uid']}'")

        
        reasoning_model = "gemini-2.0-flash"  # Use high-performance model with better rate limits
        
        # Stage 1: Basic unit info
        print(f"[Session {session_id}] 📊 Stage 1: Extracting basic unit information...")
        print(f"[Session {session_id}] 🔍 Input data length: {len(unit_research_data)} chars")
        stage1_data = extract_basic_unit_info(unit_research_data, unit_num, plant_context, reasoning_model)
        non_null_count = sum(1 for v in stage1_data.values() if v not in [None, [], {}, '', 'Not available'])
        print(f"[Session {session_id}] 📊 Stage 1 Results: {non_null_count}/{len(stage1_data)} fields extracted")
        total_api_calls += 1
        
        # Stage 2: Performance metrics
        print(f"[Session {session_id}] 📈 Stage 2: Extracting performance metrics...")
        stage2_data = extract_performance_metrics(unit_research_data, unit_num, plant_context, reasoning_model)
        non_null_count = sum(1 for v in stage2_data.values() if v not in [None, [], {}, '', 'Not available'])
        print(f"[Session {session_id}] 📈 Stage 2 Results: {non_null_count}/{len(stage2_data)} fields extracted")
        total_api_calls += 1
        
        # Stage 3: Fuel and emissions  
        print(f"[Session {session_id}] ⛽ Stage 3: Extracting fuel and emission data...")
        stage3_data = extract_fuel_and_emissions(unit_research_data, unit_num, plant_context, reasoning_model)
        non_null_count = sum(1 for v in stage3_data.values() if v not in [None, [], {}, '', 'Not available'])
        print(f"[Session {session_id}] ⛽ Stage 3 Results: {non_null_count}/{len(stage3_data)} fields extracted")
        total_api_calls += 1
        
        # Stage 4: Economic data
        print(f"[Session {session_id}] 💰 Stage 4: Extracting economic data...")
        stage4_data = extract_economic_data(unit_research_data, unit_num, plant_context, reasoning_model)
        non_null_count = sum(1 for v in stage4_data.values() if v not in [None, [], {}, '', 'Not available'])
        print(f"[Session {session_id}] 💰 Stage 4 Results: {non_null_count}/{len(stage4_data)} fields extracted")
        total_api_calls += 1
        
        # Stage 5: Technical parameters
        print(f"[Session {session_id}] ⚙️ Stage 5: Extracting technical parameters...")
        stage5_data = extract_technical_parameters(unit_research_data, unit_num, plant_context, reasoning_model)
        non_null_count = sum(1 for v in stage5_data.values() if v not in [None, [], {}, '', 'Not available'])
        print(f"[Session {session_id}] ⚙️ Stage 5 Results: {non_null_count}/{len(stage5_data)} fields extracted")
        total_api_calls += 1
        
        # Combine all stages
        print(f"[Session {session_id}] 🔧 Combining all extraction stages...")

        # Pass Excel data to combine_unit_data if available
        if excel_data_for_unit:
            print(f"[Session {session_id}] 📊 Using Excel data in combine_unit_data for Unit {unit_num}")
            plant_context["excel_data"] = excel_data_for_unit

        unit_data = combine_unit_data(
            [stage1_data, stage2_data, stage3_data, stage4_data, stage5_data],
            unit_num,
            plant_context
        )
        
        # TARGETED ENHANCEMENT: Fill missing foundation data using targeted searches
        print(f"[Session {session_id}] 🎯 Applying targeted enhancement for missing foundation data...")
        try:
            from agent.targeted_enhancement import apply_targeted_enhancement
            
            # Create plant context for enhancement
            enhancement_context = {
                "plant_name": plant_name,
                "unit_number": unit_num,
                "plant_technology": plant_data.get("plant_type", "coal")
            }
            
            # Apply targeted enhancement for missing foundation data
            unit_data = apply_targeted_enhancement(
                unit_data, 
                enhancement_context, 
                unit_research_data,  # Original research data for reference
                session_id
            )
            
        except Exception as enhancement_error:
            print(f"[Session {session_id}] ⚠️ Targeted enhancement error: {str(enhancement_error)}")
            # Continue without enhancement if there's an error
        
        # FALLBACK CALCULATIONS: Fill missing data using engineering formulas
        print(f"[Session {session_id}] 🧮 Applying fallback calculations for missing data...")
        try:
            from agent.fallback_calculations import enhance_unit_with_calculations
            
            # Create unit context for calculations
            unit_context = {
                "capacity": unit_data.get("capacity", "0"),
                "technology": unit_data.get("technology", "subcritical"),
                "plant_name": plant_name,
                "unit_number": unit_num
            }
            
            # Apply fallback calculations (now with enhanced foundation data)
            unit_data = enhance_unit_with_calculations(unit_data, unit_context, session_id)
            
        except Exception as calc_error:
            print(f"[Session {session_id}] ⚠️ Fallback calculation error: {str(calc_error)}")
            # Continue without calculations if there's an error
        
        print(f"[Session {session_id}] ✅ Multi-stage extraction complete for Unit {unit_num}!")
        print(f"[Session {session_id}] 📋 Total fields extracted: {len(unit_data)}")
        
        # Show detailed extraction results
        extracted_fields = sum(1 for v in unit_data.values() if v not in [None, [], {}, '', 'Not available', 'default null'])
        print(f"[Session {session_id}] 📈 Extraction success: {extracted_fields}/{len(unit_data)} fields have data")
        print(f"[Session {session_id}] 🎯 Key extracted data:")
        print(f"[Session {session_id}]   - Capacity: {unit_data.get('capacity', 'N/A')} {unit_data.get('capacity_unit', '')}")
        print(f"[Session {session_id}]   - Technology: {unit_data.get('technology', 'N/A')}")
        print(f"[Session {session_id}]   - Coal Type: {unit_data.get('selected_coal_type', 'N/A')}")
        print(f"[Session {session_id}]   - PLF Records: {len(unit_data.get('plf', []))}")
        print(f"[Session {session_id}]   - PAF Records: {len(unit_data.get('PAF', []))}")
        print(f"[Session {session_id}]   - Fuel Types: {len(unit_data.get('fuel_type', []))}")
        print(f"[Session {session_id}]   - Efficiency: {unit_data.get('coal_unit_efficiency', 'N/A')}")
        print(f"[Session {session_id}]   - Heat Rate: {unit_data.get('heat_rate', 'N/A')} {unit_data.get('heat_rate_unit', '')}")
        
        # SAVE UNIT JSON TO S3 (Fresh Unit Processing)
        try:
            # Import S3 storage function
            from agent.json_s3_storage import store_unit_data
            
            # Format and store unit data to S3 with UID
            if unit_data and isinstance(unit_data, dict):
                # SIMPLE DATABASE LOOKUP: Get plant_id for unit pk field
                plant_id = ""
                org_id = ""

                try:
                    print(f"[Session {session_id}] 🚨 CRITICAL DEBUG: Unit {unit_num} - Starting database lookup")
                    from agent.database_manager import get_database_manager
                    db_manager = get_database_manager()

                    # Get plant_id from database (units use plant_id as pk)
                    existing_plant = db_manager.check_plant_exists(plant_name)
                    if existing_plant:
                        plant_id = existing_plant.get("plant_id", "")
                        org_id = existing_plant.get("org_id", "")
                        print(f"[Session {session_id}] 🚨 CRITICAL DEBUG: Unit {unit_num} - Database lookup SUCCESS: plant_id='{plant_id}', org_id='{org_id}'")
                    else:
                        print(f"[Session {session_id}] 🚨 CRITICAL DEBUG: Unit {unit_num} - Plant not found in database: {plant_name}")

                except Exception as e:
                    print(f"[Session {session_id}] 🚨 CRITICAL DEBUG: Unit {unit_num} - Database lookup FAILED: {e}")
                    import traceback
                    traceback.print_exc()

                # Set pk field from database lookup (units use plant_id as pk)
                if plant_id:
                    unit_data["pk"] = plant_id
                    print(f"[Session {session_id}] 🚨 CRITICAL DEBUG: Unit {unit_num} - Set pk field from database: {plant_id}")
                else:
                    # 🚨 CRITICAL FIX: Use fallback values to ensure unit still gets saved
                    fallback_plant_id = state.get("plant_uid", f"fallback-plant-{unit_num}")
                    fallback_org_id = state.get("org_id", f"fallback-org-{unit_num}")
                    plant_id = fallback_plant_id
                    org_id = fallback_org_id
                    unit_data["pk"] = plant_id
                    print(f"[Session {session_id}] 🚨 CRITICAL DEBUG: Unit {unit_num} - Using FALLBACK values: plant_id='{plant_id}', org_id='{org_id}'")

                # Format unit data (no UID parameters needed)
                plant_uid = state.get("plant_uid", plant_id)  # Use plant_id as fallback
                print(f"[Session {session_id}] 🔍 DEBUG FRESH UNIT STORAGE: plant_uid from state = '{plant_uid}'")
                print(f"[Session {session_id}] 🔍 DEBUG FRESH UNIT STORAGE: org_id = '{org_id}'")
                print(f"[Session {session_id}] 🔍 DEBUG FRESH UNIT STORAGE: state keys = {list(state.keys())}")
                # 🚨 CRITICAL DEBUG: Create a deep copy to prevent variable overwriting
                import copy
                unit_data_copy = copy.deepcopy(unit_data)

                formatted_unit_data = process_unit_data_formatting(unit_data_copy, session_id, plant_uid)
                print(f"[Session {session_id}] 🚨 CRITICAL DEBUG: About to save Unit {unit_num}")
                print(f"[Session {session_id}] 🚨 Unit {unit_num} SK: {formatted_unit_data.get('sk', 'MISSING')}")
                print(f"[Session {session_id}] 🚨 Unit {unit_num} filename will be: {formatted_unit_data.get('sk', 'MISSING')}.json")
                print(f"[Session {session_id}] 🚨 Unit {unit_num} unit_number in data: {formatted_unit_data.get('unit_number', 'MISSING')}")

                # 🚨 CRITICAL DEBUG: Verify SK contains correct unit number
                sk_value = formatted_unit_data.get('sk', '')
                if f"#{unit_num}#" not in sk_value:
                    print(f"[Session {session_id}] 🚨 ❌ CRITICAL ERROR: SK does not contain unit number {unit_num}!")
                    print(f"[Session {session_id}] 🚨    Expected: unit#coal#{unit_num}#plant#X")
                    print(f"[Session {session_id}] 🚨    Actual: {sk_value}")
                else:
                    print(f"[Session {session_id}] 🚨 ✅ SK correctly contains unit number {unit_num}")

                unit_s3_url = store_unit_data(formatted_unit_data, plant_name, unit_num, session_id, org_id=org_id, plant_id=plant_uid)

                if unit_s3_url:
                    print(f"[Session {session_id}] 🚨 CRITICAL DEBUG: ✅ Unit {unit_num} JSON SUCCESSFULLY SAVED to S3: {unit_s3_url}")
                else:
                    print(f"[Session {session_id}] 🚨 CRITICAL DEBUG: ❌ FAILED to save Unit {unit_num} JSON to S3")
            else:
                print(f"[Session {session_id}] 🚨 CRITICAL DEBUG: ⚠️ No valid unit data to save for Unit {unit_num}")

        except Exception as s3_error:
            print(f"[Session {session_id}] 🚨 CRITICAL DEBUG: ❌ S3 storage error for Unit {unit_num}: {str(s3_error)}")
            import traceback
            traceback.print_exc()
        

        # The total_api_calls += 1 above accounts for the main batch extraction call
        
        unit_calls = total_api_calls - unit_start_calls
        print(f"[Session {session_id}] 📊 Unit {unit_num} used {unit_calls} API calls (Target: 6 - 1 research + 5 stages)")

        print(f"[Session {session_id}] 🚨 CRITICAL DEBUG: ===== COMPLETED UNIT {unit_num} =====")

        # Create individual response
        import json

        # 🚨 CRITICAL DEBUG: Create a copy for response to prevent variable overwriting
        unit_data_for_response = copy.deepcopy(unit_data)
        unit_json = json.dumps(unit_data_for_response, indent=2)
        unit_message = f"Unit {unit_num} Technical Data:\n```json\n{unit_json}\n```"

        unit_responses.append(AIMessage(content=unit_message))
        print(f"[Session {session_id}] ✅ Created Unit {unit_num} response with {len(unit_data_for_response)} fields")
        print(f"[Session {session_id}] 🚨 Unit {unit_num} response SK: {unit_data_for_response.get('sk', 'MISSING')}")

    print(f"[Session {session_id}] 🚨 CRITICAL DEBUG: ===== COMPLETED ALL UNITS LOOP =====")
    print(f"[Session {session_id}] 🚨 Units processed: {[unit for unit in sorted(units_list, key=int)]}")
    print(f"[Session {session_id}] 🚨 Unit responses created: {len(unit_responses)}")

    print(f"[Session {session_id}] 📊 MULTI-STAGE EXTRACTION SUMMARY:")
    print(f"[Session {session_id}] 📊 Total Units: {len(units_list)}")
    print(f"[Session {session_id}] 📊 Total API Calls: {total_api_calls}")
    print(f"[Session {session_id}] 📊 Average per Unit: {total_api_calls/len(units_list):.1f} calls")
    print(f"[Session {session_id}] 📊 Model Used: gemini-2.0-flash (High-performance model with 2,000+ RPM)")
    print(f"[Session {session_id}] 📊 EXTRACTION APPROACH: 6 focused calls per unit (1 research + 5 stage extractions)")
    print(f"[Session {session_id}] 📊 OLD SINGLE-CALL: 1 overwhelming call extracting 40+ fields → Most 'Not available'")
    print(f"[Session {session_id}] 📊 NEW MULTI-STAGE: 5 focused calls extracting 7-10 fields each → Much better success")
    print(f"[Session {session_id}] 📊 QUALITY IMPROVEMENT: Significantly more fields extracted successfully")
    print(f"[Session {session_id}] 📊 S3 STORAGE: All unit JSONs automatically saved to S3")
    
    return {
        **state,
        "messages": state.get("messages", []) + unit_responses,
        "processing_complete": True
    }

def perform_unit_web_research(unit_number: str, plant_content: str, plant_name: str, session_id: str) -> str:
    """Perform comprehensive web research for a specific unit using Gemini with Google Search."""
    import json
    
    print(f"[Session {session_id}] 🔍 Starting comprehensive research for Unit {unit_number} of {plant_name}")
    
    # Load unit level template to understand what fields we need
    unit_template = {}
    try:
        with open('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/unit_level.json', 'r') as f:
            unit_template = json.load(f)
    except Exception as e:
        print(f"[Session {session_id}] ⚠️ Could not load unit_level.json: {e}")
    
    # Create comprehensive research prompt for Gemini with SPECIFIC FIELD TARGETS
    research_prompt = f"""You are a power plant research specialist. Conduct comprehensive web research to gather detailed information about Unit {unit_number} of {plant_name}.

Current date: {get_current_date()}

🎯 **PRIORITY 1: GEM WIKI DATA (MOST ACCURATE FOR CAPACITY & TECHNOLOGY)**
MANDATORY: Search for "{plant_name} site:gem.wiki" and "{plant_name} site:globalenergymonitor.org" first
- GEM Wiki contains the most accurate capacity and technology data for power plant units
- Look for unit tables, specifications, and technical details in GEM Wiki content
- Extract unit-specific capacity (MW) and EXACT technology type from GEM Wiki data
- **CRITICAL**: Use exact GEM Wiki terminology for technology (e.g., "Subcritical", "Supercritical", "Ultra-supercritical")
- **SEARCH THOROUGHLY**: Check unit tables, project details, and technical specifications sections

CRITICAL UNIT-LEVEL FIELD RESEARCH:
Search for and collect specific information about Unit {unit_number} of {plant_name} for these EXACT fields:

1. OPERATIONAL PARAMETERS:
   - Annual operational hours (target: 8760 hours or actual operational hours per year)
   - Heat rate in Kcal/kWh (amount of heat energy needed to generate one kWh of electricity)
   - Blending percentage for biomass co-firing (typically 0.15 or 15% if applicable)

2. COAL TYPE AND EMISSION FACTORS:
   - Primary coal type used (bituminous, sub-bituminous, lignite, anthracite)
   - Emission factor for the specific coal type in kg CO₂e/kg of coal burned
   - Source of emission factor data (IPCC, IEA, national inventory guidelines)
   - If multiple coal types: provide range or average value

3. GAS EMISSION FACTORS:
   - Emission factor for natural gas: 2.69 kg CO₂e/kg (verify or find actual value)
   - Emission factor unit: kg CO₂e/kg or kgCO₂/kWh

4. ENVIRONMENTAL SYSTEMS:
   - FGDS (Flue Gas Desulfurization System) status:
     * Fully installed and operational
     * Under construction or planned
     * Not installed
   - Type of FGDS (wet limestone, dry FGD, seawater FGD)
   - Year of FGDS commissioning
   - Specific units covered by FGDS

5. RETROFIT COSTS (in local currency per MW):
   - CAPEX for biomass co-firing retrofit using Palm Kernel Shells (PKS)
   - CAPEX for Open Cycle Gas Turbine (OCGT) conversion
   - CAPEX for Combined Cycle Gas Turbine (CCGT) conversion
   - Express costs as full numbers (e.g., ********* IDR not 145 million IDR)
   - Include year of estimate and factors affecting cost variation

SEARCH INSTRUCTIONS:
- Focus specifically on Unit {unit_number} data, not plant-level aggregates
- Search multiple reliable sources including company reports, regulatory filings, industry databases
- Look for official documents, technical specifications, annual reports
- Verify information across multiple sources
- Include specific numerical values with units
- Note the year/date for time-series data
- For retrofit costs, search for country-specific data and recent estimates

Provide comprehensive, detailed research results with specific focus on Unit {unit_number}."""

    # Perform comprehensive web research using Gemini with rate limiting safeguards
    research_results = []
    
    try:
        if genai_client:
            print(f"[Session {session_id}] 🔍 Executing comprehensive research for Unit {unit_number}")
            
            # Add rate limiting delay before API call
            import time
            import random
            delay = random.uniform(2, 5)  # Random delay between 2-5 seconds
            print(f"[Session {session_id}] ⏰ Adding {delay:.1f}s rate limiting delay before research")
            time.sleep(delay)
            
            # Use Google Search API through genai client with production model
            response = genai_client.models.generate_content(
                model="gemini-2.0-flash",  # FIXED: Production model with 2,000 RPM vs 10 RPM
                contents=[research_prompt],
                config={
                    "tools": [{"google_search": {}}],
                    "temperature": 0,
                    "max_output_tokens": 6144,  # Increased for more comprehensive data
                }
            )
            
            if response and hasattr(response, 'text') and response.text:
                research_results.append(response.text)
                print(f"[Session {session_id}] ✅ Comprehensive research complete: {len(response.text)} chars")
            else:
                print(f"[Session {session_id}] ⚠️ No results from comprehensive research")
                
        else:
            print(f"[Session {session_id}] ❌ No genai_client available for Unit {unit_number} research")
            return f"Unit {unit_number} research failed - no genai_client available"
                
    except Exception as e:
        print(f"[Session {session_id}] ❌ Unit {unit_number} research failed: {e}")
        # Add exponential backoff for retries
        if "rate limit" in str(e).lower() or "quota" in str(e).lower():
            print(f"[Session {session_id}] 🔄 Detected rate limit, adding exponential backoff")
            import time
            time.sleep(10)  # Wait 10 seconds before proceeding
        return f"Unit {unit_number} research failed - {str(e)}"
    
    # Combine research results
    if research_results:
        combined_research = f"=== Unit {unit_number} Comprehensive Research Results ===\n\n"
        combined_research += research_results[0]
        print(f"[Session {session_id}] ✅ Unit {unit_number} research complete: {len(combined_research)} chars")
        return combined_research
    else:
        print(f"[Session {session_id}] ⚠️ No research results for Unit {unit_number}, using plant data")
        return f"=== Unit {unit_number} Research (Fallback to Plant Data) ===\n\n{plant_content}"





# PPA extraction function removed - consistently returned null values

# Grid connectivity extraction function removed - consistently returned null values
def placeholder_function_removed():
    pass





def validate_ppa_data(ppa: dict, plant_name: str, session_id: str) -> bool:
    """Validate PPA data quality and authenticity"""
    try:
        # Check for basic required fields
        if not ppa or not isinstance(ppa, dict):
            return False

        # Validate capacity
        capacity = ppa.get('capacity')
        if not capacity or capacity == "Not available":
            return False

        try:
            cap_num = float(capacity)
            if cap_num <= 0 or cap_num > 10000:  # Reasonable MW range
                return False
        except (ValueError, TypeError):
            return False

        # Validate respondents
        respondents = ppa.get('respondents', [])
        if not respondents:
            return False

        for respondent in respondents:
            name = respondent.get('name', '').strip()
            if not name or name == "Not available" or len(name) < 5:
                return False

            # Filter out generic names
            generic_patterns = ['utility company', 'power company', 'electricity board', 'buyer', 'offtaker']
            if any(pattern in name.lower() for pattern in generic_patterns):
                return False

        print(f"[Session {session_id}] ✅ PPA data validated for {plant_name}")
        return True

    except Exception as e:
        print(f"[Session {session_id}] ⚠️ PPA validation error: {e}")
        return False


def validate_grid_data(grid_connection: dict, plant_name: str, country: str, session_id: str) -> bool:
    """Validate grid connectivity data quality and authenticity"""
    try:
        if not grid_connection or not isinstance(grid_connection, dict):
            return False

        details = grid_connection.get('details', [])
        if not details:
            return False

        for detail in details:
            substation_name = detail.get('substation_name', '').strip()
            if not substation_name or substation_name == "Not available":
                return False

            # Filter out generic names
            generic_names = ['main substation', 'grid substation', 'transmission substation']
            if substation_name.lower() in generic_names or len(substation_name) < 10:
                return False

            # Validate coordinates if present
            lat = detail.get('latitude', '')
            lon = detail.get('longitude', '')
            if lat and lon and lat != "Not available" and lon != "Not available":
                try:
                    lat_num, lon_num = float(lat), float(lon)
                    if not (-90 <= lat_num <= 90 and -180 <= lon_num <= 180):
                        return False
                except (ValueError, TypeError):
                    return False

        print(f"[Session {session_id}] ✅ Grid data validated for {plant_name}")
        return True

    except Exception as e:
        print(f"[Session {session_id}] ⚠️ Grid validation error: {e}")
        return False

# Grid validation function removed - no longer needed

def extract_optimized_ppa_details(plant_name: str, country: str, session_id: str) -> list:
    """
    OPTIMIZED PPA extraction using domain team's proven prompt structure and targeted search strategy

    Based on domain team format:
    1. Contracted capacity: e.g., 500 MW
    2. Start date (YYYY/MM/DD): e.g., 2011/09/08
    3. End date (YYYY/MM/DD): e.g., 2029/12/01
    4. Tenure: e.g., 25 years
    5. Respondent name: e.g., Eskom
    6. Capacity contracted with each respondent: e.g., 500 MW
    7. Tariff price: e.g., 10
    8. Tariff currency: e.g., AUD
    9. Tariff unit: e.g., AUD/kWh
    """
    from agent.registry_nodes import get_web_search_function
    from langchain_google_genai import ChatGoogleGenerativeAI
    import os
    import json
    import re

    print(f"[Session {session_id}] 🎯 OPTIMIZED PPA extraction for {plant_name} in {country}")

    try:
        web_search_fn = get_web_search_function()

        # STAGE 1: Primary Plant-Level Search (High-Value Sources)
        primary_queries = [
            f'"{plant_name}" {country} power purchase agreement PPA contract details',
            f'"{plant_name}" {country} electricity sales agreement offtaker buyer',
            f'"{plant_name}" annual report PPA contract financial statements',
            f'"{plant_name}" regulatory filing power purchase agreement tariff',
            f'"{plant_name}" investor presentation electricity sales contract'
        ]

        all_search_content = ""
        for query in primary_queries:
            try:
                print(f"[Session {session_id}] 🔍 Primary PPA Query: {query}")
                search_results = web_search_fn(query)
                if search_results:
                    content = " ".join([r.get("content", "") for r in search_results[:3]])
                    all_search_content += f"\n--- {query} ---\n{content}\n"
            except Exception as e:
                print(f"[Session {session_id}] ⚠️ Primary search error: {e}")
                continue

        if not all_search_content.strip():
            print(f"[Session {session_id}] ⚠️ No primary search content found")
            return []

        # OPTIMIZED EXTRACTION using domain team's exact format
        model = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            api_key=os.getenv("GEMINI_API_KEY"),
            temperature=0
        )

        ppa_extraction_prompt = f"""
Extract PPA details for {plant_name} in {country} using this EXACT format and filling in each field with the correct information:

1. Contracted capacity: e.g., 500 MW
2. Start date (YYYY/MM/DD): e.g., 2011/09/08
3. End date (YYYY/MM/DD): e.g., 2029/12/01
4. Tenure: e.g., 25 years
5. Respondent name: e.g., Eskom
6. Capacity contracted with each respondent: e.g., 500 MW
7. Tariff price: e.g., 10
8. Tariff currency: e.g., AUD
9. Tariff unit: e.g., AUD/kWh

SEARCH FOCUS: Annual reports, regulatory filings, investor presentations, utility procurement documents, PUC filings.
VALIDATION: Ensure dates are logical, capacity matches plant size, respondent is real utility/company.

Return as JSON array:
[
  {{
    "capacity": "500",
    "capacity_unit": "MW",
    "start_date": "2011-09-08",
    "end_date": "2029-12-01",
    "tenure": "25",
    "tenure_type": "Years",
    "respondents": [
      {{
        "name": "Eskom Holdings SOC Ltd",
        "capacity": "500",
        "currency": "AUD",
        "price": "10.0",
        "price_unit": "AUD/kWh"
      }}
    ]
  }}
]

CRITICAL: Only extract information that is EXPLICITLY mentioned. Use "Not available" for missing fields.
If no real PPA information is found, return: []

SEARCH RESULTS:
{all_search_content[:3000]}
"""

        response = model.invoke(ppa_extraction_prompt)
        content = response.content.strip()

        print(f"[Session {session_id}] 🔍 PPA extraction response preview: {content[:200]}...")

        # Extract JSON from response
        json_match = re.search(r'\[.*\]', content, re.DOTALL)
        if json_match:
            try:
                json_str = json_match.group()
                ppa_data = json.loads(json_str)

                if isinstance(ppa_data, list) and ppa_data:
                    # Validate and filter real PPA data
                    validated_ppas = []
                    for ppa in ppa_data:
                        if validate_ppa_data(ppa, plant_name, session_id):
                            validated_ppas.append(ppa)

                    if validated_ppas:
                        print(f"[Session {session_id}] ✅ Extracted {len(validated_ppas)} validated PPA contracts")

                        # STAGE 2: Enhanced nested search for missing details
                        enhanced_ppas = enhance_ppa_with_nested_search(validated_ppas, plant_name, country, session_id)
                        return enhanced_ppas

            except json.JSONDecodeError as e:
                print(f"[Session {session_id}] ⚠️ PPA JSON parsing failed: {e}")

        print(f"[Session {session_id}] ⚠️ No valid PPA data extracted")
        return []

    except Exception as e:
        print(f"[Session {session_id}] ❌ Optimized PPA extraction error: {e}")
        return []


    genai_client = Client(api_key=os.getenv("GEMINI_API_KEY"))

    all_search_content = ""

    for query in ppa_queries:
        try:
            print(f"[Session {session_id}] 🔍 PPA Query: {query}")

            # Use Google Search API through genai client
            search_prompt = f"""Search the web for: {query}

Provide comprehensive search results with specific focus on Power Purchase Agreement details."""

            response = genai_client.models.generate_content(
                model="gemini-2.0-flash-exp",
                contents=search_prompt,
                config={
                    "tools": [{"google_search": {}}],
                    "temperature": 0.1,
                }
            )

            if response and response.text:
                all_search_content += f"\n\n--- Search for: {query} ---\n"
                all_search_content += response.text

        except Exception as e:
            error_msg = str(e).lower()
            if "429" in error_msg or "rate limit" in error_msg or "quota" in error_msg:
                print(f"[Session {session_id}] 🚨 API Rate Limit detected: {e}")
                print(f"[Session {session_id}] ⏳ Skipping remaining PPA queries due to rate limiting")
                break  # Stop further queries to avoid more rate limit errors
            else:
                print(f"[Session {session_id}] ⚠️ PPA search error: {e}")
            continue

    if not all_search_content.strip():
        return []

    # Extract PPA details using AI
    model = ChatGoogleGenerativeAI(
        model="gemini-2.0-flash-exp",
        api_key=os.getenv("GEMINI_API_KEY"),
        temperature=0.1
    )

    ppa_extraction_prompt = f"""
Extract Power Purchase Agreement (PPA) details for {plant_name} from the following search results.

EXTRACTION STRATEGY:
1. Look for ANY mention of power sales, electricity contracts, or energy agreements
2. Extract PARTIAL information even if complete details aren't available
3. Use "Not available" for missing fields rather than omitting contracts
4. Include multiple contracts if found
5. Look for regulatory filings, press releases, and financial reports
6. Search for terms like: PPA, power purchase, electricity sales, energy contract, offtaker, buyer

Focus on finding:
1. PPA capacity (in MW) - any power sales amount
2. Contract start and end dates - any timeline mentioned
3. Buyer/offtaker names (utilities, corporations, government entities)
4. Pricing information (tariff rates, $/MWh, cents/kWh)
5. Contract tenure/duration - any contract period
6. Currency of the contract
7. Contract type (renewable, fossil, hybrid)

Search Results:
{all_search_content}

Return ONLY a JSON array of PPA objects in this exact format:
[
  {{
    "capacity": "500",
    "capacity_unit": "MW",
    "start_date": "2020-01-01",
    "end_date": "2045-01-01",
    "tenure": 25,
    "tenure_type": "years",
    "respondents": [
      {{
        "name": "State Electricity Board",
        "capacity": "300",
        "currency": "INR",
        "price": "4.50",
        "price_unit": "INR/kWh"
      }}
    ]
  }}
]

IMPORTANT: Extract contracts even with partial information. Use "Not available" for missing fields.
If no PPA information is found, return: []
"""

    try:
        response = model.invoke(ppa_extraction_prompt)
        content = response.content.strip()

        # Extract JSON from response with robust error handling
        import json
        import re

        print(f"[Session {session_id}] 🔍 PPA response preview: {content[:200]}...")

        # Method 1: Try to find JSON array
        json_match = re.search(r'\[.*?\]', content, re.DOTALL)
        if json_match:
            try:
                json_str = json_match.group()
                # Clean up common JSON issues
                json_str = json_str.replace('\n', ' ').replace('\r', ' ')
                # Fix trailing commas
                json_str = re.sub(r',\s*]', ']', json_str)
                json_str = re.sub(r',\s*}', '}', json_str)

                ppa_data = json.loads(json_str)

                # Filter out fake/template data
                real_ppas = []
                for ppa in ppa_data:
                    if is_real_ppa_data(ppa):
                        real_ppas.append(ppa)
                    else:
                        print(f"[Session {session_id}] 🚫 Filtered out fake PPA: {ppa.get('respondents', [{}])[0].get('name', 'Unknown') if ppa.get('respondents') else 'Unknown'}")

                print(f"[Session {session_id}] ✅ Extracted {len(real_ppas)} real PPA contracts (filtered from {len(ppa_data)})")
                return real_ppas
            except json.JSONDecodeError as e:
                print(f"[Session {session_id}] ⚠️ JSON parsing failed: {e}")
                print(f"[Session {session_id}] 🔍 Problematic JSON: {json_str[:300]}...")

        # Method 2: Try to extract individual JSON objects
        json_objects = re.findall(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', content, re.DOTALL)
        if json_objects:
            ppa_list = []
            for obj_str in json_objects:
                try:
                    # Enhanced JSON cleaning
                    obj_str = obj_str.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
                    # Remove trailing commas before closing braces/brackets
                    obj_str = re.sub(r',\s*}', '}', obj_str)
                    obj_str = re.sub(r',\s*]', ']', obj_str)
                    # Fix common quote issues
                    obj_str = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', obj_str)
                    # Remove extra commas
                    obj_str = re.sub(r',+', ',', obj_str)

                    ppa_obj = json.loads(obj_str)
                    ppa_list.append(ppa_obj)
                except json.JSONDecodeError as e:
                    print(f"[Session {session_id}] ⚠️ Failed to parse PPA object: {e}")
                    continue

            if ppa_list:
                # Filter out fake/template data
                real_ppas = []
                for ppa in ppa_list:
                    if is_real_ppa_data(ppa):
                        real_ppas.append(ppa)
                    else:
                        print(f"[Session {session_id}] 🚫 Filtered out fake PPA: {ppa.get('respondents', [{}])[0].get('name', 'Unknown') if ppa.get('respondents') else 'Unknown'}")

                print(f"[Session {session_id}] ✅ Extracted {len(real_ppas)} real PPA contracts via enhanced object parsing (filtered from {len(ppa_list)})")
                return real_ppas

        # Method 3: Try to fix and parse as simple array
        try:
            # Extract content between first [ and last ]
            start_bracket = content.find('[')
            end_bracket = content.rfind(']')
            if start_bracket != -1 and end_bracket != -1:
                array_content = content[start_bracket:end_bracket+1]
                # Apply aggressive cleaning
                array_content = re.sub(r',\s*]', ']', array_content)
                array_content = re.sub(r',\s*}', '}', array_content)
                array_content = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', array_content)

                ppa_data = json.loads(array_content)
                print(f"[Session {session_id}] ✅ Extracted {len(ppa_data)} PPA contracts via aggressive cleaning")

                return ppa_data
        except json.JSONDecodeError:
            pass

        print(f"[Session {session_id}] ⚠️ No valid PPA JSON found in response")
        return []

    except Exception as e:
        print(f"[Session {session_id}] ❌ PPA extraction error: {e}")
        return []

def enhance_ppa_with_nested_search(ppa_data: list, plant_name: str, country: str, session_id: str) -> list:
    """
    STAGE 2: Enhanced nested search for missing PPA details using respondent names as context
    """
    from agent.registry_nodes import get_web_search_function
    from langchain_google_genai import ChatGoogleGenerativeAI
    import os

    print(f"[Session {session_id}] 🎯 NESTED PPA enhancement for {plant_name}")

    try:
        web_search_fn = get_web_search_function()
        model = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            api_key=os.getenv("GEMINI_API_KEY"),
            temperature=0
        )

        enhanced_ppas = []

        for ppa in ppa_data:
            enhanced_ppa = ppa.copy()
            respondents = ppa.get('respondents', [])

            for i, respondent in enumerate(respondents):
                respondent_name = respondent.get('name', '')
                if not respondent_name or respondent_name == "Not available":
                    continue

                # Identify missing fields
                missing_fields = []
                if not respondent.get('price') or respondent.get('price') == "Not available":
                    missing_fields.append('tariff price')
                if not respondent.get('currency') or respondent.get('currency') == "Not available":
                    missing_fields.append('tariff currency')
                if not respondent.get('capacity') or respondent.get('capacity') == "Not available":
                    missing_fields.append('capacity allocation')

                if missing_fields:
                    print(f"[Session {session_id}] 🔍 Nested search for {respondent_name}: {missing_fields}")

                    # Targeted nested queries using respondent name as context
                    nested_queries = [
                        f'"{respondent_name}" "{plant_name}" PPA tariff rate price contract',
                        f'"{respondent_name}" power purchase agreement {plant_name} capacity MW',
                        f'"{respondent_name}" electricity contract {plant_name} price currency'
                    ]

                    nested_content = ""
                    for query in nested_queries:
                        try:
                            search_results = web_search_fn(query)
                            if search_results:
                                content = " ".join([r.get("content", "") for r in search_results[:2]])
                                nested_content += content + " "
                        except Exception as e:
                            continue

                    if nested_content.strip():
                        # Extract missing details using AI
                        nested_prompt = f"""
Extract missing PPA details for {respondent_name} and {plant_name}:

Missing fields: {missing_fields}

Return ONLY a JSON object with found values:
{{
  "price": "4.50" (if found, tariff rate),
  "currency": "USD" (if found),
  "price_unit": "USD/kWh" (if found),
  "capacity": "500" (if found, in MW)
}}

Search Results:
{nested_content[:1500]}
"""

                        try:
                            response = model.invoke(nested_prompt)
                            content = response.content.strip()

                            import json
                            import re
                            json_match = re.search(r'\{[^{}]*\}', content)
                            if json_match:
                                extracted_details = json.loads(json_match.group())

                                # Update respondent with found information
                                for field, value in extracted_details.items():
                                    if value and value != "Not available":
                                        enhanced_ppa['respondents'][i][field] = value
                                        print(f"[Session {session_id}] ✅ Enhanced {respondent_name}: {field} = {value}")

                        except Exception as e:
                            print(f"[Session {session_id}] ⚠️ Nested extraction error: {e}")

            enhanced_ppas.append(enhanced_ppa)

        return enhanced_ppas

    except Exception as e:
        print(f"[Session {session_id}] ❌ PPA nested enhancement error: {e}")
        return ppa_data

def enhance_grid_with_nested_search(grid_data: list, plant_name: str, country: str, plant_data: dict, session_id: str) -> list:
    """
    STAGE 2: Enhanced nested search for missing grid details using substation names as context
    """
    from agent.registry_nodes import get_web_search_function
    from langchain_google_genai import ChatGoogleGenerativeAI
    import os
    import math

    print(f"[Session {session_id}] 🎯 NESTED Grid enhancement for {plant_name}")

    try:
        web_search_fn = get_web_search_function()
        model = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            api_key=os.getenv("GEMINI_API_KEY"),
            temperature=0
        )

        # Get plant coordinates for distance calculation
        plant_lat = plant_data.get('latitude')
        plant_lon = plant_data.get('longitude')

        enhanced_grids = []

        for grid_connection in grid_data:
            enhanced_grid = grid_connection.copy()
            details = grid_connection.get('details', [])

            enhanced_details = []
            for detail in details:
                enhanced_detail = detail.copy()
                substation_name = detail.get('substation_name', '')

                if substation_name and substation_name != "Not available":
                    # Identify missing fields
                    missing_fields = []
                    if not detail.get('latitude') or detail.get('latitude') == "Not available":
                        missing_fields.append('latitude')
                    if not detail.get('longitude') or detail.get('longitude') == "Not available":
                        missing_fields.append('longitude')
                    if not detail.get('capacity') or detail.get('capacity') == "Not available":
                        missing_fields.append('line capacity')

                    if missing_fields:
                        print(f"[Session {session_id}] 🔍 Nested search for {substation_name}: {missing_fields}")

                        # ENHANCED: Targeted nested queries using substation name as context
                        nested_queries = [
                            f'"{substation_name}" latitude longitude coordinates',  # Simple format as requested
                            f'"{substation_name}" GPS coordinates latitude longitude location',
                            f'"{substation_name}" transmission capacity kV voltage rating',
                            f'"{substation_name}" {plant_name} transmission line distance'
                        ]

                        nested_content = ""
                        for query in nested_queries:
                            try:
                                search_results = web_search_fn(query)
                                if search_results:
                                    content = " ".join([r.get("content", "") for r in search_results[:2]])
                                    nested_content += content + " "
                            except Exception as e:
                                continue

                        if nested_content.strip():
                            # Extract missing details using AI
                            nested_prompt = f"""
Extract missing grid connectivity details for {substation_name}:

Missing fields: {missing_fields}

Return ONLY a JSON object with found values:
{{
  "latitude": "-35.014" (if found, decimal degrees),
  "longitude": "158.030" (if found, decimal degrees),
  "capacity": "400" (if found, voltage in kV),
  "substation_type": "Transmission Substation, 400kV" (if found)
}}

Search Results:
{nested_content[:1500]}
"""

                            try:
                                response = model.invoke(nested_prompt)
                                content = response.content.strip()

                                import json
                                import re
                                json_match = re.search(r'\{[^{}]*\}', content)
                                if json_match:
                                    extracted_details = json.loads(json_match.group())

                                    # Update detail with found information
                                    for field, value in extracted_details.items():
                                        if value and value != "Not available":
                                            enhanced_detail[field] = value
                                            print(f"[Session {session_id}] ✅ Enhanced {substation_name}: {field} = {value}")

                            except Exception as e:
                                print(f"[Session {session_id}] ⚠️ Nested grid extraction error: {e}")

                    # Calculate distance if both plant and substation coordinates are available
                    sub_lat = enhanced_detail.get('latitude')
                    sub_lon = enhanced_detail.get('longitude')

                    if (plant_lat and plant_lon and sub_lat and sub_lon and
                        all(coord != "Not available" for coord in [plant_lat, plant_lon, sub_lat, sub_lon])):
                        try:
                            distance = calculate_distance(float(plant_lat), float(plant_lon),
                                                        float(sub_lat), float(sub_lon))
                            enhanced_detail['projects'] = [{"distance": f"{distance:.1f} km"}]
                            print(f"[Session {session_id}] ✅ Calculated distance to {substation_name}: {distance:.1f} km")
                        except Exception as e:
                            print(f"[Session {session_id}] ⚠️ Distance calculation error: {e}")

                enhanced_details.append(enhanced_detail)

            enhanced_grid['details'] = enhanced_details
            enhanced_grids.append(enhanced_grid)

        return enhanced_grids

    except Exception as e:
        print(f"[Session {session_id}] ❌ Grid nested enhancement error: {e}")
        return grid_data


def calculate_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """Calculate distance between two points using Haversine formula"""
    try:
        import math

        # Convert latitude and longitude from degrees to radians
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)

        # Haversine formula
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad
        a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))

        # Radius of earth in kilometers
        r = 6371

        distance = c * r
        return distance
    except Exception as e:
        print(f"Distance calculation error: {e}")
        return 0.0

def extract_optimized_grid_connectivity(plant_name: str, country: str, plant_data: dict, session_id: str) -> list:
    """
    OPTIMIZED Grid connectivity extraction using domain team's proven prompt structure

    Based on domain team format:
    1. Name of the substation to which the power station is connected: e.g., Bluewaters Substation
    2. Latitude of the substation (decimal degrees): e.g., -35.014
    3. Longitude of the substation (decimal degrees): e.g., 158.030
    4. Line capacity of the substation: e.g., 400 kV
    5. Linear distance of the substation from the power station: e.g., 26 km
    """
    from agent.registry_nodes import get_web_search_function
    from langchain_google_genai import ChatGoogleGenerativeAI
    import os
    import json
    import re

    print(f"[Session {session_id}] 🎯 OPTIMIZED Grid connectivity extraction for {plant_name} in {country}")

    try:
        web_search_fn = get_web_search_function()

        # Extract plant context for better search
        plant_location = plant_data.get("plant_address", "")
        plant_capacity = plant_data.get("total_capacity", "")

        # STAGE 1: Primary Grid Infrastructure Search
        primary_queries = [
            f'"{plant_name}" {country} grid connection substation transmission infrastructure',
            f'"{plant_name}" {country} electrical substation coordinates latitude longitude',
            f'"{plant_name}" transmission planning study substation details',
            f'"{plant_name}" interconnection agreement grid connection point',
            f'"{plant_name}" power evacuation substation voltage level'
        ]

        # Add location-specific queries if available
        if plant_location:
            primary_queries.extend([
                f'"{plant_name}" {plant_location} substation grid connection',
                f'{plant_location} transmission substation near "{plant_name}"'
            ])

        all_search_content = ""
        for query in primary_queries:
            try:
                print(f"[Session {session_id}] 🔍 Primary Grid Query: {query}")
                search_results = web_search_fn(query)
                if search_results:
                    content = " ".join([r.get("content", "") for r in search_results[:3]])
                    all_search_content += f"\n--- {query} ---\n{content}\n"
            except Exception as e:
                print(f"[Session {session_id}] ⚠️ Primary grid search error: {e}")
                continue

        if not all_search_content.strip():
            print(f"[Session {session_id}] ⚠️ No primary grid search content found")
            return []

        # OPTIMIZED EXTRACTION using domain team's exact format
        model = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            api_key=os.getenv("GEMINI_API_KEY"),
            temperature=0
        )

        grid_extraction_prompt = f"""
Extract grid connectivity details for {plant_name} in {country} using this EXACT format and filling in each field with the correct information:

1. Name of the substation to which the power station is connected: e.g., Bluewaters Substation
2. Latitude of the substation (decimal degrees): e.g., -35.014
3. Longitude of the substation (decimal degrees): e.g., 158.030
4. Line capacity of the substation: e.g., 400 kV
5. Linear distance of the substation from the power station: e.g., 26 km

SEARCH FOCUS: Transmission studies, interconnection agreements, utility infrastructure maps, ISO/RTO planning documents.
VALIDATION: Coordinates within country, voltage ≥69kV, distance reasonable, substation name specific.

Return as JSON array:
[
  {{
    "details": [
      {{
        "substation_name": "Bluewaters Substation",
        "substation_type": "Transmission Substation, 400kV",
        "capacity": "400",
        "latitude": "-35.014",
        "longitude": "158.030",
        "projects": [
          {{
            "distance": "26.0 km"
          }}
        ]
      }}
    ]
  }}
]

CRITICAL: Only extract information that is EXPLICITLY mentioned. Use "Not available" for missing fields.
If no real grid connectivity information is found, return: []

SEARCH RESULTS:
{all_search_content[:3000]}
"""

        response = model.invoke(grid_extraction_prompt)
        content = response.content.strip()

        print(f"[Session {session_id}] 🔍 Grid extraction response preview: {content[:200]}...")

        # Extract JSON from response
        json_match = re.search(r'\[.*\]', content, re.DOTALL)
        if json_match:
            try:
                json_str = json_match.group()
                grid_data = json.loads(json_str)

                if isinstance(grid_data, list) and grid_data:
                    # Validate and filter real grid data
                    validated_grids = []
                    for grid_connection in grid_data:
                        if validate_grid_data(grid_connection, plant_name, country, session_id):
                            validated_grids.append(grid_connection)

                    if validated_grids:
                        print(f"[Session {session_id}] ✅ Extracted {len(validated_grids)} validated grid connections")

                        # STAGE 2: Enhanced nested search for missing details
                        enhanced_grids = enhance_grid_with_nested_search(validated_grids, plant_name, country, plant_data, session_id)
                        return enhanced_grids

            except json.JSONDecodeError as e:
                print(f"[Session {session_id}] ⚠️ Grid JSON parsing failed: {e}")

        print(f"[Session {session_id}] ⚠️ No valid grid connectivity data extracted")
        return []

    except Exception as e:
        print(f"[Session {session_id}] ❌ Optimized grid connectivity extraction error: {e}")
        return []


# Grid connectivity enhancement function removed - consistently returned null values
def enhance_grid_connectivity_with_context_removed():
    return []

# PPA extraction from plant function removed - consistently returned null values
def extract_ppa_details_from_plant_removed():
    return []





def process_single_unit(state: OverallState):
    """Process a single unit completely independently in parallel using matrix approach."""
    unit_number = state.get("unit_number", "1")
    session_id = state.get("session_id", "unknown")
    my_matrix_slot = state.get("my_matrix_slot", unit_number)
    
    print(f"[Session {session_id}] 🔄 STARTING INDEPENDENT UNIT {unit_number} PROCESSING (MATRIX SLOT: {my_matrix_slot})")
    
    # Add staggered delay to avoid hitting rate limits
    import time
    import random
    delay = random.uniform(5, 15)  # Random delay between 5-15 seconds
    print(f"[Session {session_id}] Adding {delay:.1f}s staggered delay to avoid rate limits...")
    time.sleep(delay)
    
    # Update this unit's matrix slot to indicate processing has started
    matrix_update = {
        my_matrix_slot: {
            'status': 'processing_started',
            'ready': True
        }
    }
    
    flag_update = {
        f'unit_{unit_number}_processing': True
    }
    
    print(f"[Session {session_id}] ✅ Unit {unit_number} ready for independent processing")
    
    return {
        "unit_state_matrix": matrix_update,
        "unit_flags": flag_update,
    }

# Create the collector function for gathering all unit results using matrix system
def collect_unit_results(state: OverallState):
    """Collect results from all parallel unit processing flows using matrix system."""
    session_id = state.get("session_id", "unknown")
    
    print(f"[Session {session_id}] 📊 COLLECTING UNIT RESULTS (MATRIX APPROACH)")
    
    # Check if this is a "no units found" case
    if state.get("no_units_found", False):
        print(f"[Session {session_id}] No units were found - creating empty result")
        return {
            "messages": [AIMessage(content="Unit-level research was not performed as no units were identified from the plant data.")],
            "sources_gathered": [],
            "unit_results": [],
            "all_units": [],
            "final_stage": "no_units"
        }
    
    # Get matrix data
    unit_state_matrix = state.get("unit_state_matrix", {})
    unit_completion_status = state.get("unit_completion_status", {})
    extracted_units = state.get("extracted_units", [])
    
    print(f"[Session {session_id}] Matrix available units: {list(unit_state_matrix.keys())}")
    print(f"[Session {session_id}] Expected units: {extracted_units}")
    print(f"[Session {session_id}] Completion status: {unit_completion_status}")
    
    # Check if this is a single unit result or final aggregation
    current_unit = state.get("unit_number")
    if current_unit:
        # This is a single unit result - store it in the matrix and return minimal state
        print(f"[Session {session_id}] Collecting result for Unit {current_unit}")
        
        current_messages = state.get("messages", [])
        current_sources = state.get("sources_gathered", [])
        
        # Extract the unit-specific content
        unit_content = ""
        if current_messages:
            for msg in reversed(current_messages):
                if hasattr(msg, 'content'):
                    unit_content = msg.content
                    break
        
        if not unit_content:
            unit_content = f"No specific information was gathered for Unit {current_unit}."
        
        # Return the individual unit result (this gets aggregated by LangGraph)
        return {
            "messages": [AIMessage(content=unit_content)],
            "sources_gathered": current_sources,
            "unit_number": current_unit,
            "unit_completed": True
        }
    
    else:
        # This is the final aggregation step - collect all completed units from matrix
        print(f"[Session {session_id}] Final aggregation: collecting all unit results from matrix")
        
        all_messages = []
        all_sources = []
        unit_results = []
        
        for unit_num in extracted_units:
            unit_data = unit_state_matrix.get(unit_num, {})
            if unit_data.get('processing_complete', False):
                unit_content = unit_data.get('research_results', f"No information for Unit {unit_num}")
                unit_sources = unit_data.get('sources_gathered', [])
                
                all_messages.append(AIMessage(content=unit_content))
                all_sources.extend(unit_sources)
                unit_results.append({
                    "unit_number": unit_num,
                    "content": unit_content,
                    "sources": unit_sources
                })
                
                print(f"[Session {session_id}] ✅ Collected Unit {unit_num} from matrix")
            else:
                print(f"[Session {session_id}] ⚠️ Unit {unit_num} not yet complete in matrix")
        
        print(f"[Session {session_id}] 📊 Final collection: {len(unit_results)} units, {len(all_sources)} total sources")
        
        # Save single plant JSON to local file
        if unit_results:
            import os
            import json
            from datetime import datetime
            
            # Create output directory
            output_dir = "/Users/<USER>/Downloads/SE-TRANSITION-AGI-TECH-59e0b03f7500f5023133d0951c61b51e859cbb42/plant_outputs"
            os.makedirs(output_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Get plant information from state or messages
            plant_name = "Unknown_Plant"
            plant_data = {}
            
            # Try to extract plant data from messages
            for message in state.get("messages", []):
                if hasattr(message, 'content') and isinstance(message.content, str):
                    try:
                        content = message.content
                        if "PLANT-LEVEL INFORMATION" in content:
                            start_idx = content.find('{')
                            end_idx = content.rfind('}') + 1
                            if start_idx != -1 and end_idx != -1:
                                json_str = content[start_idx:end_idx]
                                plant_data = json.loads(json_str)
                                if 'name' in plant_data:
                                    plant_name = plant_data['name']
                                break
                    except:
                        continue
            
            # Add the fixed fields to plant data
            if plant_data:
                plant_data["is_ppa"] = "yes"
                plant_data["is_retrofitting"] = "yes"
                plant_data["plant_transition_life"] = 5
            
            # Create complete plant JSON
            complete_plant_json = {
                "plant_info": plant_data,
                "units": unit_results,
                "processing_metadata": {
                    "plant_name": plant_name,
                    "units_processed": len(unit_results),
                    "processing_timestamp": timestamp,
                    "session_id": session_id,
                    "processing_mode": "Single Plant"
                }
            }
            
            # Clean plant name for filename
            safe_plant_name = "".join(c for c in plant_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_plant_name = safe_plant_name.replace(' ', '_')
            
            filename = f"{safe_plant_name}_{timestamp}.json"
            filepath = os.path.join(output_dir, filename)
            
            try:
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(complete_plant_json, f, indent=2, ensure_ascii=False)
                print(f"[{session_id}] 💾 Saved single plant JSON to: {filepath}")
                
                # Add file path to final message
                final_message_content = f"Plant processing complete with {len(unit_results)} units.\n\n📁 SAVED TO: {filepath}"
                all_messages.append(AIMessage(content=final_message_content))
                
            except Exception as e:
                print(f"[{session_id}] ❌ Failed to save plant JSON: {e}")
        
        return {
            "messages": all_messages,
            "sources_gathered": all_sources,
            "unit_results": unit_results,
            "all_units": extracted_units,
            "final_stage": "units_complete"
        }


# Unit Processing Functions with Isolated State
def process_isolated_unit(state: dict):
    """Process a single unit using completely isolated state to prevent conflicts."""
    unit_number = state.get("unit_number", "1")
    unit_session_id = state.get("unit_session_id", "unknown")
    batch_id = state.get("unit_batch_id", "unknown")
    
    print(f"[{unit_session_id}] 🔄 STARTING ISOLATED UNIT {unit_number} PROCESSING (Batch: {batch_id})")
    print(f"[{unit_session_id}] 🔍 DEBUG: State keys: {list(state.keys())}")
    print(f"[{unit_session_id}] 🔍 DEBUG: Plant context: {state.get('plant_name', 'N/A')}")
    
    # Add staggered delay to avoid hitting rate limits
    import time
    import random
    delay = random.uniform(2, 8)  # Shorter delay for batched processing
    print(f"[{unit_session_id}] Adding {delay:.1f}s staggered delay to avoid rate limits...")
    time.sleep(delay)
    
    print(f"[{unit_session_id}] ✅ Unit {unit_number} ready for isolated processing")
    
    # Return the same isolated state with processing flag set
    return {
        **state,
        "processing_complete": False,  # Will be set to True after finalization
    }


def unit_generate_query_isolated(state: dict, config: RunnableConfig) -> dict:
    """Generate unit-level queries using isolated state and unit-specific prompts."""
    global _current_unit_name_mapping  # Declare global at the top of function

    unit_number = state.get("unit_number", "1")
    unit_session_id = state.get("unit_session_id", "unknown")

    print(f"[{unit_session_id}] ===== UNIT {unit_number} QUERY GENERATION =====")
    print(f"[{unit_session_id}] 🔍 DEBUG: Plant context: {state.get('plant_name', 'N/A')} ({state.get('plant_country', 'N/A')})")
    
    configurable = Configuration.from_runnable_config(config)
    
    # Initialize Gemini for unit query generation
    llm = ChatGoogleGenerativeAI(
        model=configurable.query_generator_model,
        temperature=0,
        max_retries=3,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    structured_llm = llm.with_structured_output(SearchQueryList)
    
    # Get current date and research topic
    current_date = get_current_date()
    research_topic = get_research_topic(state["messages"])
    
    print(f"[{unit_session_id}] Research topic: '{research_topic}'")
    print(f"[{unit_session_id}] Unit number: {unit_number}")

    # Get original unit name from mapping if available (FIXED: Handle non-numeric unit IDs)
    try:
        unit_key = int(unit_number)
    except (ValueError, TypeError):
        unit_key = unit_number  # Use string key for alphanumeric unit IDs

    original_unit_name = _current_unit_name_mapping.get(unit_key, f"Unit {unit_number}")
    print(f"[{unit_session_id}] Original unit name: '{original_unit_name}'")

    # Use unit-specific prompt with original unit name
    formatted_prompt = unit_query_writer_instructions.format(
        unit_number=f"{original_unit_name} (Unit {unit_number})",  # Use both original name and number
        number_queries=state.get("initial_search_query_count", 6),
        current_date=current_date,
        research_topic=research_topic
    )
    
    # Generate the search queries
    try:
        result = structured_llm.invoke(formatted_prompt)
        print(f"[{unit_session_id}] Generated {len(result.query)} unit-level queries")
        
        return {
            **state,
            "search_query": result.query,
            "research_loop_count": state.get("research_loop_count", 0),
        }
    except Exception as e:
        print(f"[{unit_session_id}] ❌ Query generation failed: {e}")
        return {
            **state,
            "search_query": [f"Unit {unit_number} technical specifications"],  # Fallback query
            "processing_error": f"Query generation error: {str(e)}",
        }


def unit_web_research_isolated(state: dict, config: RunnableConfig) -> dict:
    """Perform web research for unit-level information using isolated state."""
    unit_number = state.get("unit_number", "1")
    unit_session_id = state.get("unit_session_id", "unknown")
    
    print(f"[{unit_session_id}] ===== UNIT {unit_number} WEB RESEARCH =====")
    
    try:
        configurable = Configuration.from_runnable_config(config)
        search_queries = state.get("search_query", [])
        
        if not search_queries:
            print(f"[{unit_session_id}] No search queries available")
            return {
                **state,
                "web_research_result": ["No search queries generated"],
                "sources_gathered": [],
            }
    except Exception as e:
        print(f"[{unit_session_id}] ❌ Configuration error: {e}")
        return {
            **state,
            "web_research_result": [f"Configuration error: {str(e)}"],
            "sources_gathered": [],
            "processing_error": f"Config error: {str(e)}"
        }
    
    # Process each query (limit to prevent excessive API calls)
    max_queries = min(len(search_queries), 5)  # Limit queries for efficiency
    research_results = []
    all_sources = []
    
    for i, query in enumerate(search_queries[:max_queries]):
        print(f"[{unit_session_id}] Searching query {i+1}/{max_queries}: '{query}'")
        
        try:
            # Use unit-specific web search prompt
            formatted_prompt = unit_web_searcher_instructions.format(
                unit_number=unit_number,
                current_date=get_current_date(),
                research_topic=get_research_topic(state["messages"])
            )
            
            # Perform web search using Google Search API
            search_result = genai_client.models.generate_content(
                model=configurable.query_generator_model,
                contents=query,  # Use the actual search query, not the formatted prompt
                config={
                    "tools": [{"google_search": {}}],
                    "temperature": 0,
                },
            )
            
            # Process results
            resolved_urls = resolve_urls(
                search_result.candidates[0].grounding_metadata.grounding_chunks, i
            )
            citations = get_citations(search_result, resolved_urls)
            modified_text = insert_citation_markers(search_result.text, citations)
            sources = [item for citation in citations for item in citation["segments"]]
            
            research_results.append(modified_text)
            all_sources.extend(sources)
            
            print(f"[{unit_session_id}] ✅ Query {i+1} completed, found {len(sources)} sources")
            
            # Add delay between queries to avoid rate limits
            time.sleep(1)
            
        except Exception as e:
            print(f"[{unit_session_id}] ❌ Search query {i+1} failed: {e}")
            research_results.append(f"Search failed for query: {query}")
    
    return {
        **state,
        "web_research_result": research_results,
        "sources_gathered": all_sources,
    }


def unit_finalize_isolated(state: dict, config: RunnableConfig) -> dict:
    """Finalize unit processing and extract structured unit-level data."""
    unit_number = state.get("unit_number", "1")
    unit_session_id = state.get("unit_session_id", "unknown")
    
    print(f"[{unit_session_id}] ===== UNIT {unit_number} FINALIZATION =====")
    
    configurable = Configuration.from_runnable_config(config)
    reasoning_model = state.get("reasoning_model", "") or configurable.reasoning_model
    
    # Use unit-specific answer instructions
    current_date = get_current_date()
    research_topic = get_research_topic(state["messages"])
    summaries = "\n---\n".join(state.get("web_research_result", []))
    
    # Get plant context from isolated state with safe defaults
    plant_type = state.get("plant_technology", "coal")
    plant_name = state.get("plant_name", "Unknown")
    plant_id = state.get("plant_id", "1")
    
    try:
        formatted_prompt = unit_answer_instructions.format(
            unit_number=unit_number,
            current_date=current_date,
            research_topic=research_topic,
            summaries=summaries,
            plant_type=plant_type,
            plant_name=plant_name,
            plant_id=plant_id
        )
        print(f"[{unit_session_id}] ✅ Prompt formatted successfully with plant context: {plant_name} ({plant_type})")
        
    except KeyError as ke:
        print(f"[{unit_session_id}] ❌ Template formatting KeyError: {ke}")
        # Create a simplified prompt without problematic placeholders
        formatted_prompt = f"""Extract unit-level technical information for Unit {unit_number}.

Current date: {current_date}
Research topic: {research_topic}
Plant context: {plant_name} ({plant_type})

Summaries:
{summaries}

Please extract all available technical specifications for this unit and format as JSON."""
        
    except Exception as te:
        print(f"[{unit_session_id}] ❌ Template formatting error: {te}")
        # Fallback to basic prompt
        formatted_prompt = f"Extract technical data for Unit {unit_number} from: {summaries}"
    
    # Initialize LLM with structured output for unit data
    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        # CRITICAL: Check if this is a USA plant and use Excel tool
        plant_name = state.get("plant_name", "Unknown Plant")
        country = state.get("country", "Unknown")

        # CRITICAL FIX: Get country from database BEFORE Excel tool check
        if country == "Unknown":
            try:
                from agent.database_manager import get_database_manager
                db_manager = get_database_manager()
                plant_info = db_manager.check_plant_exists(plant_name)
                if plant_info and plant_info.get("country"):
                    country = plant_info["country"]
                    print(f"[{unit_session_id}] 🌍 Retrieved country from database: {country}")
            except Exception as e:
                print(f"[{unit_session_id}] ⚠️ Could not retrieve country: {e}")

        print(f"[{unit_session_id}] 🔍 Checking data source for {plant_name} in {country}")

        # If USA plant, use Excel tool instead of web search
        if country and ("united states" in country.lower() or "usa" in country.lower() or "us" in country.lower()):
            print(f"[{unit_session_id}] 🇺🇸 USA PLANT DETECTED - Using Excel tool for {plant_name}")

            try:
                # Import and use Excel tool
                import sys
                import os
                sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')
                from excel_power_plant_tool import ExcelPowerPlantTool

                # Initialize Excel tool with correct path
                excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx', unit_session_id)

                # Get unit data from Excel
                excel_results = excel_tool.get_plant_data(plant_name, unit_number)

                if excel_results:
                    excel_data = excel_results[0]  # Get first result
                    print(f"[{unit_session_id}] ✅ Found Excel data for {plant_name} Unit {unit_number}")
                    print(f"[{unit_session_id}] 📊 Excel data keys: {list(excel_data.keys())}")

                    # Return Excel data directly - it's already in the correct format
                    return {**state, "unit_data": excel_data}
                else:
                    print(f"[{unit_session_id}] ❌ No Excel data found for {plant_name} Unit {unit_number}")
                    print(f"[{unit_session_id}] 🔄 Falling back to web search extraction...")

            except Exception as e:
                print(f"[{unit_session_id}] ❌ Excel tool failed: {e}")
                print(f"[{unit_session_id}] 🔄 Falling back to web search extraction...")

        # MULTI-STAGE EXTRACTION for non-USA plants or Excel fallback
        print(f"[{unit_session_id}] 🔄 Starting multi-stage unit data extraction...")

        # Import the multi-stage extraction functions
        from agent.unit_extraction_stages import (
            extract_basic_unit_info,
            extract_performance_metrics,
            extract_fuel_and_emissions,
            extract_economic_data,
            extract_technical_parameters,
            combine_unit_data
        )
        
        # Country already retrieved above before Excel tool check

        # CRITICAL FIX: Get plant_type from plant data in state, not from extracted plant_technology
        plant_data_from_state = state.get("plant_data", {})
        correct_plant_type = plant_data_from_state.get("plant_type", state.get("plant_technology", "coal"))

        print(f"[{unit_session_id}] 🔍 DEBUG MATRIX PLANT TYPE:")
        print(f"[{unit_session_id}]   - state.get('plant_technology'): '{state.get('plant_technology', 'NOT_FOUND')}'")
        print(f"[{unit_session_id}]   - plant_data.get('plant_type'): '{plant_data_from_state.get('plant_type', 'NOT_FOUND')}'")
        print(f"[{unit_session_id}]   - Using plant_type: '{correct_plant_type}'")

        # UPDATED PLANT CONTEXT: Separate plant_id (sequential) and plant_uid (UUID)
        plant_context = {
            "plant_name": plant_name,
            "plant_type": correct_plant_type,  # Use correct plant_type from plant data
            "plant_technology": correct_plant_type,  # Keep for backward compatibility
            "plant_id": "1",  # Default sequential number for matrix processing
            "plant_uid": state.get("plant_uid", ""),  # Plant UUID for pk field
            "country": country,  # Use actual country
            "org_id": state.get("org_id", ""),  # Organization UID
        }

        print(f"[{unit_session_id}] 🔧 PLANT CONTEXT (Matrix): plant_type='{plant_context['plant_type']}', plant_id='{plant_context['plant_id']}', plant_uid='{plant_context['plant_uid']}'")

        
        # Stage 1: Basic unit info (most important)
        print(f"[{unit_session_id}] 📊 Stage 1: Extracting basic unit information...")
        stage1_data = extract_basic_unit_info(summaries, unit_number, plant_context, reasoning_model)
        print(f"[{unit_session_id}] ✅ Stage 1 complete: {list(stage1_data.keys())}")
        
        # Stage 2: Performance metrics
        print(f"[{unit_session_id}] 📈 Stage 2: Extracting performance metrics...")
        stage2_data = extract_performance_metrics(summaries, unit_number, plant_context, reasoning_model)
        print(f"[{unit_session_id}] ✅ Stage 2 complete: {list(stage2_data.keys())}")
        
        # Stage 3: Fuel and emissions  
        print(f"[{unit_session_id}] ⛽ Stage 3: Extracting fuel and emission data...")
        stage3_data = extract_fuel_and_emissions(summaries, unit_number, plant_context, reasoning_model)
        print(f"[{unit_session_id}] ✅ Stage 3 complete: {list(stage3_data.keys())}")
        
        # Stage 4: Economic data
        print(f"[{unit_session_id}] 💰 Stage 4: Extracting economic data...")
        stage4_data = extract_economic_data(summaries, unit_number, plant_context, reasoning_model)
        print(f"[{unit_session_id}] ✅ Stage 4 complete: {list(stage4_data.keys())}")
        
        # Stage 5: Technical parameters
        print(f"[{unit_session_id}] ⚙️ Stage 5: Extracting technical parameters...")
        stage5_data = extract_technical_parameters(summaries, unit_number, plant_context, reasoning_model)
        print(f"[{unit_session_id}] ✅ Stage 5 complete: {list(stage5_data.keys())}")
        
        # Combine all stages
        print(f"[{unit_session_id}] 🔧 Combining all extraction stages...")
        unit_technical_data = combine_unit_data(
            [stage1_data, stage2_data, stage3_data, stage4_data, stage5_data],
            unit_number,
            plant_context
        )
        
        print(f"[{unit_session_id}] ✅ Multi-stage extraction complete!")
        print(f"[{unit_session_id}] 📋 Total fields extracted: {len(unit_technical_data)}")
        print(f"[{unit_session_id}] 🎯 Key data points:")
        print(f"[{unit_session_id}]   - Capacity: {unit_technical_data.get('capacity', 'N/A')}")
        print(f"[{unit_session_id}]   - Technology: {unit_technical_data.get('technology', 'N/A')}")
        print(f"[{unit_session_id}]   - Coal Type: {unit_technical_data.get('selected_coal_type', 'N/A')}")
        print(f"[{unit_session_id}]   - PLF Data: {len(unit_technical_data.get('plf', []))} records")
        
    except KeyError as ke:
        print(f"[{unit_session_id}] ❌ KeyError in structured extraction: {ke}")
        print(f"[{unit_session_id}] 🔄 Using fallback extraction with basic unit data")
        
        # Create basic unit data structure with proper plant context
        plant_name = state.get("plant_name", "Unknown Plant")

        # CRITICAL FIX: Get plant_type from plant data in state, not from extracted plant_technology
        plant_data_from_state = state.get("plant_data", {})
        correct_plant_type = plant_data_from_state.get("plant_type", state.get("plant_technology", "coal"))

        print(f"[{unit_session_id}] 🔍 DEBUG ISOLATED PLANT TYPE:")
        print(f"[{unit_session_id}]   - state.get('plant_technology'): '{state.get('plant_technology', 'NOT_FOUND')}'")
        print(f"[{unit_session_id}]   - plant_data.get('plant_type'): '{plant_data_from_state.get('plant_type', 'NOT_FOUND')}'")
        print(f"[{unit_session_id}]   - Using plant_type: '{correct_plant_type}'")

        sequential_plant_id = "1"  # Default sequential number for isolated processing
        plant_uid = state.get("plant_uid", "")  # Get UUID from state

        unit_technical_data = {
            "sk": f"unit#{correct_plant_type}#{unit_number}#plant#{sequential_plant_id}",
            "unit_number": unit_number,
            "plant_name": plant_name,  # Add plant name to unit data
            "plant_id": sequential_plant_id,  # Sequential number (1, 2, 3, etc.)
            "plant_uid": plant_uid,  # UUID from database
            "capacity": "Unknown",
            "capacity_unit": "MW",
            "technology": correct_plant_type,
            "boiler_type": "Unknown",
            "commencement_date": "",
            "remaining_useful_life": None,
            "unit_lifetime": None,
            "coal_unit_efficiency": "",

            "heat_rate": None,
            "heat_rate_unit": "kJ/kWh",

            "extraction_error": f"KeyError: {str(ke)}"
        }
        
    except Exception as e:
        print(f"[{unit_session_id}] ❌ General structured extraction failed: {e}")
        
        # Try fallback to regular completion
        try:
            result = llm.invoke(formatted_prompt)
            unit_technical_data = {
                "unit_number": unit_number,
                "extraction_result": result.content,
                "extraction_error": str(e)
            }
        except Exception as fallback_error:
            print(f"[{unit_session_id}] ❌ Fallback also failed: {fallback_error}")
            unit_technical_data = {
                "unit_number": unit_number,
                "extraction_error": f"Both structured and fallback failed: {str(e)} | {str(fallback_error)}"
            }
    
    # Mark processing as complete and store data for collection
    
    # Create comprehensive unit result message with the actual extracted data
    if unit_technical_data and isinstance(unit_technical_data, dict):
        # Convert the structured data to a formatted JSON message for easy collection
        import json
        unit_data_json = json.dumps(unit_technical_data, indent=2)
        result_message = f"Unit {unit_number} Technical Data:\n```json\n{unit_data_json}\n```"
        
        print(f"[{unit_session_id}] ✅ Created structured result message with real technical data")
    else:
        result_message = f"Unit {unit_number} processing completed but no structured data was extracted"
        print(f"[{unit_session_id}] ⚠️ No structured technical data available for Unit {unit_number}")
    
    # Enhanced debugging for result storage
    print(f"[{unit_session_id}] 🔍 UNIT {unit_number} RESULT STORAGE DEBUG:")
    print(f"[{unit_session_id}]   - Technical data keys: {list(unit_technical_data.keys()) if isinstance(unit_technical_data, dict) else 'Not dict'}")
    print(f"[{unit_session_id}]   - Storage key 1: unit_technical_data")
    print(f"[{unit_session_id}]   - Storage key 2: unit_{unit_number}_technical_data") 
    print(f"[{unit_session_id}]   - Message content length: {len(result_message)}")
    print(f"[{unit_session_id}]   - Plant context: {state.get('plant_name', 'N/A')}")
    print(f"[{unit_session_id}]   - Capacity extracted: {unit_technical_data.get('capacity', 'N/A') if isinstance(unit_technical_data, dict) else 'N/A'}")
    
    # SAVE UNIT JSON TO S3 (Isolated Unit Processing)
    unit_s3_url = None
    try:
        # Get main session ID from original state
        main_session_id = state.get("original_session_id", unit_session_id)
        plant_name = state.get("plant_name_for_s3") or state.get("plant_name", research_topic)
        
        # Store unit data to S3 with database lookup for UIDs
        if unit_technical_data and isinstance(unit_technical_data, dict):
            # SIMPLE DATABASE LOOKUP: Get plant_id for unit pk field
            plant_id = ""
            org_id = ""

            try:
                from agent.database_manager import get_database_manager
                db_manager = get_database_manager()

                # Get plant_id from database (units use plant_id as pk)
                existing_plant = db_manager.check_plant_exists(plant_name)
                if existing_plant:
                    plant_id = existing_plant.get("plant_id", "")
                    org_id = existing_plant.get("org_id", "")
                    print(f"[{unit_session_id}] ✅ Retrieved for unit {unit_number} (Isolated): plant_id='{plant_id}'")
                else:
                    print(f"[{unit_session_id}] ⚠️ Plant not found in database for unit (Isolated): {plant_name}")

            except Exception as e:
                print(f"[{unit_session_id}] ❌ Database lookup failed for unit (Isolated): {e}")

            # Set pk field from database lookup (units use plant_id as pk)
            if plant_id:
                unit_technical_data["pk"] = plant_id
                print(f"[{unit_session_id}] ✅ Set unit {unit_number} pk field (Isolated): {plant_id}")
            else:
                print(f"[{unit_session_id}] ⚠️ No plant_id available for unit {unit_number} pk field (Isolated)")

            # Format unit data before storage
            plant_uid = state.get("plant_uid", "")  # Get UUID from state for folder structure
            print(f"[{unit_session_id}] 🔍 DEBUG ISOLATED UNIT STORAGE: plant_uid from state = '{plant_uid}'")
            print(f"[{unit_session_id}] 🔍 DEBUG ISOLATED UNIT STORAGE: org_id = '{org_id}'")
            print(f"[{unit_session_id}] 🔍 DEBUG ISOLATED UNIT STORAGE: state keys = {list(state.keys())}")
            formatted_unit_data = process_unit_data_formatting(unit_technical_data, unit_session_id, plant_uid)
            unit_s3_url = store_unit_data(formatted_unit_data, plant_name, unit_number, main_session_id, org_id=org_id, plant_id=plant_uid)
            
            if unit_s3_url:
                print(f"[{unit_session_id}] ✅ Unit {unit_number} JSON saved to S3 (Isolated): {unit_s3_url}")
            else:
                print(f"[{unit_session_id}] ❌ Failed to save Unit {unit_number} JSON to S3 (Isolated)")
        else:
            print(f"[{unit_session_id}] ⚠️ No valid technical data to save for Unit {unit_number}")
            
    except Exception as s3_error:
        print(f"[{unit_session_id}] ❌ S3 storage error for Unit {unit_number} (Isolated): {str(s3_error)}")
    
    # Prepare S3 URL updates for the state
    s3_updates = {}
    if unit_s3_url:
        current_s3_urls = state.get("s3_json_urls", {})
        if "units" not in current_s3_urls:
            current_s3_urls["units"] = {}
        current_s3_urls["units"][unit_number] = unit_s3_url
        s3_updates["s3_json_urls"] = current_s3_urls
        
        current_storage_status = state.get("json_storage_complete", {})
        if "units" not in current_storage_status:
            current_storage_status["units"] = {}
        current_storage_status["units"][unit_number] = True
        s3_updates["json_storage_complete"] = current_storage_status
    
    result_state = {
        **state,
        # Store ONLY with unit-specific key to prevent overwrites between parallel units
        f"unit_{unit_number}_technical_data": unit_technical_data,  # unit_1_technical_data, unit_2_technical_data, etc.
        "processing_complete": True,
        "phase_complete": True,
        "messages": [AIMessage(content=result_message)],  # Store the structured data in the message
        "technical_data_available": bool(unit_technical_data),
        # Include S3 updates if successful
        **s3_updates
    }
    
    print(f"[{unit_session_id}] ✅ Unit {unit_number} finalization complete - data stored in state")
    return result_state


def collect_smart_batch_results(state: OverallState):
    """Collect results from smart batch parallel processing and determine next steps."""
    session_id = state.get("session_id", "unknown")
    
    print(f"[Session {session_id}] 📊 COLLECTING SMART BATCH RESULTS")
    
    # Check if this is a "no units found" case
    if state.get("no_units_found", False):
        print(f"[Session {session_id}] No units were found - creating empty result")
        return {
            **state,
            "messages": [AIMessage(content="No units found for processing")],
            "sources_gathered": [],
            "unit_processing_results": [],
            "final_stage": "units_complete"
        }
    
    # Get current batch processing state
    current_batch_number = state.get("batch_number", 0)
    total_batches = state.get("total_batches", 1)
    unit_batches = state.get("unit_batches", [])
    
    # Collect REAL results from current batch processing (from isolated unit states)
    current_batch_results = []
    batch_messages = []
    batch_sources = []
    
    # Get current batch to know which units we're expecting
    unit_batches = state.get("unit_batches", [])
    if current_batch_number < len(unit_batches):
        current_batch_units = unit_batches[current_batch_number]
        
        print(f"[Session {session_id}] Looking for real data from units: {current_batch_units}")
        print(f"[Session {session_id}] 🔍 DEBUG - Available state keys: {list(state.keys())}")
        print(f"[Session {session_id}] 🔍 DEBUG - State contains {len(state.get('messages', []))} messages")
        
        # Extract REAL unit processing results from the accumulated state
        # In LangGraph parallel processing, results accumulate in messages and other state fields
        messages = state.get("messages", [])
        sources = state.get("sources_gathered", [])
        
        # Look for actual unit technical data that was processed
        for unit_number in current_batch_units:
            unit_found = False
            unit_technical_data = {}
            unit_sources = []
            unit_error = ""
            
            # Method 1: Look for unit technical data in state (unit-specific keys only)
            possible_keys = [
                f"unit_{unit_number}_technical_data",  # Primary: unit_1_technical_data, unit_2_technical_data
                f"unit{unit_number}_technical_data",   # Secondary: unit1_technical_data, unit2_technical_data
                # NOTE: Removed generic "unit_technical_data" key to prevent overwrites between units
            ]
            
            for unit_key in possible_keys:
                if unit_key in state and state[unit_key]:
                    unit_technical_data = state[unit_key]
                    unit_found = True
                    print(f"[Session {session_id}] ✅ Found real technical data for Unit {unit_number} in state key: {unit_key}")
                    break
            
            # Method 2: Look in messages for unit-specific content
            if not unit_found and messages:
                print(f"[Session {session_id}] 🔍 Searching {len(messages)} messages for Unit {unit_number} data...")
                for i, msg in enumerate(messages):
                    if hasattr(msg, 'content') and isinstance(msg.content, str):
                        content = msg.content
                        # Check if this message contains unit-specific data
                        if f"Unit {unit_number}" in content or f'"unit_number": "{unit_number}"' in content:
                            print(f"[Session {session_id}] 🎯 Found Unit {unit_number} content in message {i}")
                            print(f"[Session {session_id}] 🔍 Message preview: {content[:200]}...")
                            # Try to extract JSON data from the message
                            try:
                                import json
                                import re
                                
                                # Look for JSON blocks in the message
                                json_matches = re.findall(r'```json\s*(\{.*?\})\s*```', content, re.DOTALL)
                                if json_matches:
                                    unit_technical_data = json.loads(json_matches[-1])  # Use last JSON block
                                    unit_found = True
                                    print(f"[Session {session_id}] ✅ Extracted real technical data for Unit {unit_number} from message")
                                    break
                                
                                # Also look for direct JSON content
                                if content.strip().startswith('{') and content.strip().endswith('}'):
                                    unit_technical_data = json.loads(content.strip())
                                    unit_found = True
                                    print(f"[Session {session_id}] ✅ Parsed real technical data for Unit {unit_number} from JSON message")
                                    break
                                    
                            except (json.JSONDecodeError, IndexError):
                                # If JSON parsing fails, at least capture the content
                                unit_technical_data = {
                                    "unit_number": unit_number,
                                    "raw_content": content,
                                    "data_source": "message_content"
                                }
                                unit_found = True
                                print(f"[Session {session_id}] ✅ Captured raw content for Unit {unit_number}")
                                break
            
            # Method 3: Look for unit-specific sources
            unit_sources = [src for src in sources if f"unit{unit_number}" in str(src).lower() or f"unit {unit_number}" in str(src).lower()]
            if not unit_sources:
                # If no unit-specific sources, take a portion of total sources (FIXED: Handle non-numeric unit IDs)
                sources_per_unit = len(sources) // len(current_batch_units) if current_batch_units else 0
                try:
                    unit_index = int(unit_number) - 1
                except (ValueError, TypeError):
                    # For alphanumeric unit IDs, use position in current_batch_units
                    unit_index = current_batch_units.index(unit_number) if unit_number in current_batch_units else 0

                start_idx = unit_index * sources_per_unit
                end_idx = start_idx + sources_per_unit
                unit_sources = sources[start_idx:end_idx] if sources else []
            
            # If we still don't have data, create a meaningful error message with debug info
            if not unit_found:
                unit_error = f"Real technical data for Unit {unit_number} was not found in state or messages"
                
                # Debug information to help identify the issue
                available_unit_keys = [key for key in state.keys() if 'unit' in key.lower()]
                print(f"[Session {session_id}] ❌ Unit {unit_number} not found. Available unit keys in state: {available_unit_keys}")
                print(f"[Session {session_id}] ❌ Messages with 'Unit' content: {[i for i, msg in enumerate(messages) if hasattr(msg, 'content') and 'Unit' in str(msg.content)]}")
                
                # Get plant context for sk generation
                # CRITICAL FIX: Get plant_type from plant data in state, not from extracted plant_technology
                plant_data_from_state = state.get("plant_data", {})
                correct_plant_type = plant_data_from_state.get("plant_type", state.get("plant_technology", "coal"))

                print(f"[Session {session_id}] 🔍 DEBUG ERROR FALLBACK PLANT TYPE:")
                print(f"[Session {session_id}]   - state.get('plant_technology'): '{state.get('plant_technology', 'NOT_FOUND')}'")
                print(f"[Session {session_id}]   - plant_data.get('plant_type'): '{plant_data_from_state.get('plant_type', 'NOT_FOUND')}'")
                print(f"[Session {session_id}]   - Using plant_type: '{correct_plant_type}'")

                sequential_plant_id = "1"  # Default sequential number
                plant_uid = state.get("plant_uid", "")  # Get UUID from state

                unit_technical_data = {
                    "sk": f"unit#{correct_plant_type}#{unit_number}#plant#{sequential_plant_id}",
                    "unit_number": unit_number,
                    "plant_id": sequential_plant_id,  # Sequential number (1, 2, 3, etc.)
                    "plant_uid": plant_uid,  # UUID from database
                    "capacity": "",
                    "capacity_unit": "",
                    "technology": "",
                    "processing_error": unit_error,
                    "debug_available_keys": available_unit_keys,
                    "data_available": False
                }
                print(f"[Session {session_id}] ⚠️ No real data found for Unit {unit_number}, using empty template")
            else:
                # Ensure unit_number is set in the data
                if isinstance(unit_technical_data, dict):
                    unit_technical_data["unit_number"] = unit_number
                    unit_technical_data["data_available"] = True

                    # Ensure required fields are present
                    sequential_plant_id = "1"  # Default sequential number
                    plant_uid = state.get("plant_uid", "")  # Get UUID from state

                    # Ensure plant_id and plant_uid fields are present
                    if "plant_id" not in unit_technical_data:
                        unit_technical_data["plant_id"] = sequential_plant_id
                    if "plant_uid" not in unit_technical_data:
                        unit_technical_data["plant_uid"] = plant_uid

                    # Ensure sk field is present and correct at the top
                    if "sk" not in unit_technical_data or not unit_technical_data["sk"]:
                        # CRITICAL FIX: Get plant_type from plant data in state, not from extracted plant_technology
                        plant_data_from_state = state.get("plant_data", {})
                        correct_plant_type = plant_data_from_state.get("plant_type", state.get("plant_technology", "coal"))
                        sk_value = f"unit#{correct_plant_type}#{unit_number}#plant#{sequential_plant_id}"

                        # Create new ordered dict with sk first
                        ordered_unit_data = {"sk": sk_value}
                        for key, value in unit_technical_data.items():
                            if key != "sk":
                                ordered_unit_data[key] = value
                        unit_technical_data = ordered_unit_data
            
            current_batch_results.append({
                "unit_number": unit_number,
                "unit_data": unit_technical_data,
                "processing_complete": unit_found,
                "processing_error": unit_error,
                "batch_id": f"B{current_batch_number + 1}",
                "sources_count": len(unit_sources)
            })
            
            # Log processing results (DO NOT add to batch_messages for interface)
            if unit_found:
                capacity = unit_technical_data.get("capacity", "Unknown")
                technology = unit_technical_data.get("technology", "Unknown")
                print(f"[Session {session_id}] ✅ Unit {unit_number}: {capacity} MW {technology} - Real data extracted successfully")
            else:
                print(f"[Session {session_id}] ⚠️ Unit {unit_number}: Processing completed but data extraction needs debugging")
                
            # DO NOT add unit messages to batch_messages - this was causing interface display issue
            batch_sources.extend(unit_sources)
            
            print(f"[Session {session_id}] ✅ Collected Unit {unit_number} result: {len(unit_sources)} sources, data_found={unit_found}")
    else:
        print(f"[Session {session_id}] ⚠️ Batch {current_batch_number} not found in batch configuration")
    
    # Update processing results with current batch
    all_processing_results = state.get("unit_processing_results", [])
    all_processing_results.extend(current_batch_results)
    
    print(f"[Session {session_id}] 📊 Batch {current_batch_number + 1}/{total_batches} collection:")
    print(f"[Session {session_id}]   - Current batch results: {len(current_batch_results)}")
    print(f"[Session {session_id}]   - Total results so far: {len(all_processing_results)}")
    
    # Check if we need to process more batches
    next_batch_number = current_batch_number + 1
    
    if next_batch_number < total_batches:
        # More batches to process
        print(f"[Session {session_id}] 🔄 Preparing next batch {next_batch_number + 1}/{total_batches}")
        
        return {
            **state,
            "batch_number": next_batch_number,
            "unit_processing_results": all_processing_results,
            "messages": state.get("messages", []) + batch_messages,
            "sources_gathered": state.get("sources_gathered", []) + batch_sources,
            "continue_batch_processing": True,  # Flag to continue with next batch
        }
    else:
        # All batches complete - finalize results
        print(f"[Session {session_id}] ✅ All batches complete! Final processing...")
        
        # Aggregate all results
        all_messages = state.get("messages", []) + batch_messages
        all_sources = state.get("sources_gathered", []) + batch_sources
        
        # Create summary of unit processing
        unit_summary = []
        for result in all_processing_results:
            unit_summary.append({
                "unit_number": result["unit_number"],
                "processing_complete": result["processing_complete"],
                "has_technical_data": bool(result["unit_data"]),
                "batch_id": result["batch_id"],
                "error": result["processing_error"] if result["processing_error"] else None
            })
        
        # SEQUENTIAL APPROACH: Process each unit individually and create separate responses
        print(f"[Session {session_id}] 🔄 SEQUENTIAL: Processing {len(all_processing_results)} units individually")
        
        # Sort units by unit_number to ensure Unit 1 comes first
        sorted_results = sorted(all_processing_results, key=lambda x: int(x.get("unit_number", 0)))
        print(f"[Session {session_id}] 📋 SEQUENTIAL: Unit order: {[r.get('unit_number') for r in sorted_results]}")
        
        # Define unit-level fields from unit_level.json (excluding plant-level fields)
        unit_level_fields = {
            "sk", "auxiliary_power_consumed", "boiler_type", "capacity", "capacity_unit",
            "capex_required_renovation_closed_cycle", "capex_required_renovation_closed_cycle_unit",
            "capex_required_renovation_open_cycle", "capex_required_renovation_open_cycle_unit",
            "capex_required_retrofit_biomass", "capex_required_retrofit_biomass_unit", "closed_cylce_gas_turbine_efficency",
            "combined_cycle_heat_rate", "commencement_date", "efficiency_loss_biomass_cofiring", "emission_factor",
            "fuel_type", "gcv_biomass", "gcv_biomass_unit", "gcv_coal", "gcv_coal_unit",
            "gcv_natural_gas", "gcv_natural_gas_unit", "gross_power_generation", "heat_rate", "heat_rate_unit",
            "open_cycle_gas_turbine_efficency", "open_cycle_heat_rate", "PAF", "plant_id", "plf",
            "remaining_useful_life",
            "technology", "coal_unit_efficiency", "unit_lifetime", "unit_number"
        }
        
        successful_units = 0
        
        for result in sorted_results:
            unit_data = result["unit_data"]
            unit_number = result["unit_number"]
            
            print(f"[Session {session_id}] 🔍 SEQUENTIAL: Processing Unit {unit_number}")
            
            if result["processing_complete"] and unit_data:
                print(f"[Session {session_id}] 🔍 SEQUENTIAL: Unit {unit_number} - Available fields: {list(unit_data.keys())}")
                
                # Use the EXACT same logic that worked for Unit 2 (from line 2702)
                # Don't filter - use whatever data extraction provided
                import json
                unit_data_json = json.dumps(unit_data, indent=2)
                result_message = f"Unit {unit_number} Technical Data:\n```json\n{unit_data_json}\n```"
                
                # Create individual response for this unit
                unit_message = AIMessage(content=result_message)
                all_messages.append(unit_message)
                successful_units += 1
                print(f"[Session {session_id}] ✅ SEQUENTIAL: Created response for Unit {unit_number} with complete technical data")
            else:
                # Create error message for failed unit
                error_data = {
                    "unit_number": str(unit_number),
                    "processing_error": result.get("processing_error", "Unknown error"),
                    "processing_complete": False
                }
                import json
                error_json_content = json.dumps(error_data, indent=2)
                error_message = f"Unit {unit_number} Processing Error:\n```json\n{error_json_content}\n```"
                
                error_unit_message = AIMessage(content=error_message)
                all_messages.append(error_unit_message)
                print(f"[Session {session_id}] ⚠️ SEQUENTIAL: Created error response for Unit {unit_number}")
        
        print(f"[Session {session_id}] 🎉 SEQUENTIAL: Unit processing complete!")
        print(f"[Session {session_id}] 📊 Final stats:")
        print(f"[Session {session_id}]   - Total units processed: {len(all_processing_results)}")
        print(f"[Session {session_id}]   - Successful units: {successful_units}")
        print(f"[Session {session_id}]   - Individual responses created: {successful_units}")
        print(f"[Session {session_id}]   - Total sources collected: {len(all_sources)}")
        
        return {
            **state,
            "messages": all_messages,
            "sources_gathered": all_sources,
            "unit_processing_results": all_processing_results,
            "unit_summary": unit_summary,
            "unit_processing_summary": f"Unit processing completed: {successful_units}/{len(all_processing_results)} units successful",
            "final_stage": "units_complete",
            "continue_batch_processing": False,
        }


builder.add_node("process_all_units", process_all_units)
builder.add_node("extract_unit_data_fresh", extract_unit_data_fresh)  # NEW FRESH EXTRACTION
builder.add_node("process_single_unit", process_single_unit)
builder.add_node("collect_unit_results", collect_unit_results)

# SIMPLE: Add AGI UID saver node
builder.add_node("save_agi_org_id_directly", save_agi_org_id_directly)

# Add new isolated unit processing nodes
builder.add_node("process_isolated_unit", process_isolated_unit)
builder.add_node("unit_generate_query_isolated", unit_generate_query_isolated)
builder.add_node("unit_web_research_isolated", unit_web_research_isolated)
builder.add_node("unit_finalize_isolated", unit_finalize_isolated)

# Add smart batch result collector
builder.add_node("collect_smart_batch_results", collect_smart_batch_results)

# Multi-plant processing removed - single plant only

# FLOW DEFINITION
# ===============
# SMART ROUTING: Check if org level is already complete
def route_graph_start(state: OverallState) -> str:
    """Route graph start based on whether organization level is complete"""
    org_complete = state.get("org_level_complete", False)
    skip_org_discovery = state.get("skip_org_discovery", False)
    session_id = state.get("session_id", "unknown")

    print(f"[Session {session_id}] 🔍 DEBUG ROUTING: org_complete={org_complete}, skip_org_discovery={skip_org_discovery}")
    print(f"[Session {session_id}] 🔍 DEBUG ROUTING: All state keys = {list(state.keys())}")
    print(f"[Session {session_id}] 🔍 DEBUG ROUTING: skip_org_discovery raw value = {repr(state.get('skip_org_discovery'))}")

    if org_complete:
        print(f"[Session {session_id}] 🔀 Organization level already complete - skipping to plant level")
        return "plant_generate_query"  # Skip org discovery, go directly to plant research
    elif skip_org_discovery:
        print(f"[Session {session_id}] 🔀 Entity extraction: Skip org discovery, go to parallel processing (includes image extraction)")
        return "spawn_parallel_processing_with_uid"  # Skip discovery, but still do parallel processing for image extraction
    else:
        print(f"[Session {session_id}] 🔀 Starting from organization level")
        return "save_agi_org_id_directly"  # Normal flow

# Add conditional start routing
builder.add_conditional_edges(
    START,
    route_graph_start,
    {
        "save_agi_org_id_directly": "save_agi_org_id_directly",  # Normal flow
        "plant_generate_query": "plant_generate_query",  # Skip to plant level
        "spawn_parallel_processing_with_uid": "spawn_parallel_processing_with_uid"  # Entity extraction: Skip discovery, go to parallel processing
    }
)

# After saving AGI UID, go directly to quick org discovery
builder.add_edge("save_agi_org_id_directly", "quick_org_discovery")

# Quick discovery leads to UID generation (only for plant UIDs now)
builder.add_edge("quick_org_discovery", "generate_uid")

# DUPLICATE FUNCTION REMOVED - Function is now defined earlier in the file

# After UID generation, conditionally route to database population or parallel processing
builder.add_conditional_edges(
    "generate_uid",
    route_after_uid_generation,
    {
        "populate_database_async": "populate_database_async",  # New plants, save to DB
        "spawn_parallel_processing_with_uid": "spawn_parallel_processing_with_uid"  # Route to wrapper node
    }
)

# CRITICAL FIX: After database population, route to entity extraction or single plant processing
builder.add_conditional_edges(
    "populate_database_async",
    route_after_database_population,  # Use routing function to decide next step
    {
        "entity_extraction_trigger": "entity_extraction_trigger",  # Multi-plant: trigger entity extraction
        "spawn_parallel_processing_with_uid": "spawn_parallel_processing_with_uid"  # Single plant: normal processing
    }
)

# Entity extraction trigger routes to END (runs in background)
builder.add_edge("entity_extraction_trigger", END)

# Add conditional edge from wrapper node to actual parallel processing
builder.add_conditional_edges(
    "spawn_parallel_processing_with_uid",
    spawn_parallel_processing_with_uid,  # Use the original function for parallel processing
    ["org_generate_query", "extract_images_parallel"]  # Both 3-level extraction and image extraction
)

# DUPLICATE FUNCTION REMOVED - Function is defined earlier in the file

# Multi-plant extraction removed

# SINGLE PLANT APPROACH - No multi-plant counter needed

# ORGANIZATION LEVEL FLOW
# -----------------------
# Organization-level query generation continues as normal

# Define a modified continue_to_web_research function for organization level
def continue_to_org_web_research(state: QueryGenerationState):
    """LangGraph node that sends the search queries to the organization web research node."""
    print(f"Sending {len(state['query_list'])} queries for organization level")
    
    return [
        Send("org_web_research", {
            "search_query": search_query, 
            "id": int(idx),
            "search_phase": 1
        })
        for idx, search_query in enumerate(state["query_list"])
    ]

# Generate web research queries for organization level
builder.add_conditional_edges(
    "org_generate_query", 
    continue_to_org_web_research, 
    ["org_web_research"]
)

# Reflect on organization-level web research
builder.add_edge("org_web_research", "org_reflection")

# Define a modified evaluate_research function for organization level
def evaluate_org_research(state: ReflectionState) -> str:
    """LangGraph routing function that determines the next step in the organization research flow."""
    print(f"Evaluating organization research, loop count: {state.get('research_loop_count', 0)}")
    
    # Safe access to is_sufficient with fallback
    is_sufficient = state.get("is_sufficient", False)
    if is_sufficient is None:
        is_sufficient = False
        print(f"Warning: is_sufficient was None, defaulting to False")

    # Use the same logic as evaluate_research but always for phase 1
    # CRITICAL FIX: Reduce loop count to prevent recursion limit
    if is_sufficient or state.get("research_loop_count", 0) >= 2:  # Reduced from 3 to 2
        print(f"Organization research terminating: is_sufficient={is_sufficient}, loops={state.get('research_loop_count', 0)}")
        return "org_finalize_answer"
    else:
        return [
            Send(
                "org_web_research",
                {
                    "search_query": follow_up_query,
                    "id": state.get("number_of_ran_queries", 0) + int(idx),
                    "search_phase": 1,
                },
            )
            for idx, follow_up_query in enumerate(state["follow_up_queries"])
        ]

# Either continue organization research or finalize organization answer
builder.add_conditional_edges(
    "org_reflection", 
    evaluate_org_research, 
    ["org_web_research", "org_finalize_answer"]
)

# After organization-level is complete, transition to plant-level
builder.add_edge("org_finalize_answer", "clear_state_for_plant_level")

# PLANT LEVEL FLOW
# ----------------
# Start plant-level with query generation
builder.add_edge("clear_state_for_plant_level", "plant_generate_query")

# Define a modified continue_to_web_research function for plant level
def continue_to_plant_web_research(state: QueryGenerationState):
    """LangGraph node that sends the search queries to the plant web research node."""
    print(f"Sending {len(state['query_list'])} queries for plant level")
    
    return [
        Send("plant_web_research", {
            "search_query": search_query, 
            "id": int(idx),
            "search_phase": 2
        })
        for idx, search_query in enumerate(state["query_list"])
    ]

# Generate web research queries for plant level
builder.add_conditional_edges(
    "plant_generate_query", 
    continue_to_plant_web_research, 
    ["plant_web_research"]
)

# Reflect on plant-level web research
builder.add_edge("plant_web_research", "plant_reflection")

# Define a modified evaluate_research function for plant level
def evaluate_plant_research(state: ReflectionState) -> str:
    """LangGraph routing function that determines the next step in the plant research flow."""
    print(f"Evaluating plant research, loop count: {state.get('research_loop_count', 0)}")
    
    # Safe access to is_sufficient with fallback
    is_sufficient = state.get("is_sufficient", False)
    if is_sufficient is None:
        is_sufficient = False
        print(f"Warning: is_sufficient was None, defaulting to False")

    # Use the same logic as evaluate_research but always for phase 2
    # CRITICAL FIX: Reduce loop count to prevent recursion limit
    if is_sufficient or state.get("research_loop_count", 0) >= 2:  # Reduced from 3 to 2
        print(f"Plant research terminating: is_sufficient={is_sufficient}, loops={state.get('research_loop_count', 0)}")
        return "plant_finalize_answer"
    else:
        return [
            Send(
                "plant_web_research",
                {
                    "search_query": follow_up_query,
                    "id": state.get("number_of_ran_queries", 0) + int(idx),
                    "search_phase": 2,
                },
            )
            for idx, follow_up_query in enumerate(state["follow_up_queries"])
        ]

# Either continue plant research or finalize plant answer
builder.add_conditional_edges(
    "plant_reflection", 
    evaluate_plant_research, 
    ["plant_web_research", "plant_finalize_answer"]
)

# Multi-plant detection removed

# Multi-plant routing removed - always go to process_all_units
builder.add_edge("plant_finalize_answer", "process_all_units")

# Route units to fresh extraction
builder.add_conditional_edges(
    "process_all_units",
    lambda state: "extract_unit_data_fresh" if state.get("ready_for_unit_processing") else "collect_unit_results",
    ["extract_unit_data_fresh", "collect_unit_results"]
)

# Single plant mode - go to finalization after extraction (includes transition plan)
builder.add_edge("extract_unit_data_fresh", "finalize_single_plant")

# Multi-plant extraction removed

# Multi-plant processing edges removed

# Conditional edge function for spawning parallel units
# State Copy Creation Functions
def create_isolated_unit_state(unit_number: str, batch_id: str, state: OverallState) -> dict:
    """Create completely isolated state for a single unit to prevent concurrent access."""
    session_id = state.get("session_id", "unknown")
    unit_session_id = f"unit{unit_number}-batch{batch_id}-{session_id}"
    
    # Extract plant context for unit processing
    plant_name = ""
    plant_country = ""
    plant_technology = ""
    plant_id = "0"  # Default plant ID
    
    messages = state.get("messages", [])
    
    # First pass: Extract plant name from messages (prioritize AI responses with finalized data)
    for message in messages:
        if hasattr(message, 'content') and isinstance(message.content, str):
            content = message.content
            content_lower = content.lower()
            
            # Check if this is a HumanMessage (user input) - more reliable check
            message_type = str(type(message).__name__)
            
            # Try to extract from AI response with finalized plant data first
            if 'AI' in message_type and not plant_name:
                try:
                    import json
                    import re
                    
                    # Method 1: Try to find JSON with plant name
                    if '"name"' in content:
                        start_idx = content.find('{')
                        end_idx = content.rfind('}') + 1
                        if start_idx != -1 and end_idx != -1:
                            json_str = content[start_idx:end_idx]
                            plant_data = json.loads(json_str)
                            if 'name' in plant_data:
                                plant_name = plant_data['name']
                                print(f"[DEBUG] Extracted plant name from AI JSON: '{plant_name}'")
                                break
                    
                    # Method 2: Try regex patterns for common plant name formats
                    patterns = [
                        r'Plant Name[:\s]*([^\n\r,]+(?:Power Station|Power Plant|Plant))',
                        r'Name[:\s]*([^\n\r,]+(?:Power Station|Power Plant|Plant))',
                        r'Plant[:\s]*([^\n\r,]+(?:Power Station|Power Plant))',
                        r'([A-Z][^,\n\r]+(?:Power Station|Power Plant|Thermal Plant))',
                        r'Plant:\s*([^\n\r,]+)',
                        r'([A-Z][A-Z\s]+(?:Power Station|Power Plant|Thermal Plant))',  # Added for NTPC patterns
                    ]
                    
                    for pattern in patterns:
                        match = re.search(pattern, content, re.IGNORECASE)
                        if match:
                            extracted = match.group(1).strip()
                            if len(extracted) > 5 and extracted.lower() != 'unknown':  # Valid plant name
                                plant_name = extracted
                                print(f"[DEBUG] Extracted plant name from AI pattern: '{plant_name}'")
                                break
                    
                    if plant_name:
                        break
                        
                except Exception as e:
                    print(f"[DEBUG] Plant name extraction error: {e}")
                    pass
            
            # Fallback: Extract from HumanMessage
            if 'Human' in message_type and not plant_name:
                # Try to extract plant name from the original query
                if "power station" in content_lower:
                    plant_name = content.split("Power Station")[0].strip() + " Power Station"
                elif "power plant" in content_lower:
                    plant_name = content.split("Power Plant")[0].strip() + " Power Plant"
                else:
                    plant_name = content.strip()
                print(f"[DEBUG] Extracted plant name from HumanMessage: '{plant_name}'")
                break
    
    # Second pass: Extract plant context from all messages
    for message in messages:
        if hasattr(message, 'content') and isinstance(message.content, str):
            content = message.content
            content_lower = content.lower()
            
            # Extract basic plant context from any message
            if "plant" in content_lower or "power" in content_lower:
                # Country extraction
                if "taiwan" in content_lower and not plant_country:
                    plant_country = "Taiwan"
                    print(f"[DEBUG] Extracted plant country: Taiwan")
                elif "india" in content_lower and not plant_country:
                    plant_country = "India"
                    print(f"[DEBUG] Extracted plant country: India")
                elif "china" in content_lower and not plant_country:
                    plant_country = "China"
                    print(f"[DEBUG] Extracted plant country: China")
                elif "japan" in content_lower and not plant_country:
                    plant_country = "Japan"
                    print(f"[DEBUG] Extracted plant country: Japan")
                
                # Technology extraction
                if "coal" in content_lower and not plant_technology:
                    plant_technology = "Coal"
                    print(f"[DEBUG] Extracted plant technology: Coal")
                elif ("gas" in content_lower or "natural gas" in content_lower) and not plant_technology:
                    plant_technology = "Natural Gas"
                    print(f"[DEBUG] Extracted plant technology: Natural Gas")
                elif "nuclear" in content_lower and not plant_technology:
                    plant_technology = "Nuclear"
                    print(f"[DEBUG] Extracted plant technology: Nuclear")
                elif "solar" in content_lower and not plant_technology:
                    plant_technology = "Solar"
                    print(f"[DEBUG] Extracted plant technology: Solar")
                
                # Try to extract plant ID from JSON responses
                if '"plant_id"' in content and (not plant_id or plant_id == "0"):
                    try:
                        import re
                        id_match = re.search(r'"plant_id":\s*(\d+)', content)
                        if id_match:
                            plant_id = id_match.group(1)
                            print(f"[DEBUG] Extracted plant ID: {plant_id}")
                    except:
                        pass
    
    # Create completely isolated unit state - NO SHARED REFERENCES
    isolated_state = {
        # Unit identification
        "unit_number": unit_number,
        "unit_session_id": unit_session_id,
        "unit_batch_id": batch_id,
        
        # Unit processing state (completely independent)
        "messages": messages.copy(),  # Copy messages, don't reference
        "search_query": [],
        "web_research_result": [],
        "sources_gathered": [],
        
        # Unit processing control
        "search_phase": 3,  # Always 3 for unit-level
        "research_loop_count": 0,
        "continue_research": False,
        "phase_complete": False,
        
        # Unit configuration (copied from main state)
        "initial_search_query_count": state.get("initial_search_query_count", 6),
        "max_research_loops": state.get("max_research_loops", 3),
        "reasoning_model": state.get("reasoning_model", ""),
        
        # Unit results (completely isolated)
        "unit_technical_data": {},
        "processing_complete": False,
        "processing_error": "",
        
        # Plant context (read-only)
        "plant_name": plant_name,
        "plant_country": plant_country,
        "plant_technology": plant_technology,
        "plant_id": plant_id,
    }
    
    print(f"[Session {session_id}] 🏗️ Created isolated state for Unit {unit_number} (Batch {batch_id})")
    print(f"[Session {session_id}]   - Unit session: {unit_session_id}")
    print(f"[Session {session_id}]   - Plant context: {plant_name} ({plant_country}, {plant_technology})")
    
    return isolated_state




# Define isolated unit processing flow (completely independent chain)
builder.add_edge("process_isolated_unit", "unit_generate_query_isolated")
builder.add_edge("unit_generate_query_isolated", "unit_web_research_isolated")
builder.add_edge("unit_web_research_isolated", "unit_finalize_isolated")

# After isolated unit processing, results go back to main collector
builder.add_edge("unit_finalize_isolated", "collect_smart_batch_results")

# Add batch continuation logic
def evaluate_batch_continuation(state: OverallState) -> str:
    """Determine if we need to continue with next batch or finish processing."""
    session_id = state.get("session_id", "unknown")
    continue_batch = state.get("continue_batch_processing", False)
    
    if continue_batch:
        print(f"[Session {session_id}] 🔄 Continuing to next batch...")
        return "process_all_units"  # Go back to spawn next batch
    else:
        print(f"[Session {session_id}] ✅ All batches complete, finalizing...")
        return "finalize_single_plant"

# Route batch continuation - either process next batch or finalize
builder.add_conditional_edges(
    "collect_smart_batch_results",
    evaluate_batch_continuation,
    ["process_all_units", "finalize_single_plant"]
)

# Each parallel unit follows this flow: process_single_unit → unit_generate_query → ... → unit_finalize_answer
builder.add_edge("process_single_unit", "unit_generate_query")

# Define unit-level web research routing
def continue_to_unit_web_research(state: QueryGenerationState):
    """LangGraph node that sends the search queries to the unit web research node."""
    unit_number = state.get("unit_number", "1")
    print(f"Sending {len(state['query_list'])} queries for Unit {unit_number}")
    
    return [
        Send("unit_web_research", {
            "search_query": search_query, 
            "id": int(idx),
            "search_phase": 3,
            "unit_number": state.get("unit_number", "1")
        })
        for idx, search_query in enumerate(state["query_list"])
    ]

# Generate web research queries for unit level
builder.add_conditional_edges(
    "unit_generate_query", 
    continue_to_unit_web_research, 
    ["unit_web_research"]
)

# Reflect on unit-level web research
builder.add_edge("unit_web_research", "unit_reflection")

# Define unit-level research evaluation
def evaluate_unit_research(state: ReflectionState) -> str:
    """LangGraph routing function that determines the next step in the unit research flow."""
    unit_number = state.get("unit_number", "1")
    research_loop_count = state.get("research_loop_count", 0)
    print(f"Evaluating Unit {unit_number} research, loop count: {research_loop_count}")
    
    # Safe access to is_sufficient with fallback
    is_sufficient = state.get("is_sufficient", False)
    if is_sufficient is None:
        is_sufficient = False
        print(f"Warning: is_sufficient was None, defaulting to False")

    # Simple termination logic to prevent infinite loops
    if is_sufficient or research_loop_count >= 2:  # Limit to 2 loops for units
        print(f"Unit {unit_number} research complete. is_sufficient: {is_sufficient}, loops: {research_loop_count}")
        return "unit_finalize_answer"
    else:
        print(f"Unit {unit_number} needs more research. Continuing...")
        return [
            Send(
                "unit_web_research",
                {
                    "search_query": follow_up_query,
                    "id": state.get("number_of_ran_queries", 0) + int(idx),
                    "search_phase": 3,
                    "unit_number": state.get("unit_number", "1")
                },
            )
            for idx, follow_up_query in enumerate(state["follow_up_queries"])
        ]

# Either continue unit research or finalize unit answer
builder.add_conditional_edges(
    "unit_reflection", 
    evaluate_unit_research, 
    ["unit_web_research", "unit_finalize_answer"]
)

def finalize_single_plant_processing(state: OverallState) -> OverallState:
    """Finalize single plant processing and save JSON files."""
    session_id = state.get("session_id", "unknown")
    plant_name = state.get("current_plant_name", "Unknown Plant")
    
    print(f"[Session {session_id}] 🏁 FINALIZING SINGLE PLANT PROCESSING: {plant_name}")
    
    # Get all unit results
    unit_results = state.get("unit_processing_results", [])
    messages = state.get("messages", [])
    
    # Extract plant data from messages
    plant_data = None
    for message in messages:
        if hasattr(message, 'content') and isinstance(message.content, str):
            try:
                import json
                content = message.content

                # Use proper bracket matching to avoid "Extra data" errors
                def extract_json_object(text):
                    start_idx = text.find('{')
                    if start_idx == -1:
                        return None

                    bracket_count = 0
                    for i, char in enumerate(text[start_idx:], start_idx):
                        if char == '{':
                            bracket_count += 1
                        elif char == '}':
                            bracket_count -= 1
                            if bracket_count == 0:
                                return text[start_idx:i+1]
                    return None

                json_str = extract_json_object(content)
                if json_str:
                    potential_data = json.loads(json_str)
                    if isinstance(potential_data, dict) and any(key in potential_data for key in ['plant_name', 'organization_name', 'location']):
                        plant_data = potential_data
                        break
            except:
                continue
    
    if plant_data and unit_results:
        # Save JSON file
        import os
        import json
        from datetime import datetime
        
        # Create output directory
        output_dir = "/Users/<USER>/Downloads/SE-TRANSITION-AGI-TECH-59e0b03f7500f5023133d0951c61b51e859cbb42/plant_outputs"
        os.makedirs(output_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create complete plant JSON
        complete_plant_json = {
            "plant_info": plant_data,
            "units": unit_results,
            "processing_metadata": {
                "plant_name": plant_name,
                "units_processed": len(unit_results),
                "processing_timestamp": timestamp,
                "session_id": session_id,
                "processing_mode": "Single Plant"
            }
        }
        
        # Clean plant name for filename
        safe_plant_name = "".join(c for c in plant_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_plant_name = safe_plant_name.replace(' ', '_')
        
        filename = f"{safe_plant_name}_{timestamp}.json"
        filepath = os.path.join(output_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(complete_plant_json, f, indent=2, ensure_ascii=False)
            print(f"[{session_id}] 💾 Saved single plant JSON to: {filepath}")
            
            # Add success message to state
            from langchain_core.messages import AIMessage
            save_message = AIMessage(content=f"✅ Plant data saved to: {filepath}")
            state["messages"] = state.get("messages", []) + [save_message]
            
        except Exception as e:
            print(f"[{session_id}] ❌ Failed to save single plant JSON: {e}")
    else:
        print(f"[{session_id}] ⚠️ No plant data or unit results to save")

    # ENHANCEMENT: Store Level-4 Transition Plan JSON with database lookup
    try:
        from agent.json_s3_storage import store_transition_plan_data
        research_topic = get_research_topic(state.get("messages", []))

        # PRIORITY: Use UIDs from state first (most reliable), then database as fallback
        plant_id = state.get("plant_uid", "")  # Get UUID for folder structure
        org_id = state.get("org_id", "")

        if plant_id and org_id:
            print(f"[{session_id}] ✅ Using UIDs from state: plant_id='{plant_id}', org_id='{org_id}'")
        else:
            print(f"[{session_id}] ⚠️ UIDs not in state, trying database lookup...")
            try:
                from agent.database_manager import get_database_manager
                db_manager = get_database_manager()

                # Get plant_id from database as fallback
                existing_plant = db_manager.check_plant_exists(research_topic)
                if existing_plant:
                    plant_id = existing_plant.get("plant_id", plant_id)  # Keep state value if exists
                    org_id = existing_plant.get("org_id", org_id)  # Keep state value if exists
                    print(f"[{session_id}] ✅ Retrieved from database: plant_id='{plant_id}', org_id='{org_id}'")
                else:
                    print(f"[{session_id}] ⚠️ Plant not found in database: {research_topic}")

            except Exception as e:
                print(f"[{session_id}] ❌ Database lookup failed: {e}")

        if not plant_id or not org_id:
            print(f"[{session_id}] ❌ Missing UIDs - plant_id: '{plant_id}', org_id: '{org_id}'")

        print(f"[{session_id}] 📋 Storing transition plan at organization level with org_id: {org_id}...")
        transition_s3_url = store_transition_plan_data(research_topic, session_id, org_id=org_id, plant_id=plant_id)  # Pass plant_id

        if transition_s3_url:
            print(f"[{session_id}] ✅ Level-4 transition plan saved to S3: {transition_s3_url}")
        else:
            print(f"[{session_id}] ❌ Failed to save Level-4 transition plan to S3")

    except Exception as e:
        print(f"[{session_id}] ❌ CRITICAL ERROR storing Level-4 transition plan: {e}")
        print(f"[{session_id}] 🔄 Attempting emergency transition plan creation...")

        # EMERGENCY FALLBACK: Create minimal transition plan
        try:
            emergency_transition_data = {
                "pk": state.get("plant_uid", state.get("org_id", "EMERGENCY_UID")),
                "sk": "transition_plan",
                "selected_plan_id": "",
                "transitionPlanStratName": "",
                "plant_uid": state.get("plant_uid", ""),
                "org_id": state.get("org_id", ""),
                "emergency_creation": True,
                "error_reason": str(e)
            }

            # Try to save emergency transition plan
            import json
            print(f"[{session_id}] 🆘 Emergency transition plan created: {json.dumps(emergency_transition_data, indent=2)}")

        except Exception as emergency_error:
            print(f"[{session_id}] ❌ Even emergency transition plan failed: {emergency_error}")

        import traceback
        traceback.print_exc()

    return state

# Add the finalization node
builder.add_node("finalize_single_plant", finalize_single_plant_processing)

# Final edge from old collector to finalization, then to END
builder.add_edge("collect_unit_results", "finalize_single_plant")
builder.add_edge("finalize_single_plant", END)  # FIXED: Go directly to END after unit extraction

# Image extraction completes independently (parallel processing)
builder.add_edge("extract_images_parallel", END)

# REMOVED: trigger_financial_pipeline no longer used in main flow

# Compile the graph
graph = builder.compile(name="power-plant-info-extraction-agent")
