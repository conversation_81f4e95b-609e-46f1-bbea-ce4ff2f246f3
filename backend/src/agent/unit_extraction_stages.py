"""
Multi-stage unit data extraction for better API call efficiency and data quality
"""

import os
import json
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage

def normalize_plant_name(name: str) -> str:
    suffixes = [' Power Plant', ' Generating Plant', ' Power Station', ' Generating Station', ' Energy Center', ' Center', ' Power Complex', ' Generating Complex', ' Power Facility', ' Generating Facility', ' Power Project', ' Generating Project', ' Power Site', ' Generating Site', ' Power Area', ' Generating Area', ' Electric Plant', ' Plant', ' Station', ' Generation Complex', ' Steam Station', ' Steam Electric Plant', ' Energy Complex', ' Electric Plant', ' Complex']
    for s in suffixes:
        if name.endswith(s):
            name = name[:-len(s)]
            break
    return name.strip()

def extract_json_object(text: str) -> str:
    """Extract the first complete JSON object from text with proper bracket matching"""
    start_idx = text.find('{')
    if start_idx == -1:
        return None

    bracket_count = 0
    for i, char in enumerate(text[start_idx:], start_idx):
        if char == '{':
            bracket_count += 1
        elif char == '}':
            bracket_count -= 1
            if bracket_count == 0:
                return text[start_idx:i+1]
    return None

def parse_json_response(content: str, stage_name: str) -> dict:
    """Enhanced JSON parsing with better error handling and logging"""
    print(f"🔍 {stage_name} raw response length: {len(content)} chars")
    print(f"🔍 Response preview: {content[:200]}...")

    # Method 1: Try to extract JSON with proper bracket matching
    json_str = extract_json_object(content)
    if json_str:
        try:
            parsed_data = json.loads(json_str)
            print(f"✅ {stage_name} JSON parsed successfully with {len(parsed_data)} fields")
            return parsed_data
        except json.JSONDecodeError as e:
            print(f"⚠️ Failed to parse extracted JSON for {stage_name}: {e}")

    # Method 2: Try parsing the whole response as JSON
    try:
        parsed_data = json.loads(content)
        print(f"✅ Full {stage_name} response parsed as JSON with {len(parsed_data)} fields")
        return parsed_data
    except json.JSONDecodeError as e:
        print(f"❌ Failed to parse full response as JSON for {stage_name}: {e}")

    # Method 3: Extract JSON using regex as fallback
    import re
    json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
    matches = re.findall(json_pattern, content, re.DOTALL)

    if matches:
        for match in matches:
            try:
                parsed_data = json.loads(match)
                print(f"✅ {stage_name} JSON extracted via regex with {len(parsed_data)} fields")
                return parsed_data
            except json.JSONDecodeError:
                continue

    print(f"❌ All JSON parsing methods failed for {stage_name}")
    return {}


def extract_basic_unit_info(summaries: str, unit_number: str, plant_context: dict, reasoning_model: str) -> dict:
    """Stage 1: Extract basic unit identification and capacity information"""
    
    prompt = f"""EXPERT UNIT DATA EXTRACTION for Unit {unit_number}

🎯 **CRITICAL: GEMWIKI PRIORITY EXTRACTION STRATEGY**
1. **GEMWIKI FIRST**: Search for "gem.wiki" or "globalenergymonitor.org" data - this is the GOLD STANDARD
   - Look for "Project Details" section with unit specifications
   - Find unit tables with capacity, technology, and operational status
   - Extract commissioning dates from project timeline
   - GEM Wiki data is the MOST ACCURATE source for capacity and technology fields

2. **UNIT-SPECIFIC SEARCH**: Look for "Unit {unit_number}" specific information
   - Search for capacity patterns: "Unit {unit_number}: XXX MW"
   - Look for technology specifications for this specific unit
   - Find operational dates and status

3. **STRUCTURED DATA EXTRACTION**: Look for tabular data
   - Unit tables with columns: Unit, Capacity, Technology, Status, Date
   - Project details sections with technical specifications
   - Operational data with performance metrics

4. **INTELLIGENT FALLBACK**: Use plant-level data with unit-specific calculations
   - If total plant capacity is known, estimate unit capacity
   - Use plant technology for unit technology
   - Apply reasonable assumptions based on plant context

🔧 CRITICAL FIELD REQUIREMENTS:
1. **unit_number**: "{unit_number}" (FIXED)
2. **plant_id**: Extract from context or use "1" (NOT "0")
3. **capacity**: Unit capacity in MW (CRITICAL: MUST extract from GEMWiki if available)
   - **PRIORITY 1**: Look for GEM Wiki unit tables with "Unit {unit_number}: XXX MW" format
   - **PRIORITY 2**: Check GEM Wiki project details section for unit specifications
   - **PRIORITY 3**: Look for patterns: "Unit {unit_number}: XXX MW", "XXX MW Unit {unit_number}"
   - **FALLBACK**: If not found, estimate from total plant capacity ÷ number of units
4. **capacity_unit**: "MW" (FIXED)
5. **technology**: BOILER/TURBINE TECHNOLOGY TYPE (CRITICAL: MUST extract from GEMWiki if available)
   - **PRIORITY 1**: Search GEM Wiki for EXACT technology specifications in unit tables
   - **PRIORITY 2**: Look for technology in GEM Wiki project details section
   - **PRIORITY 3**: Check GEM Wiki unit specifications for boiler/turbine type
   - **EXACT GEM WIKI TERMS**: Use exact terminology from GEM Wiki (e.g., "Subcritical", "Supercritical", "Ultra-supercritical")
   - Coal plants: "Sub-Critical", "Super-Critical", "Ultra-Super-Critical", "Subcritical", "Supercritical"
   - Gas plants: "Combined Cycle Gas Turbine (CCGT)", "Open Cycle Gas Turbine (OCGT)"
   - Steam plants: "Pulverized Coal", "Circulating Fluidized Bed"
   - IMPORTANT: This is NOT fuel type (Coal/Gas) but the actual technology used
6. **boiler_type**: Boiler specification (extract from technical details)
7. **commencement_date**: Operation start date in YYYY-MM-DD format
   - Look for "commissioned", "operational", "started commercial operation"
   - Extract year at minimum, add "-01-01" if only year available

🏭 PLANT CONTEXT:
- Plant Name: {plant_context.get('plant_name', 'Unknown')}
- Plant Technology: {plant_context.get('plant_technology', 'Unknown')}
- Plant Capacity: {plant_context.get('capacity', 'Unknown')} MW
- Country: {plant_context.get('country', 'Unknown')}

Research Data:
{summaries}

INSTRUCTIONS:
🎯 **CRITICAL PRIORITY: GEMWiki (gem.wiki) data for capacity and technology - this is the MOST RELIABLE source**
- **STEP 1**: Search for GEM Wiki content first (gem.wiki, globalenergymonitor.org)
- **STEP 2**: Look for unit tables, specifications, and technical details in GEMWiki content
- **STEP 3**: Extract exact capacity (MW) and technology type from GEM Wiki unit specifications
- **STEP 4**: If GEM Wiki data unavailable, use other sources as fallback
- If unit-specific capacity not found, divide total plant capacity by number of units
- Use "Not available" only if no relevant information exists in any source
- Provide your best estimate based on available data, prioritizing GEM Wiki accuracy

Respond ONLY with JSON format:
{{
  "unit_number": "{unit_number}",
  "plant_id": "extracted_or_0",
  "capacity": "estimated_capacity_value",
  "capacity_unit": "MW",
  "technology": "extracted_technology",
  "boiler_type": "extracted_or_Not_available",
  "commencement_date": "extracted_date_or_Not_available"
}}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        result = llm.invoke(prompt)
        content = result.content.strip()
        
        # Use enhanced JSON parsing
        parsed_data = parse_json_response(content, "Basic Info")
        if parsed_data and len(parsed_data) > 0:  # Check if dict has content
            return parsed_data
        else:
            print(f"⚠️ Basic info extraction returned empty data for Unit {unit_number}")

    except Exception as e:
        print(f"❌ Basic info extraction failed: {e}")

    # 🚨 CRITICAL FIX: Always return a dict, never None
    return {
        "unit_number": unit_number,
        "plant_id": "0",
        "capacity": "Not available",
        "capacity_unit": "MW",
        "technology": "Not available",
            "boiler_type": "Not available",
            "commencement_date": "Not available"
        }


def extract_performance_metrics(summaries: str, unit_number: str, plant_context: dict, reasoning_model: str) -> dict:
    """Stage 2: Extract performance and operational metrics with SPECIFIC FIELD TARGETS"""

    prompt = f"""COMPREHENSIVE PERFORMANCE DATA EXTRACTION for Unit {unit_number}

🎯 MULTI-SOURCE PERFORMANCE EXTRACTION STRATEGY:
1. **GEMWIKI PERFORMANCE DATA**: Look for operational statistics and performance metrics
2. **REGULATORY REPORTS**: Search for annual performance reports and filings
3. **INDUSTRY DATABASES**: Check for capacity factors and generation data
4. **TECHNICAL SPECIFICATIONS**: Extract design parameters and efficiency ratings

🔧 CRITICAL PERFORMANCE FIELDS (MUST EXTRACT):

**EFFICIENCY & HEAT RATE:**
- heat_rate: Heat rate in kCal/kWh (search for efficiency data, typical ranges: Coal: 2000-2500, Gas: 1800-2200)
- heat_rate_unit: "Kcal/kWh" (FIXED)
- coal_unit_efficiency: Unit efficiency percentage (search for "efficiency", "thermal efficiency", typical: 35-45%)

**OPERATIONAL PERFORMANCE (TIME SERIES):**
- plf: Plant Load Factor by year (search for "capacity factor", "load factor", "utilization")
  * Look for patterns: "PLF: XX%", "Capacity Factor: XX%", "Load Factor: XX%"
  * Extract multiple years if available: 2020, 2021, 2022, 2023
- PAF: Plant Availability Factor by year (search for "availability factor", "availability")
  * Look for "Available hours", "Availability: XX%", "Plant availability"
- auxiliary_power_consumed: Auxiliary power consumption (search for "auxiliary consumption", "station use")
  * Look for "Auxiliary power: XX%", "Station consumption: XX MWh"

**CRITICAL GENERATION DATA (REQUIRED FOR EXCEL CALCULATIONS):**
- unit_generation_mwh: Annual generation at unit level (PRIORITY 1)
  * Search patterns: "Unit {unit_number} generation", "Unit {unit_number} output", "Unit {unit_number} produced"
  * Look for: "Generated: XX MWh", "Output: XX MWh", "Production: XX GWh", "Electricity generation: XX"
  * Time series: "2020 generation", "2021 generation", "2022 generation", "2023 generation", "2024 generation"
  * Units: MWh, GWh (convert GWh to MWh), million units, billion units
- plant_generation_mwh: Annual generation at plant level (PRIORITY 1)
  * Search patterns: "Plant generation", "Total generation", "Annual generation", "Plant output"
  * Look for: "Plant generated XX GWh", "Total output XX MWh", "Annual production XX"
  * Time series: Extract year-wise data 2020-2024
- gross_power_generation: Gross generation by year (search for "generation", "output", "produced")
  * Look for "Generated: XX GWh", "Output: XX MWh", "Production: XX units"
  * Distinguish from net generation: "Gross generation", "Total generation before auxiliary"

**CRITICAL FUEL CONSUMPTION DATA (REQUIRED FOR EXCEL CALCULATIONS):**
- fuel_consumed_tons: Annual fuel consumption (PRIORITY 1)
  * Search patterns: "Coal consumption", "Fuel consumption", "Coal burned", "Fuel usage"
  * Look for: "Consumed XX tons", "Coal usage XX tonnes", "Fuel input XX tons", "Burns XX tons coal"
  * Time series: "2020 consumption", "2021 consumption", "2022 consumption", "2023 consumption", "2024 consumption"
  * Units: tons, tonnes, million tons, kt (kilotons), Mt (million tons)
- coal_consumption_rate: Coal consumption rate
  * Search patterns: "Coal consumption rate", "Fuel consumption per MWh", "Coal usage efficiency"
  * Look for: "XX tons per MWh", "Coal rate XX kg/MWh", "Fuel efficiency XX"

**CRITICAL EMISSIONS DATA (REQUIRED FOR EXCEL CALCULATIONS):**
- annual_emission_mt: Annual emissions in million tons CO2 (PRIORITY 1)
  * Search patterns: "CO2 emissions", "Carbon emissions", "Greenhouse gas emissions", "Annual emissions"
  * Look for: "Emitted XX Mt CO2", "Carbon footprint XX million tons", "CO2 output XX"
  * Time series: "2020 emissions", "2021 emissions", "2022 emissions", "2023 emissions", "2024 emissions"
  * Units: Mt CO2, million tons CO2, tonnes CO2 (convert to Mt), kg CO2 (convert to Mt)
- emission_factor_calculated: Calculated emission factor
  * Search patterns: "Emission factor", "Carbon intensity", "CO2 per MWh", "Emission rate"
  * Look for: "XX kg CO2/MWh", "Carbon intensity XX", "Emission factor XX"

**LIFETIME & DESIGN:**
- unit_lifetime: Design lifetime in years (search for "design life", "operational life", typical: 25-40 years)
- remaining_useful_life: Remaining years (calculate from commissioning date if available)

🔍 SEARCH PATTERNS TO LOOK FOR:
- "Unit {unit_number} performance", "Unit {unit_number} statistics"
- "Capacity factor", "Load factor", "PLF", "Availability factor"
- "Heat rate", "Efficiency", "Thermal efficiency"
- "Generation data", "Output statistics", "Performance metrics"
- "Auxiliary consumption", "Station use", "Parasitic load"

🏭 PLANT CONTEXT FOR REFERENCE:
- Plant: {plant_context.get('plant_name', 'Unknown')} Unit {unit_number}
- Technology: {plant_context.get('plant_technology', 'Unknown')}
- Capacity: {plant_context.get('capacity', 'Unknown')} MW
- Country: {plant_context.get('country', 'Unknown')}

Research Data:
{summaries}

Respond ONLY with JSON format. Use empty arrays [] for missing time-series data.
Example:
{{
  "heat_rate": "2150",
  "heat_rate_unit": "Kcal/kWh",
  "plf": [{{"value": "75.5", "year": "2023"}}, {{"value": "78.2", "year": "2022"}}],
  "PAF": [{{"value": "85.3", "year": "2023"}}],
  "auxiliary_power_consumed": [],
  "gross_power_generation": [{{"value": "3500", "year": "2023"}}],
  "coal_unit_efficiency": "42.5",
  "unit_lifetime": "25",
  "remaining_useful_life": "15",
  "unit_generation_mwh": [{{"value": "2500000", "year": "2023"}}, {{"value": "2400000", "year": "2022"}}],
  "plant_generation_mwh": [{{"value": "10000000", "year": "2023"}}, {{"value": "9600000", "year": "2022"}}],
  "fuel_consumed_tons": [{{"value": "1200000", "year": "2023"}}, {{"value": "1150000", "year": "2022"}}],
  "annual_emission_mt": [{{"value": "2.5", "year": "2023"}}, {{"value": "2.4", "year": "2022"}}],
  "emission_factor_calculated": [{{"value": "0.92", "year": "2023"}}, {{"value": "0.94", "year": "2022"}}]
}}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        result = llm.invoke(prompt)
        content = result.content.strip()
        
        # Use enhanced JSON parsing
        parsed_data = parse_json_response(content, "Performance Metrics")
        if parsed_data and len(parsed_data) > 0:  # Check if dict has content
            return parsed_data
        else:
            print(f"⚠️ Performance metrics extraction returned empty data for Unit {unit_number}")

    except Exception as e:
        print(f"❌ Performance metrics extraction failed: {e}")

    # 🚨 CRITICAL FIX: Always return a dict, never None
    return {
            "heat_rate": "Not available",
            "heat_rate_unit": "Kcal/kWh",  # Default unit
            "plf": [],  # No fallback - will use new formulas
            "PAF": [],  # No fallback - will use new formulas
            "auxiliary_power_consumed": [],  # No fallback - will use new formulas
            "gross_power_generation": [],  # No fallback - will use new formulas
            "coal_unit_efficiency": "Not available",
            "unit_lifetime": "Not available",
            # remaining_useful_life removed - Excel tool handles this
            "unit_generation_mwh": [],  # Critical for Excel calculations
            "plant_generation_mwh": [],  # Critical for Excel calculations
            "fuel_consumed_tons": [],  # Critical for Excel calculations
            "annual_emission_mt": [],  # Critical for Excel calculations
            "emission_factor_calculated": []  # Critical for Excel calculations
        }


def extract_fuel_and_emissions(summaries: str, unit_number: str, plant_context: dict, reasoning_model: str) -> dict:
    """Stage 3: Extract fuel composition and emission data with SPECIFIC FIELD TARGETS"""

    prompt = f"""Extract ONLY the fuel composition and emission information for Unit {unit_number}:

CRITICAL SPECIFIC FIELDS TO EXTRACT:
1. annual_operational_hours: Annual operational hours (FIXED VALUE: 8760)
2. blending_percentage_of_biomass: Biomass co-firing blending percentage (FIXED VALUE: 0.15)
3. emission_factor_coal: Emission factor for coal in kg CO₂e/kg of fuel
4. emission_factor_gas: Emission factor for natural gas (FIXED VALUE: 2.69)
5. emission_factor_of_gas_unit: Unit for gas emission factor (kg CO₂e/kg)
6. emission_factor_unit: Unit for emission factor (kgCO₂/kWh)
7. fgds_status: Status of Flue Gas Desulfurization System

COAL TYPE AND EMISSION FACTOR RESEARCH:
- What is the primary type of coal (bituminous, sub-bituminous, lignite, anthracite) used at Unit {unit_number}?
- What is the typical emission factor for that coal type in kg CO₂e/kg of coal burned?
- Source of emission factor data (IPCC, IEA, national inventory guidelines)?

FGDS SYSTEM RESEARCH:
- Does Unit {unit_number} have FGDS installed and operational?
- Type of FGDS (wet limestone, dry FGD, seawater FGD)?
- Year of FGDS commissioning?

Plant Context: {plant_context.get('plant_name', 'Unknown')} Unit {unit_number}
Country: {plant_context.get('country', 'Unknown')}

Research Data:
{summaries}

Respond ONLY with JSON format:
{{
  "annual_operational_hours": 8760,
  "blending_percentage_of_biomass": 0.15,
  "emission_factor_coal": "emission_factor_value_with_source",
  "emission_factor_gas": 2.69,
  "emission_factor_of_gas_unit": "kg CO2e/kg",
  "emission_factor_unit": "kgCO_2/kWH",
  "fgds_status": "Fully_installed_operational_or_status",
  "fuel_type": [
    {{"fuel": "Coal", "type": "Bituminous", "years_percentage": {{"2023": "80", "2022": "85"}}}},
    {{"fuel": "Biomass", "type": "Wood chips", "years_percentage": {{"2023": "20", "2022": "15"}}}}
  ],
  "emission_factor": [{{"value": "0.92", "year": "2023"}}],
  "gcv_coal": "5500",
  "gcv_coal_unit": "kCal/kg",
  "gcv_biomass": "4200",
  "gcv_biomass_unit": "kCal/kg",
  "efficiency_loss_biomass_cofiring": "2.5"
}}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        result = llm.invoke(prompt)
        content = result.content.strip()
        
        # Try to extract JSON from the response
        start_idx = content.find('{')
        end_idx = content.rfind('}') + 1
        
        if start_idx != -1 and end_idx != -1:
            json_str = content[start_idx:end_idx]
            return json.loads(json_str)
        else:
            return json.loads(content)  # Try parsing the whole thing
            
    except Exception as e:
        print(f"❌ Fuel and emissions extraction failed: {e}")
        return {
            "annual_operational_hours": 8760,  # FIXED VALUE
            "blending_percentage_of_biomass": 0.15,  # FIXED VALUE
            "emission_factor_coal": "Not available",
            "emission_factor_gas": 2.69,  # FIXED VALUE
            "emission_factor_of_gas_unit": "kgCO2e/kg of fuel",  # Fixed value
            "emission_factor_unit": "kgCO_2/kWH",  # Default value
            "fgds_status": "Not available",
            "fuel_type": [],
            "emission_factor": [],
            "gcv_coal": "Not available",
            "gcv_coal_unit": "kCal/kg",
            "gcv_biomass": "Not available",
            "gcv_biomass_unit": "kCal/kg",
            "efficiency_loss_biomass_cofiring": "Not available"
        }


def get_country_currency(country: str) -> str:
    """Get the currency code for a country"""
    currency_map = {
        'United States': 'USD',
        'USA': 'USD',
        'US': 'USD',
        'India': 'INR',
        'China': 'CNY',
        'Japan': 'JPY',
        'Germany': 'EUR',
        'United Kingdom': 'GBP',
        'UK': 'GBP',
        'Canada': 'CAD',
        'Australia': 'AUD',
        'Brazil': 'BRL',
        'South Africa': 'ZAR',
        'Indonesia': 'IDR',
        'Thailand': 'THB',
        'Malaysia': 'MYR',
        'Philippines': 'PHP',
        'Vietnam': 'VND',
        'South Korea': 'KRW',
        'Taiwan': 'TWD',
        'Poland': 'PLN',
        'Turkey': 'TRY',
        'Mexico': 'MXN',
        'Chile': 'CLP',
        'Colombia': 'COP',
        'Argentina': 'ARS',
        # CRITICAL FIX: Add missing European countries
        'Italy': 'EUR',
        'France': 'EUR',
        'Spain': 'EUR',
        'Netherlands': 'EUR',
        'Belgium': 'EUR',
        'Austria': 'EUR',
        'Portugal': 'EUR',
        'Greece': 'EUR',
        'Ireland': 'EUR',
        'Finland': 'EUR',
        'Luxembourg': 'EUR',
        'Slovenia': 'EUR',
        'Slovakia': 'EUR',
        'Estonia': 'EUR',
        'Latvia': 'EUR',
        'Lithuania': 'EUR',
        'Malta': 'EUR',
        'Cyprus': 'EUR',
        # Other major countries
        'Switzerland': 'CHF',
        'Norway': 'NOK',
        'Sweden': 'SEK',
        'Denmark': 'DKK',
        'Czech Republic': 'CZK',
        'Hungary': 'HUF',
        'Romania': 'RON',
        'Bulgaria': 'BGN',
        'Croatia': 'HRK',
        'Russia': 'RUB',
        'Ukraine': 'UAH',
        'Israel': 'ILS',
        'Saudi Arabia': 'SAR',
        'UAE': 'AED',
        'Egypt': 'EGP',
        'Nigeria': 'NGN',
        'Kenya': 'KES',
        'Morocco': 'MAD',
        'Algeria': 'DZD',
        'Tunisia': 'TND',
        # CRITICAL FIX: Add missing African countries
        'Zambia': 'ZMW',
        'Zimbabwe': 'ZWL',
        'Botswana': 'BWP',
        'Namibia': 'NAD',
        'Ghana': 'GHS',
        'Tanzania': 'TZS',
        'Uganda': 'UGX',
        'Rwanda': 'RWF',
        'Ethiopia': 'ETB',
        'Mozambique': 'MZN',
        'Angola': 'AOA',
        'Cameroon': 'XAF',
        'Ivory Coast': 'XOF',
        'Senegal': 'XOF',
        'Mali': 'XOF',
        'Burkina Faso': 'XOF',
        'Niger': 'XOF',
        'Chad': 'XAF',
        'Central African Republic': 'XAF',
        'Democratic Republic of Congo': 'CDF',
        'Republic of Congo': 'XAF'
    }

    # Try exact match first
    if country in currency_map:
        return currency_map[country]

    # Try partial match
    country_lower = country.lower()
    for country_key, currency in currency_map.items():
        if country_key.lower() in country_lower or country_lower in country_key.lower():
            return currency

    # Default to USD if country not found
    return 'USD'

def extract_economic_data(summaries: str, unit_number: str, plant_context: dict, reasoning_model: str) -> dict:
    """Stage 4: Extract economic and conversion cost data with SPECIFIC FIELD TARGETS"""

    country = plant_context.get('country', 'Unknown')
    currency = get_country_currency(country)
    prompt = f"""Extract ONLY the economic and conversion cost information for Unit {unit_number}:

CRITICAL SPECIFIC FIELDS TO EXTRACT:
1. capex_required_retrofit_biomass: CAPEX for biomass cofiring retrofit using Palm Kernel Shells (PKS)
2. capex_required_retrofit_biomass_unit: Unit for retrofit CAPEX (FIXED VALUE: {currency}/MW)
3. capex_required_renovation_open_cycle: CAPEX for Open Cycle Gas Turbine (OCGT) conversion
4. capex_required_renovation_open_cycle_unit: Unit for OCGT CAPEX (FIXED VALUE: {currency}/MW)
5. capex_required_renovation_closed_cycle: CAPEX for Combined Cycle Gas Turbine (CCGT) conversion
6. capex_required_renovation_closed_cycle_unit: Unit for CCGT CAPEX (FIXED VALUE: {currency}/MW)

RETROFIT COST RESEARCH QUESTIONS:
- What is the average cost per megawatt (MW) of retrofitting Unit {unit_number} into a biomass co-firing plant in {country}, using Palm Kernel Shells (PKS)?
- How much would it cost to retrofit Unit {unit_number} into an Open Cycle Gas Turbine (OCGT) power plant in {country}?
- How much would it cost to retrofit Unit {unit_number} into a Combined Cycle Gas Turbine (CCGT) power plant in {country}?

IMPORTANT INSTRUCTIONS:
- Express costs as full numbers in local currency (e.g., 145000000 IDR not 145 million IDR)
- Include year of estimate if available
- Consider plant size, age, and site conditions in your estimates
- Use country-specific data when available

Plant Context: {plant_context.get('plant_name', 'Unknown')} Unit {unit_number}
Country: {country}

Research Data:
{summaries}

Respond ONLY with JSON format.
Example:
{{
  "capex_required_retrofit_biomass": "50000000",
  "capex_required_retrofit_biomass_unit": "{currency}/MW",
  "capex_required_renovation_open_cycle": "200000000",
  "capex_required_renovation_open_cycle_unit": "{currency}/MW",
  "capex_required_renovation_closed_cycle": "350000000",
  "capex_required_renovation_closed_cycle_unit": "{currency}/MW"
}}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        result = llm.invoke(prompt)
        content = result.content.strip()
        
        # Try to extract JSON from the response using proper bracket matching
        json_str = extract_json_object(content)
        if json_str:
            return json.loads(json_str)
        else:
            return json.loads(content)  # Try parsing the whole thing
            
    except Exception as e:
        print(f"❌ Economic data extraction failed: {e}")
        return {
            "capex_required_retrofit_biomass": "Not available",
            "capex_required_retrofit_biomass_unit": f"{currency}/MW",  # FIXED VALUE with actual currency
            "capex_required_renovation_open_cycle": "Not available",
            "capex_required_renovation_open_cycle_unit": f"{currency}/MW",  # FIXED VALUE with actual currency
            "capex_required_renovation_closed_cycle": "Not available",
            "capex_required_renovation_closed_cycle_unit": f"{currency}/MW"  # FIXED VALUE with actual currency
        }


def extract_technical_parameters(summaries: str, unit_number: str, plant_context: dict, reasoning_model: str) -> dict:
    """Stage 5: Extract country-specific technical parameters"""
    
    prompt = f"""Extract ONLY the country-specific technical parameters for Unit {unit_number}:

EXTRACT ONLY THESE FIELDS:
1. gcv_natural_gas: Gross calorific value of natural gas
2. gcv_natural_gas_unit: Unit for gas GCV  
3. open_cycle_gas_turbine_efficency: OCGT efficiency for the country
4. closed_cylce_gas_turbine_efficency: CCGT efficiency for the country
5. combined_cycle_heat_rate: CCGT heat rate for the country
6. open_cycle_heat_rate: OCGT heat rate for the country

Plant Context: {plant_context.get('plant_name', 'Unknown')} Unit {unit_number}
Country: {plant_context.get('country', 'Unknown')}

Research Data:
{summaries}

Respond ONLY with JSON format.
Example:
{{
  "gcv_natural_gas": "10000",
  "gcv_natural_gas_unit": "kcal/scm",
  "open_cycle_gas_turbine_efficency": "35",
  "closed_cylce_gas_turbine_efficency": "55",
  "combined_cycle_heat_rate": "1800",
  "open_cycle_heat_rate": "2800"
}}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        result = llm.invoke(prompt)
        content = result.content.strip()
        
        # Try to extract JSON from the response
        start_idx = content.find('{')
        end_idx = content.rfind('}') + 1
        
        if start_idx != -1 and end_idx != -1:
            json_str = content[start_idx:end_idx]
            return json.loads(json_str)
        else:
            return json.loads(content)  # Try parsing the whole thing
            
    except Exception as e:
        print(f"❌ Technical parameters extraction failed: {e}")
        return {
            "gcv_natural_gas": "10000",
            "gcv_natural_gas_unit": "kcal/scm",
            "open_cycle_gas_turbine_efficency": "Not available",
            "closed_cylce_gas_turbine_efficency": "Not available",
            "combined_cycle_heat_rate": "Not available",
            "open_cycle_heat_rate": "Not available"
        }


def combine_unit_data(stage_results: list, unit_number: str, plant_context: dict) -> dict:
    """Combine all stage results into final unit data structure"""

    # 🚨 CRITICAL FIX: Handle None stage_results
    if stage_results is None:
        print(f"🚨 CRITICAL ERROR: stage_results is None for Unit {unit_number}!")
        stage_results = []

    # 🚨 CRITICAL FIX: Filter out None results from stages
    valid_stage_results = []
    for i, stage_result in enumerate(stage_results):
        if stage_result is None:
            print(f"🚨 WARNING: Stage {i+1} returned None for Unit {unit_number}, using empty dict")
            valid_stage_results.append({})
        elif isinstance(stage_result, dict):
            valid_stage_results.append(stage_result)
        else:
            print(f"🚨 WARNING: Stage {i+1} returned {type(stage_result)} instead of dict for Unit {unit_number}, using empty dict")
            valid_stage_results.append({})

    # Update stage_results to use the validated list
    stage_results = valid_stage_results
    print(f"🔍 Validated stage_results: {len(stage_results)} stages for Unit {unit_number}")

    # Check if Excel data is available
    excel_data = plant_context.get('excel_data')
    if excel_data:
        print(f"🔧 EXCEL MODE: Using Excel data for Unit {unit_number}")
        print(f"🔍 Excel data keys: {list(excel_data.keys())}")

    # Start with required base structure
    # SIMPLIFIED FIX: Use plant_type directly from plant_context (already has correct value from plant data)
    plant_type = plant_context.get('plant_type', plant_context.get('plant_technology', 'coal'))

    print(f"🔧 Using plant_type from plant_context: {plant_type}")

    sequential_plant_id = plant_context.get('plant_id', '1')  # Sequential number (1, 2, 3, etc.)
    plant_uid = plant_context.get('plant_uid', plant_context.get('org_id', 'default null'))  # UUID

    print(f"🔧 UNIT DATA GENERATION: plant_type='{plant_type}' (from plant_context), unit_id='{unit_number}', plant_id='{sequential_plant_id}', plant_uid='{plant_uid}'")
    print(f"🔍 DEBUG PLANT CONTEXT: {plant_context}")

    combined_data = {
        "sk": f"unit#{plant_type}#{unit_number}#plant#{sequential_plant_id}",
        "unit_number": unit_number,
        "plant_id": sequential_plant_id,  # Sequential number (1, 2, 3, etc.)
        "plant_uid": plant_uid,  # UUID from database
        # Default values for fields that might be missing

        "pk": plant_uid,  # Use plant_uid (UUID) as primary key

        # FIXED VALUES as specified
        "annual_operational_hours": 8760,
        "blending_percentage_of_biomass": 0.15,
        "emission_factor_gas": 2.69,

        # Currency-specific units
        "capex_required_renovation_unit": f"{get_country_currency(plant_context.get('country', 'Unknown'))}/MW",
        "capex_required_retrofit_biomass_unit": f"{get_country_currency(plant_context.get('country', 'Unknown'))}/MW",
        "capex_required_renovation_open_cycle_unit": f"{get_country_currency(plant_context.get('country', 'Unknown'))}/MW",
        "capex_required_renovation_closed_cycle_unit": f"{get_country_currency(plant_context.get('country', 'Unknown'))}/MW",

        # Time series fields - will be populated from Excel tool for USA plants
        "plf": [],
        "PAF": [],
        "auxiliary_power_consumed": [],
        "gross_power_generation": [],
        "emission_factor": [
            {"value": 0.95, "year": "2023"},
            {"value": 0.97, "year": "2022"},
            {"value": 0.93, "year": "2021"}
        ],
        "fuel_type": [
            {"value": "Coal", "year": "2023"},
            {"value": "Coal", "year": "2022"},
            {"value": "Coal", "year": "2021"}
        ],

        # CRITICAL MANDATORY FIELDS (NEVER NULL - with intelligent defaults)
        "capacity": 300.0,  # Default capacity in MW (will be overridden by extraction)
        "capacity_unit": "MW",
        "technology": "Sub-Critical",  # DEFAULT BOILER TECHNOLOGY (NOT fuel type)
        "boiler_type": "Pulverized Coal",  # Default boiler type (will be overridden by extraction)
        "commencement_date": "2000-01-01T00:00:00.000Z",  # Default date (will be overridden by extraction)

        # Other mandatory fields with intelligent defaults
        "heat_rate": 2200.0,  # Default heat rate for coal plants (will be overridden)
        "heat_rate_unit": "Kcal/kWh",
        "coal_unit_efficiency": 38.0,  # Default efficiency percentage (will be overridden)
        "unit_lifetime": 30,  # Default lifetime in years (will be overridden)
        "remaining_useful_life": 15,  # Default remaining life (will be overridden)

        # Other default fields
        "capex_required_renovation": "default null",
        "emission_factor_coal": "default null",
        "emission_factor_of_gas_unit": "default null",
        "emission_factor_unit": "default null",
        "fgds_status": "default null",
        "ramp_down_rate": "default null",
        "ramp_up_rate": "default null"
    }
    
    # Use Excel data if available, otherwise merge stage results
    if excel_data:
        print(f"📊 EXCEL MODE: Using Excel data instead of stage results for Unit {unit_number}")
        # Override with Excel data for specific fields
        excel_fields_to_use = [
            'capacity', 'commencement_date', 'remaining_useful_life', 'unit_lifetime',
            'fuel_type', 'emission_factor', 'emission_factor_coal', 'plant_name', 'unit_id'
        ]
        for field in excel_fields_to_use:
            if field in excel_data and excel_data[field] is not None:
                combined_data[field] = excel_data[field]
                print(f"📊 Used Excel data for {field}: {excel_data[field]}")
    else:
        # Merge all stage results with debugging
        print(f"🔍 Combining {len(stage_results)} stage results for Unit {unit_number}")
        for i, stage_result in enumerate(stage_results):
            if isinstance(stage_result, dict):
                print(f"🔍 Stage {i+1} contributed {len(stage_result)} fields: {list(stage_result.keys())}")
                combined_data.update(stage_result)
            else:
                print(f"⚠️ Stage {i+1} result is not a dict: {type(stage_result)}")

    # APPLY SOPHISTICATED FALLBACK CALCULATIONS using the engineering engine
    try:
        from agent.fallback_calculations import FallbackCalculationEngine
        from agent.excel_calculation_engine import ExcelCalculationEngine, create_excel_calculation_engine
        fallback_engine = FallbackCalculationEngine()

        print(f"🔧 Applying sophisticated fallback calculations using engineering engine...")

        # Note: PLF, auxiliary_power, gross_power_generation fallback calculations removed
        # These will be calculated using new formulas later
        # Only emission_factor and fuel_type still have fallback logic
        time_series_fields = ["emission_factor", "fuel_type"]

        for field in time_series_fields:
            if not combined_data.get(field) or combined_data.get(field) == []:
                print(f"🔧 Time series field '{field}' is empty, keeping fallback data")
                # Fallback data is already set in defaults above
            else:
                print(f"✅ Time series field '{field}' has extracted data: {len(combined_data.get(field, []))} entries")

        # CRITICAL: Ensure basic unit fields are NEVER null/empty (keep existing logic)
        critical_fields = {
            "capacity": 300.0,
            "technology": "Sub-Critical",  # DEFAULT BOILER TECHNOLOGY (NOT fuel type)
            "boiler_type": "Pulverized Coal",
            "commencement_date": "2000-01-01T00:00:00.000Z"  # ISO 8601 format with milliseconds
        }

        for field, default_value in critical_fields.items():
            current_value = combined_data.get(field)
            if current_value in [None, "", "default null", "Not available", "null"]:
                combined_data[field] = default_value
                print(f"🔧 Critical field '{field}' was null/empty, set to default: {default_value}")
            else:
                print(f"✅ Critical field '{field}' has valid data: {current_value}")

        # FIRST: Apply Excel Calculation Engine for time series fields (CLEAN IMPLEMENTATION)
        print(f"🔧 Applying Excel Calculation Engine for Unit {unit_number}...")

        try:
            # Create calculation engine based on country
            country = plant_context.get('country', '')

            if country and ("united states" in country.lower() or "usa" in country.lower() or "us" in country.lower()):
                print(f"🇺🇸 Creating USA Excel calculation engine for real data calculations")
                import sys
                sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')
                from usa_excel_calculation_engine import create_usa_excel_calculation_engine
                from excel_power_plant_tool import ExcelPowerPlantTool

                calc_engine = create_usa_excel_calculation_engine()
                is_usa_plant = True

                # 🚨 CRITICAL FIX: Call Excel tool FIRST to get correct gcv_coal and remaining_useful_life
                plant_name = plant_context.get('plant_name', '')
                plant_name_for_excel = normalize_plant_name(plant_name)

                print(f"🔧 Getting Excel data for {plant_name_for_excel} Unit {unit_number}")
                excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/USA Details.xlsx', f'unit-{unit_number}')
                excel_unit_results = excel_tool.get_plant_data(plant_name_for_excel, unit_number)

                if excel_unit_results:
                    excel_unit_data = excel_unit_results[0]
                    print(f"✅ Found Excel data for Unit {unit_number}")

                    # Override critical fields with Excel data
                    critical_excel_fields = ['gcv_coal', 'remaining_useful_life', 'fuel_type', 'capacity', 'commencement_date']
                    for field in critical_excel_fields:
                        if field in excel_unit_data and excel_unit_data[field] is not None:
                            combined_data[field] = excel_unit_data[field]
                            print(f"🔧 Set {field} from Excel: {excel_unit_data[field]}")
                else:
                    print(f"⚠️ No Excel data found for {plant_name_for_excel} Unit {unit_number}")

                # 🚨 CRITICAL FIX: For USA plants, get unit list from USA Details sheet
                plant_name = plant_context.get('plant_name', '')
                plant_name_for_excel = normalize_plant_name(plant_name)

                print(f"🔍 Getting unit list from USA Details sheet for: {plant_name_for_excel}")
                usa_units = calc_engine.get_plant_units_from_usa_details(plant_name_for_excel)

                if usa_units:
                    # Override the unit list with USA Details data
                    usa_unit_ids = [unit['unit_id'] for unit in usa_units]
                    print(f"✅ USA Details shows {len(usa_units)} units: {usa_unit_ids}")

                    # Update plant_context with correct unit information
                    plant_context['usa_units'] = usa_units
                    # 🚨 CRITICAL FIX: Keep unit IDs as strings to handle alphanumeric IDs like 'GEN1', 'ST2'
                    plant_context['units_id'] = usa_unit_ids  # Keep as strings

                    print(f"🔧 Updated plant_context units_id: {plant_context['units_id']}")
                else:
                    print(f"⚠️ No units found in USA Details for {plant_name_for_excel}")
            else:
                # Create standard Excel calculation engine for non-USA plants
                calc_engine = create_excel_calculation_engine('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH/current state calculations.xlsx')
                is_usa_plant = False

            # Prepare unit data for calculations
            unit_calc_data = {
                'capacity': combined_data.get('capacity', 0),
                'technology': combined_data.get('technology', 'subcritical'),
                'coal_type': 'bituminous',  # Default, can be enhanced from web search
                'generation_mwh': None,  # Will be extracted from web search if available
                'fuel_consumed_tons': None,  # Will be extracted from web search if available
                'annual_emission_mt': None,  # Will be extracted from web search if available
                'efficiency': None,  # Will be extracted from web search if available
                'plf': None  # Will be extracted from web search if available
            }

            # Extract data from stage results if available
            for stage_result in stage_results:
                if isinstance(stage_result, dict):
                    # Look for relevant fields in stage results
                    if 'plf' in stage_result:
                        unit_calc_data['plf'] = stage_result['plf']
                    if 'efficiency' in stage_result:
                        unit_calc_data['efficiency'] = stage_result['efficiency']

                    # CRITICAL: Extract new generation data fields
                    if 'unit_generation_mwh' in stage_result:
                        unit_calc_data['generation_mwh'] = stage_result['unit_generation_mwh']
                        print(f"✅ Found unit_generation_mwh: {stage_result['unit_generation_mwh']}")
                    if 'plant_generation_mwh' in stage_result:
                        unit_calc_data['plant_generation_mwh'] = stage_result['plant_generation_mwh']
                        print(f"✅ Found plant_generation_mwh: {stage_result['plant_generation_mwh']}")

                    # CRITICAL: Extract fuel consumption data
                    if 'fuel_consumed_tons' in stage_result:
                        unit_calc_data['fuel_consumed_tons'] = stage_result['fuel_consumed_tons']
                        print(f"✅ Found fuel_consumed_tons: {stage_result['fuel_consumed_tons']}")

                    # CRITICAL: Extract emissions data
                    if 'annual_emission_mt' in stage_result:
                        unit_calc_data['annual_emission_mt'] = stage_result['annual_emission_mt']
                        print(f"✅ Found annual_emission_mt: {stage_result['annual_emission_mt']}")

                    # Legacy field support
                    if 'generation_mwh' in stage_result:
                        unit_calc_data['generation_mwh'] = stage_result['generation_mwh']

            # Run Excel-based calculations
            if is_usa_plant:
                # For USA plants, use the USA-specific calculation method with real data
                plant_name = plant_context.get('plant_name', '')

                plant_name_for_excel = normalize_plant_name(plant_name)

                print(f"🇺🇸 Running USA calculations for: {plant_name_for_excel}")
                calc_results = calc_engine.calculate_unit_parameters_usa(plant_name_for_excel, unit_calc_data)

                if calc_results:
                    print(f"✅ USA Excel calculations successful: {list(calc_results.keys())}")
                    print(f"🎯 Using real data from USA Details.xlsx Coal yearly sheets")
                else:
                    print("⚠️ USA Excel calculations returned no results")
            else:
                # For non-USA plants, use the standard calculation method
                calc_results = calc_engine.calculate_unit_parameters(unit_calc_data, plant_context)

            # Check if calculations were successful
            if not calc_results:
                print(f"❌ Excel calculations failed for Unit {unit_number}")
                calc_results = {}

            # Apply calculated values to combined_data (Excel calculations take priority over fallback values)
            if 'plf_unit' in calc_results:
                # Convert time series PLF to required format
                if 'time_series_plf' in calc_results:
                    plf_time_series = []
                    for year, value in calc_results['time_series_plf'].items():
                        plf_time_series.append({"value": round(value, 3), "year": str(year)})
                    combined_data['plf'] = plf_time_series
                    print(f"✅ Applied Excel calculated PLF time series: {len(plf_time_series)} years")
                    plf_preview = [f"{item['value']:.1%}" for item in plf_time_series[:3]]
                    print(f"🎯 PLF values: {plf_preview}")  # Show first 3 values

            if 'efficiency' in calc_results:
                combined_data['coal_unit_efficiency'] = round(calc_results['efficiency'] * 100, 1)  # Convert to percentage
                print(f"✅ Applied calculated efficiency: {combined_data['coal_unit_efficiency']}%")
            elif excel_data and 'coal_unit_efficiency' in excel_data:
                # Fallback to Excel data if not in calc_results
                # Excel data stores efficiency as decimal, convert to percentage for display
                excel_efficiency = excel_data['coal_unit_efficiency']
                if excel_efficiency < 1:  # If it's already a decimal
                    combined_data['coal_unit_efficiency'] = round(excel_efficiency * 100, 1)
                else:  # If it's already a percentage
                    combined_data['coal_unit_efficiency'] = round(excel_efficiency, 1)
                print(f"✅ Applied Excel efficiency: {combined_data['coal_unit_efficiency']}%")

            if 'heat_rate_kcal_per_kwh' in calc_results:
                combined_data['heat_rate'] = round(calc_results['heat_rate_kcal_per_kwh'], 1)
                print(f"✅ Applied calculated heat rate: {combined_data['heat_rate']} kCal/kWh")
            elif excel_data and 'heat_rate_kcal_per_kwh' in excel_data:
                # Fallback to Excel data if not in calc_results
                combined_data['heat_rate'] = round(excel_data['heat_rate_kcal_per_kwh'], 1)
                print(f"✅ Applied Excel heat rate: {combined_data['heat_rate']} kCal/kWh")

            if 'emission_factor_kg_per_kwh' in calc_results:
                # Convert time series emission factor to required format
                if 'time_series_emission_factor' in calc_results:
                    emission_time_series = []
                    for year, value in calc_results['time_series_emission_factor'].items():
                        emission_time_series.append({"value": round(value, 3), "year": str(year)})
                    combined_data['emission_factor'] = emission_time_series
                    print(f"✅ Applied calculated emission factor time series: {len(emission_time_series)} years")

            if 'auxiliary_power_percent' in calc_results:
                # Convert time series auxiliary power to required format
                if 'time_series_auxiliary_power' in calc_results:
                    aux_time_series = []
                    for year, value in calc_results['time_series_auxiliary_power'].items():
                        aux_time_series.append({"value": round(value, 3), "year": str(year)})  # Keep as decimal (e.g., 0.08 for 8%)
                    combined_data['auxiliary_power_consumed'] = aux_time_series
                    print(f"✅ Applied calculated auxiliary power time series: {len(aux_time_series)} years")

            # Calculate gross_power_generation from PLF and capacity
            if 'time_series_plf' in calc_results and combined_data.get('capacity'):
                capacity_mw = float(combined_data['capacity'])
                gross_power_time_series = []
                for year, plf_value in calc_results['time_series_plf'].items():
                    # Gross Power Generation (GWh) = Capacity (MW) × PLF × 8760 hours / 1000
                    gross_power_gwh = capacity_mw * plf_value * 8760 / 1000
                    gross_power_time_series.append({"value": round(gross_power_gwh, 2), "year": str(year)})
                combined_data['gross_power_generation'] = gross_power_time_series
                print(f"✅ Calculated gross power generation time series: {len(gross_power_time_series)} years")

            print(f"✅ PowerPlant Calculation Engine completed for Unit {unit_number}")
            print(f"🔍 Methods used: {calc_results.get('calculation_summary', {}).get('methods_used', [])}")

        except Exception as calc_error:
            print(f"⚠️ Error applying PowerPlant Calculation Engine: {calc_error}")

        # SECOND: Use original fallback engine for remaining fields
        enhanced_data = fallback_engine.enhance_unit_data(
            extracted_data=combined_data,
            unit_context=plant_context,
            session_id=f"unit_{unit_number}"
        )

        # Update combined_data with enhanced results
        combined_data.update(enhanced_data)
        print(f"✅ Applied sophisticated fallback calculations using engineering formulas")

    except Exception as e:
        print(f"⚠️ Error applying fallback calculations: {e}")

    # REQUIREMENT 1 & 2: Apply date and efficiency formatting
    try:
        print(f"🔧 Applying date and efficiency formatting for Unit {unit_number}...")

        # Format commencement date
        if "commencement_date" in combined_data:
            original_date = combined_data["commencement_date"]
            formatted_date = format_commencement_date(original_date)
            combined_data["commencement_date"] = formatted_date
            print(f"📅 Formatted commencement_date: '{original_date}' → '{formatted_date}'")

        # Format efficiency fields
        efficiency_fields = [
            "closed_cycle_efficiency",
            "closed_cycle_turbine_efficiency",
            "open_cycle_turbine_efficiency",
            "efficiency_loss_biomass_cofiring"
        ]

        for field in efficiency_fields:
            if field in combined_data:
                original_value = combined_data[field]
                formatted_value = format_efficiency_value(original_value)
                if formatted_value:  # Only update if formatting succeeded
                    combined_data[field] = formatted_value
                    print(f"⚡ Formatted {field}: '{original_value}' → '{formatted_value}'")

        print(f"✅ Date and efficiency formatting complete")

    except Exception as e:
        print(f"⚠️ Error applying date/efficiency formatting: {e}")

    # Debug: Check if critical time series fields were extracted
    time_series_fields = ["annual_operational_hours", "blending_percentage_of_biomass", "emission_factor_coal"]
    for field in time_series_fields:
        if field in combined_data and combined_data[field] != "default null":
            print(f"✅ {field}: {combined_data[field]}")
        else:
            print(f"⚠️ {field}: Missing or default value")

    return combined_data


def format_commencement_date(date_value: str) -> str:
    """
    Format commencement date to yyyy-mm-ddThh:mm:ss.msZ format

    Args:
        date_value: Input date in various formats

    Returns:
        Formatted date string in ISO 8601 format with milliseconds
    """
    import re
    from datetime import datetime

    if not date_value or date_value in ["", "default null", "Not available", "null"]:
        return "2000-01-01T00:00:00.000Z"

    # If already in correct format, return as is
    if re.match(r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z', date_value):
        return date_value

    try:
        # Try to parse common date formats first (more specific)
        date_formats = [
            "%Y-%m-%d",
            "%Y/%m/%d",
            "%d/%m/%Y",
            "%m/%d/%Y",
            "%Y-%m-%d %H:%M:%S",
            "%Y/%m/%d %H:%M:%S"
        ]

        for fmt in date_formats:
            try:
                parsed_date = datetime.strptime(str(date_value).strip(), fmt)
                return parsed_date.strftime("%Y-%m-%dT%H:%M:%S.000Z")
            except ValueError:
                continue

        # If no format matches, extract year as fallback
        year_match = re.search(r'(\d{4})', str(date_value))
        if year_match:
            year = year_match.group(1)
            # Default to January 1st if only year is available
            return f"{year}-01-01T00:00:00.000Z"

    except Exception as e:
        print(f"⚠️ Error formatting date '{date_value}': {e}")

    # Fallback to default
    return "2000-01-01T00:00:00.000Z"


def format_efficiency_value(value: str) -> str:
    """
    Format efficiency values to decimal format (divide by 100 if percentage)

    Args:
        value: Input efficiency value (could be percentage or decimal)

    Returns:
        Formatted efficiency as decimal string (0.x format)
    """
    if not value or value in ["", "default null", "Not available", "null"]:
        return ""

    try:
        # Remove any non-numeric characters except decimal point and minus
        import re
        clean_value = re.sub(r'[^\d.-]', '', str(value))

        if not clean_value:
            return ""

        numeric_value = float(clean_value)

        # If value is greater than 1, assume it's a percentage and divide by 100
        if numeric_value > 1:
            numeric_value = numeric_value / 100

        # Format to reasonable decimal places
        return f"{numeric_value:.4f}"

    except (ValueError, TypeError) as e:
        print(f"⚠️ Error formatting efficiency value '{value}': {e}")
        return ""


def process_unit_data_formatting(unit_technical_data: dict, session_id: str, plant_uid: str = "") -> dict:
    """
    Format unit data with proper date and efficiency formatting

    Args:
        unit_technical_data: Raw unit data
        session_id: Session ID for logging
        plant_uid: Plant UUID

    Returns:
        Formatted unit data
    """
    print(f"[Session {session_id}] 🔧 Formatting unit data with date and efficiency fixes")

    # Apply date formatting
    if "commencement_date" in unit_technical_data:
        original_date = unit_technical_data["commencement_date"]
        formatted_date = format_commencement_date(original_date)
        unit_technical_data["commencement_date"] = formatted_date
        print(f"[Session {session_id}] 📅 Formatted commencement_date: '{original_date}' → '{formatted_date}'")

    # Apply efficiency formatting (convert to 0.x format)
    efficiency_fields = [
        "closed_cycle_efficiency",
        "closed_cycle_turbine_efficiency",
        "open_cycle_turbine_efficiency",
        "efficiency_loss_biomass_cofiring"
    ]

    for field in efficiency_fields:
        if field in unit_technical_data:
            original_value = unit_technical_data[field]
            formatted_value = format_efficiency_value(original_value)
            if formatted_value:  # Only update if formatting succeeded
                unit_technical_data[field] = formatted_value
                print(f"[Session {session_id}] ⚡ Formatted {field}: '{original_value}' → '{formatted_value}'")

    return unit_technical_data