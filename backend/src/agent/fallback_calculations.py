"""
Fallback Calculations Module
Automatically calculates missing power plant data using engineering formulas
when search-based extraction fails to find the information.
"""

import logging
from typing import Dict, List, Optional, Any
from agent.reference_data import CALCULATOR, REFERENCE_DATA
from agent.tools_and_schemas import YearlyData

logger = logging.getLogger(__name__)

class FallbackCalculationEngine:
    """
    Engine that calculates missing power plant data using reference formulas
    """
    
    def __init__(self):
        self.calculator = CALCULATOR
        self.ref_data = REFERENCE_DATA
    
    def enhance_unit_data(self, extracted_data: Dict, unit_context: Dict, session_id: str = "unknown") -> Dict:
        """
        Main method to enhance extracted data with calculated values for missing fields
        
        Args:
            extracted_data: Data extracted from search
            unit_context: Context about the unit (capacity, technology, etc.)
            session_id: Session identifier for logging
            
        Returns:
            Enhanced data with calculated missing fields
        """
        enhanced_data = extracted_data.copy()
        calculations_performed = []
        
        try:
            # Extract basic unit info
            capacity = self._extract_numeric_value(unit_context.get("capacity", 0))
            technology = unit_context.get("technology", "subcritical")
            
            print(f"[Session {session_id}] 🔧 FALLBACK CALCULATIONS: Enhancing unit data")
            print(f"[Session {session_id}] 📊 Unit Context: {capacity}MW {technology}")
            
            # ALL CALCULATIONS REMOVED - Only Excel-based calculations will be used
            print(f"[Session {session_id}] 🚫 ALL fallback calculations DISABLED - using only Excel formulas")
            # All old calculation methods have been removed
            # Only the sophisticated calculations from 'current state calculations.xlsx' will be used
            
            # 3. Estimate Plant Efficiency if missing
            if self._is_missing_or_empty(enhanced_data, "coal_unit_efficiency"):
                efficiency = self._calculate_efficiency_fallback(technology)
                if efficiency:
                    enhanced_data["coal_unit_efficiency"] = efficiency
                    calculations_performed.append("Unit Efficiency")
            
            # Gross Power Generation calculations removed - using only Excel formulas
            print(f"[Session {session_id}] 🚫 Gross Power Generation fallback calculations DISABLED")
            
            # 5. Calculate Emission Factor if missing
            if self._is_missing_or_empty(enhanced_data, "emission_factor"):
                emission_data = self._calculate_emission_factor_fallback(enhanced_data, unit_context)
                if emission_data:
                    enhanced_data["emission_factor"] = emission_data
                    calculations_performed.append("Emission Factor")
            
            # 6. Calculate Plant Availability Factor if missing
            if self._is_missing_or_empty(enhanced_data, "PAF"):
                paf_data = self._calculate_paf_fallback(enhanced_data, unit_context)
                if paf_data:
                    enhanced_data["PAF"] = paf_data
                    calculations_performed.append("PAF")
            
            # 7. Populate Reference Fields (GCV, Technology Parameters)
            reference_fields_populated = self._populate_reference_fields(enhanced_data, unit_context)
            if reference_fields_populated:
                calculations_performed.extend(reference_fields_populated)
            
            # 8. Generate Multi-Year Time Series for all time-series fields
            time_series_enhanced = self._enhance_time_series_data(enhanced_data, unit_context)
            if time_series_enhanced:
                calculations_performed.extend(time_series_enhanced)
            
            # 9. Fix years_percentage format in fuel_type
            fuel_enhanced = self._enhance_fuel_years_percentage(enhanced_data, unit_context)
            if fuel_enhanced:
                calculations_performed.append("Fuel Years Percentage")
            
            # 10. Normalize data types and value ranges to match reference format
            normalization_performed = self._normalize_data_types_and_ranges(enhanced_data, unit_context, session_id)
            if normalization_performed:
                calculations_performed.extend(normalization_performed)
            
            # 11. Clean output format (remove metadata)
            self._clean_output_format(enhanced_data)
            
            # 12. Validate and flag unusual values (but don't add to output)
            validation_results = self.calculator.validate_extracted_values(enhanced_data, unit_context)
            if validation_results:
                # Log validation warnings but don't add to final output
                print(f"[Session {session_id}] ⚠️ Validation warnings: {validation_results}")
                # enhanced_data["_validation_warnings"] = validation_results  # REMOVED
            
            # Log results
            if calculations_performed:
                print(f"[Session {session_id}] ✅ CALCULATIONS COMPLETED: {', '.join(calculations_performed)}")
            else:
                print(f"[Session {session_id}] ℹ️  No fallback calculations needed - all data available")
                
        except Exception as e:
            logger.error(f"[Session {session_id}] Error in fallback calculations: {str(e)}")
            print(f"[Session {session_id}] ❌ FALLBACK CALCULATION ERROR: {str(e)}")
        
        return enhanced_data
    
    def _is_missing_or_empty(self, data: Dict, field: str) -> bool:
        """Check if field is missing or empty"""
        if field not in data:
            return True
        
        value = data[field]
        
        # Handle different field types
        if isinstance(value, list):
            return len(value) == 0
        elif isinstance(value, str):
            return value.strip() in ["", "Not available", "N/A", "Unknown"]
        elif value is None:
            return True
        
        return False
    
    def _extract_numeric_value(self, value: Any) -> float:
        """Extract numeric value from various formats"""
        if isinstance(value, (int, float)):
            return float(value)

        if isinstance(value, str):
            # Remove common non-numeric characters
            cleaned = value.replace(",", "").replace("MW", "").replace("GW", "").replace("%", "").strip()

            # CRITICAL FIX: Check for empty string before float conversion
            if not cleaned or cleaned in ["", "Not available", "N/A", "Unknown", "default null"]:
                return 0.0

            try:
                return float(cleaned)
            except ValueError:
                return 0.0

        return 0.0
    
    def _fix_unrealistic_plf_values(self, plf_data: List[Dict], extracted_data: Dict, unit_context: Dict, session_id: str) -> List[Dict]:
        """Fix individual unrealistic PLF values by recalculating from generation data"""
        try:
            capacity = self._extract_numeric_value(unit_context.get("capacity", 0))
            generation_data = extracted_data.get("gross_power_generation", [])
            
            if capacity <= 0 or not generation_data:
                return plf_data
            
            # Create a lookup of generation data by year
            generation_by_year = {}
            for gen_record in generation_data:
                if isinstance(gen_record, dict) and "value" in gen_record and "year" in gen_record:
                    year = gen_record["year"]
                    generation_mwh = self._extract_generation_value_with_unit_conversion(gen_record["value"])
                    if generation_mwh > 0:
                        generation_by_year[year] = generation_mwh
            
            fixed_plf_data = []
            for plf_item in plf_data:
                if isinstance(plf_item, dict) and "value" in plf_item:
                    plf_val = self._extract_numeric_value(plf_item["value"])
                    year = plf_item.get("year", "Unknown")
                    
                    # If PLF is unrealistic and we have generation data for this year
                    if (plf_val > 100 or plf_val < 0) and year in generation_by_year:
                        generation_mwh = generation_by_year[year]
                        
                        # Calculate correct PLF
                        corrected_plf = self.calculator.calculate_plf(generation_mwh, capacity)
                        
                        # Apply unit detection if still unrealistic
                        if corrected_plf > 100:
                            # The generation data has unit issues - try different conversions
                            
                            # Method 1: Try GWh to MWh conversion (divide by 1000)
                            corrected_generation = generation_mwh / 1000
                            corrected_plf = self.calculator.calculate_plf(corrected_generation, capacity)
                            
                            if 10 <= corrected_plf <= 100:
                                print(f"[Session {session_id}] 🔧 Fixed PLF for {year}: {plf_val}% → {corrected_plf:.1f}% (GWh→MWh conversion)")
                            else:
                                # Method 2: Try assuming generation is in GWh but read as MWh
                                # This means we need to divide by 1000 again
                                corrected_generation = generation_mwh / 1000000
                                corrected_plf = self.calculator.calculate_plf(corrected_generation, capacity)
                                
                                if 10 <= corrected_plf <= 100:
                                    print(f"[Session {session_id}] 🔧 Fixed PLF for {year}: {plf_val}% → {corrected_plf:.1f}% (TWh→MWh conversion)")
                                else:
                                    # Method 3: Calculate what generation SHOULD be for reasonable PLF
                                    # Assume 70% PLF as reasonable target
                                    max_generation = capacity * 8760
                                    target_plf = 70.0  # Reasonable PLF
                                    target_generation = (target_plf / 100) * max_generation
                                    
                                    print(f"[Session {session_id}] 🔧 Fixed PLF for {year}: {plf_val}% → {target_plf}% (estimated reasonable PLF)")
                                    corrected_plf = target_plf
                        else:
                            print(f"[Session {session_id}] 🔧 Fixed PLF for {year}: {plf_val}% → {corrected_plf:.1f}%")
                        
                        # Add corrected PLF
                        fixed_plf_data.append({
                            "value": f"{corrected_plf:.1f}%",
                            "year": year,
                            "_corrected": True,
                            "_original_value": plf_item["value"]
                        })
                    else:
                        # Keep original if reasonable or no generation data
                        fixed_plf_data.append(plf_item)
                else:
                    fixed_plf_data.append(plf_item)
            
            return fixed_plf_data
            
        except Exception as e:
            logger.error(f"Error fixing unrealistic PLF values: {str(e)}")
            return plf_data
    
    def _extract_generation_value_with_unit_conversion(self, value_str: str) -> float:
        """Extract generation value and convert to MWh if needed"""
        if not isinstance(value_str, str):
            return self._extract_numeric_value(value_str)
        
        # Check for unit indicators
        value_lower = value_str.lower()
        numeric_value = self._extract_numeric_value(value_str)
        
        if numeric_value <= 0:
            return 0.0
        
        # Convert based on detected units
        if any(unit in value_lower for unit in ['gwh', 'gw-h', 'gw h']):
            # Convert GWh to MWh
            return numeric_value * 1000
        elif any(unit in value_lower for unit in ['twh', 'tw-h', 'tw h']):
            # Convert TWh to MWh
            return numeric_value * 1000000
        elif any(unit in value_lower for unit in ['kwh', 'kw-h', 'kw h']):
            # Convert kWh to MWh
            return numeric_value / 1000
        else:
            # Assume MWh if no unit specified, but validate against capacity
            return numeric_value
    
    def _calculate_plf_fallback(self, extracted_data: Dict, unit_context: Dict) -> Optional[List[Dict]]:
        """
        Calculate PLF following CSV requirements:
        1. If annual generation data is available, calculate PLF from it
        2. If no generation data, use country-based typical PLF standards
        """
        try:
            capacity = self._extract_numeric_value(unit_context.get("capacity", 0))
            
            if capacity <= 0:
                return None
            
            # First try to calculate from actual generation data
            generation_data = extracted_data.get("gross_power_generation", [])
            
            if generation_data:
                plf_data = []
                for gen_record in generation_data:
                    if isinstance(gen_record, dict) and "value" in gen_record:
                        # Use enhanced unit conversion
                        generation_mwh = self._extract_generation_value_with_unit_conversion(gen_record["value"])
                        year = gen_record.get("year", "Unknown")
                        
                        if generation_mwh > 0:
                            # Calculate PLF
                            plf = self.calculator.calculate_plf(generation_mwh, capacity)
                            
                            # Intelligent unit detection: if PLF > 100%, likely unit mismatch
                            if plf > 100:
                                print(f"[PLF Fix] Unrealistic PLF detected: {plf:.1f}% (Generation: {generation_mwh}, Capacity: {capacity} MW)")
                                
                                # Calculate what the generation should be for reasonable PLF
                                max_generation = capacity * 8760
                                
                                # Method 1: Try dividing by 1000 (GWh to MWh conversion)
                                corrected_generation = generation_mwh / 1000
                                corrected_plf = self.calculator.calculate_plf(corrected_generation, capacity)
                                
                                if 10 <= corrected_plf <= 100:  # Reasonable PLF range
                                    print(f"[PLF Fix] Applied GWh→MWh conversion: {generation_mwh} → {corrected_generation} MWh")
                                    print(f"[PLF Fix] PLF corrected: {plf:.1f}% → {corrected_plf:.1f}%")
                                    generation_mwh = corrected_generation
                                    plf = corrected_plf
                                else:
                                    # Method 2: Try dividing by 1000000 (TWh to MWh conversion)
                                    corrected_generation = generation_mwh / 1000000
                                    corrected_plf = self.calculator.calculate_plf(corrected_generation, capacity)
                                    
                                    if 10 <= corrected_plf <= 100:  # Reasonable PLF range
                                        print(f"[PLF Fix] Applied TWh→MWh conversion: {generation_mwh} → {corrected_generation} MWh")
                                        print(f"[PLF Fix] PLF corrected: {plf:.1f}% → {corrected_plf:.1f}%")
                                        generation_mwh = corrected_generation
                                        plf = corrected_plf
                                    else:
                                        # Method 3: Calculate reasonable PLF based on typical values
                                        # If generation data is completely wrong, estimate reasonable PLF
                                        typical_plf = 70.0  # Typical PLF for coal plants
                                        print(f"[PLF Fix] Generation data too unrealistic, using typical PLF: {typical_plf}%")
                                        plf = typical_plf
                            
                            # Only add if PLF is reasonable
                            if 0 <= plf <= 100:
                                plf_data.append({
                                    "value": f"{plf:.1f}%",
                                    "year": year,
                                    "_calculated": True,
                                    "_method": "Calculated from generation data with unit validation"
                                })
                            else:
                                print(f"[PLF Fix] Skipping unrealistic PLF: {plf:.1f}% for year {year}")
                
                if plf_data:
                    return plf_data
            
            # If no generation data, use country-based typical PLF
            technology = unit_context.get("technology", "subcritical")
            country = unit_context.get("country", "default")
            
            typical_plf = self.calculator.get_country_plf_standard(country, technology)
            
            # Generate multi-year PLF data with variations
            target_years = ["2024", "2023", "2022", "2021", "2020"]
            variations = [0.95, 1.02, 0.98, 1.05, 0.97]  # Realistic variations
            
            plf_data = []
            for i, year in enumerate(target_years):
                variation = variations[i % len(variations)]
                yearly_plf = typical_plf * variation
                
                plf_data.append({
                    "value": f"{yearly_plf:.1f}%",
                    "year": year,
                    "_calculated": True,
                    "_method": f"Country standard PLF for {country} {technology} plants"
                })
            
            print(f"🔧 GENERATED PLF DATA: {typical_plf}% typical for {country} {technology}")
            return plf_data
            
        except Exception as e:
            logger.error(f"Error calculating PLF fallback: {str(e)}")
            return None
    
    def _calculate_auxiliary_power_fallback(self, capacity: float, technology: str) -> Optional[List[Dict]]:
        """Calculate auxiliary power consumption based on capacity and technology"""
        try:
            if capacity <= 0:
                return None
            
            aux_power = self.calculator.estimate_auxiliary_power_consumption(capacity, technology)
            
            return [{
                "value": f"{aux_power}%",
                "year": "Estimated",
                "_calculated": True,
                "_method": f"Industry standard for {capacity}MW {technology} plant"
            }]
            
        except Exception as e:
            logger.error(f"Error calculating auxiliary power fallback: {str(e)}")
            return None
    
    def _calculate_efficiency_fallback(self, technology: str) -> Optional[str]:
        """Estimate plant efficiency based on technology"""
        try:
            efficiency = self.calculator.estimate_plant_efficiency(technology)
            return f"{efficiency}%"
            
        except Exception as e:
            logger.error(f"Error calculating efficiency fallback: {str(e)}")
            return None
    
    def _populate_reference_fields(self, extracted_data: Dict, unit_context: Dict) -> List[str]:
        """Populate reference fields from standard values"""
        populated_fields = []
        
        try:
            # Coal GCV values
            if self._is_missing_or_empty(extracted_data, "gcv_coal"):
                # 🚨 FIXED: Extract actual coal type from fuel_type data
                coal_type = "bituminous"  # Default coal type

                # Try to get coal type from fuel_type data
                fuel_type_data = extracted_data.get("fuel_type", [])
                if isinstance(fuel_type_data, list) and fuel_type_data:
                    # Find the primary coal fuel type (highest generation or first coal type)
                    for fuel in fuel_type_data:
                        if isinstance(fuel, dict):
                            fuel_name = fuel.get("fuel", "").lower()
                            fuel_subtype = fuel.get("type", "").lower()

                            if "coal" in fuel_name and fuel_subtype:
                                coal_type = fuel_subtype
                                print(f"🔧 Found coal type from fuel_type data: {coal_type}")
                                break

                # Try to get coal type from fuel_type data
                fuel_type_data = extracted_data.get("fuel_type", [])
                if fuel_type_data and isinstance(fuel_type_data, list):
                    for fuel_item in fuel_type_data:
                        if isinstance(fuel_item, dict):
                            fuel_name = fuel_item.get("fuel", "").lower()
                            fuel_subtype = fuel_item.get("type", "").lower()

                            # Check if this is a coal fuel type
                            if "coal" in fuel_name:
                                # Map fuel subtypes to our coal types
                                if "lignite" in fuel_subtype:
                                    coal_type = "lignite"
                                    break
                                elif "bituminous" in fuel_subtype:
                                    coal_type = "bituminous"
                                    break
                                elif "sub" in fuel_subtype and "bituminous" in fuel_subtype:
                                    coal_type = "sub_bituminous"
                                    break
                                elif "anthracite" in fuel_subtype:
                                    coal_type = "anthracite"
                                    break

                print(f"🔧 Using coal type '{coal_type}' for GCV calculation")

                if coal_type and coal_type != "Not available":
                    gcv_coal = self.calculator.get_coal_gcv(coal_type)
                    extracted_data["gcv_coal"] = str(gcv_coal)
                    extracted_data["gcv_coal_unit"] = "kCal/kg"
                    populated_fields.append("GCV Coal")
                    print(f"✅ Set GCV Coal to {gcv_coal} kcal/kg for {coal_type} coal")
            
            # Biomass GCV values
            if self._is_missing_or_empty(extracted_data, "gcv_biomass"):
                biomass_type = "wood_chips"  # Default biomass type
                if biomass_type and biomass_type != "Not available":
                    gcv_biomass = self.calculator.get_biomass_gcv(biomass_type)
                    extracted_data["gcv_biomass"] = str(gcv_biomass)
                    extracted_data["gcv_biomass_unit"] = "kCal/kg"
                    populated_fields.append("GCV Biomass")
            
            # Natural Gas GCV values
            if self._is_missing_or_empty(extracted_data, "gcv_natural_gas"):
                gcv_gas = self.calculator.get_natural_gas_gcv()
                extracted_data["gcv_natural_gas"] = str(gcv_gas)
                extracted_data["gcv_natural_gas_unit"] = "kcal/scm"
                populated_fields.append("GCV Natural Gas")
            
            # Gas turbine efficiency values
            if self._is_missing_or_empty(extracted_data, "open_cycle_gas_turbine_efficency"):
                ocgt_eff = self.calculator.get_ocgt_efficiency()
                extracted_data["open_cycle_gas_turbine_efficency"] = str(ocgt_eff)
                populated_fields.append("OCGT Efficiency")
            
            if self._is_missing_or_empty(extracted_data, "closed_cylce_gas_turbine_efficency"):
                ccgt_eff = self.calculator.get_ccgt_efficiency()
                extracted_data["closed_cylce_gas_turbine_efficency"] = str(ccgt_eff)
                populated_fields.append("CCGT Efficiency")
            
            # Heat rate values
            if self._is_missing_or_empty(extracted_data, "open_cycle_heat_rate"):
                ocgt_hr = self.calculator.get_ocgt_heat_rate()
                extracted_data["open_cycle_heat_rate"] = str(ocgt_hr)
                populated_fields.append("OCGT Heat Rate")
            
            if self._is_missing_or_empty(extracted_data, "combined_cycle_heat_rate"):
                ccgt_hr = self.calculator.get_ccgt_heat_rate()
                extracted_data["combined_cycle_heat_rate"] = str(ccgt_hr)
                populated_fields.append("CCGT Heat Rate")
            
            # Cofiring efficiency loss
            if self._is_missing_or_empty(extracted_data, "efficiency_loss_biomass_cofiring"):
                cofiring_loss = self.calculator.get_cofiring_efficiency_loss()
                extracted_data["efficiency_loss_biomass_cofiring"] = str(cofiring_loss)
                populated_fields.append("Cofiring Efficiency Loss")
                
        except Exception as e:
            logger.error(f"Error populating reference fields: {str(e)}")
        
        return populated_fields
    
    def _enhance_time_series_data(self, extracted_data: Dict, unit_context: Dict) -> List[str]:
        """Enhance time series fields to have 4-5 years of data (2020-2024)"""
        enhanced_fields = []
        target_years = ["2024", "2023", "2022", "2021", "2020"]
        
        try:
            # Enhance PLF data - but only if we have very limited data
            if "plf" in extracted_data and isinstance(extracted_data["plf"], list):
                # Only expand if we have less than 3 data points
                if len(extracted_data["plf"]) < 3:
                    enhanced_plf = self._expand_time_series(extracted_data["plf"], target_years, "plf")
                    if len(enhanced_plf) > len(extracted_data["plf"]):
                        extracted_data["plf"] = enhanced_plf
                        enhanced_fields.append("PLF Multi-Year")
                else:
                    # Clean existing data format but don't add generated data
                    self._clean_time_series_format(extracted_data["plf"])
                    enhanced_fields.append("PLF Format Cleaned")
            
            # Enhance PAF data - but only if we have very limited data
            if "PAF" in extracted_data and isinstance(extracted_data["PAF"], list):
                if len(extracted_data["PAF"]) < 3:
                    enhanced_paf = self._expand_time_series(extracted_data["PAF"], target_years, "paf")
                    if len(enhanced_paf) > len(extracted_data["PAF"]):
                        extracted_data["PAF"] = enhanced_paf
                        enhanced_fields.append("PAF Multi-Year")
                else:
                    # Clean existing data format but don't add generated data
                    self._clean_time_series_format(extracted_data["PAF"])
                    enhanced_fields.append("PAF Format Cleaned")
            
            # Enhance auxiliary power data - but only if we have very limited data
            if "auxiliary_power_consumed" in extracted_data and isinstance(extracted_data["auxiliary_power_consumed"], list):
                if len(extracted_data["auxiliary_power_consumed"]) < 3:
                    enhanced_aux = self._expand_time_series(extracted_data["auxiliary_power_consumed"], target_years, "aux_power")
                    if len(enhanced_aux) > len(extracted_data["auxiliary_power_consumed"]):
                        extracted_data["auxiliary_power_consumed"] = enhanced_aux
                        enhanced_fields.append("Auxiliary Power Multi-Year")
                else:
                    # Clean existing data format but don't add generated data
                    self._clean_time_series_format(extracted_data["auxiliary_power_consumed"])
                    enhanced_fields.append("Auxiliary Power Format Cleaned")
            
            # Enhance emission factor data - but only if we have very limited data
            if "emission_factor" in extracted_data and isinstance(extracted_data["emission_factor"], list):
                if len(extracted_data["emission_factor"]) < 3:
                    enhanced_emission = self._expand_time_series(extracted_data["emission_factor"], target_years, "emission_factor")
                    if len(enhanced_emission) > len(extracted_data["emission_factor"]):
                        extracted_data["emission_factor"] = enhanced_emission
                        enhanced_fields.append("Emission Factor Multi-Year")
                else:
                    # Clean existing data format but don't add generated data
                    self._clean_time_series_format(extracted_data["emission_factor"])
                    enhanced_fields.append("Emission Factor Format Cleaned")
            
            # Enhance generation data - but only if we have very limited data
            if "gross_power_generation" in extracted_data and isinstance(extracted_data["gross_power_generation"], list):
                if len(extracted_data["gross_power_generation"]) < 3:
                    enhanced_generation = self._expand_time_series(extracted_data["gross_power_generation"], target_years, "generation")
                    if len(enhanced_generation) > len(extracted_data["gross_power_generation"]):
                        extracted_data["gross_power_generation"] = enhanced_generation
                        enhanced_fields.append("Generation Multi-Year")
                else:
                    # Clean existing data format but don't add generated data
                    self._clean_time_series_format(extracted_data["gross_power_generation"])
                    enhanced_fields.append("Generation Format Cleaned")
                    
        except Exception as e:
            logger.error(f"Error enhancing time series data: {str(e)}")
        
        return enhanced_fields
    
    def _clean_time_series_format(self, time_series_data: List[Dict]):
        """Clean and standardize time-series data format without adding generated data"""
        for item in time_series_data:
            if isinstance(item, dict):
                # Standardize year format
                if "year" in item:
                    year_str = str(item["year"]).strip()
                    # Extract just the year from complex formats like "2021-22" or "September 2019"
                    import re
                    year_match = re.search(r'\b(20\d{2})\b', year_str)
                    if year_match:
                        item["year"] = year_match.group(1)
                    elif year_str == "N/A":
                        item["year"] = "N/A"
                
                # Clean value format
                if "value" in item:
                    value_str = str(item["value"]).strip()
                    # Remove inconsistent formatting but keep the actual value
                    if value_str.endswith('%'):
                        # Keep percentage format consistent
                        clean_value = value_str.replace('%', '').strip()
                        try:
                            float(clean_value)
                            item["value"] = f"{clean_value}%"
                        except:
                            pass
    
    def _expand_time_series(self, existing_data: List[Dict], target_years: List[str], data_type: str) -> List[Dict]:
        """Expand time series data to cover target years"""
        if not existing_data:
            return existing_data
        
        # Get existing years
        existing_years = {item.get("year") for item in existing_data if item.get("year")}
        enhanced_data = existing_data.copy()
        
        # Calculate average value for missing years
        existing_values = []
        for item in existing_data:
            try:
                value_str = item.get("value", "0")
                # Clean value string (remove units, percentages, etc.)
                clean_value = value_str.replace("%", "").replace("kg CO2e/kWh", "").replace(",", "").strip()
                value = float(clean_value)
                existing_values.append(value)
            except:
                continue
        
        if not existing_values:
            return existing_data
        
        avg_value = sum(existing_values) / len(existing_values)
        
        # Add missing years with slight variations around average
        variations = [0.95, 0.97, 1.02, 1.05, 0.98]  # Small variations to make data realistic
        
        for i, year in enumerate(target_years):
            if year not in existing_years:
                variation = variations[i % len(variations)]
                estimated_value = avg_value * variation
                
                # Format value based on data type
                if data_type in ["plf", "paf", "aux_power"]:
                    formatted_value = f"{estimated_value:.2f}%"
                elif data_type == "emission_factor":
                    formatted_value = f"{estimated_value:.3f}"
                else:
                    formatted_value = f"{estimated_value:.0f}"
                
                enhanced_data.append({
                    "value": formatted_value,
                    "year": year
                })
        
        # Sort by year (newest first)
        enhanced_data.sort(key=lambda x: x.get("year", "0"), reverse=True)
        return enhanced_data
    
    def _enhance_fuel_years_percentage(self, extracted_data: Dict, unit_context: Dict) -> bool:
        """Fix fuel_type years_percentage format to include multiple years (coal only, exclude biomass)"""
        try:
            if "fuel_type" not in extracted_data or not extracted_data["fuel_type"]:
                return False

            enhanced = False

            # Calculate target years based on unit operational lifetime
            target_years = self._get_unit_operational_years(extracted_data, unit_context)

            # Filter to keep only coal fuel types, exclude biomass
            coal_fuel_types = []

            for fuel_item in extracted_data["fuel_type"]:
                if not isinstance(fuel_item, dict):
                    continue

                fuel_name = fuel_item.get("fuel", "").lower()
                fuel_type = fuel_item.get("type", "").lower()

                # Only keep coal fuel types, exclude biomass
                is_coal = (
                    "coal" in fuel_name or
                    any(coal_type in fuel_type for coal_type in [
                        "bituminous", "sub-bituminous", "lignite", "anthracite"
                    ])
                )

                is_biomass = (
                    "biomass" in fuel_name or
                    "wood" in fuel_type or "pellet" in fuel_type or
                    "pks" in fuel_type or "palm" in fuel_type or
                    "kernel" in fuel_type or "husk" in fuel_type or
                    "shell" in fuel_type
                )

                # Only process coal fuel types
                if not (is_coal and not is_biomass):
                    continue

                coal_fuel_types.append(fuel_item)
                
                years_percentage = fuel_item.get("years_percentage")
                
                # If years_percentage is null or empty, generate default data (coal only)
                if not years_percentage:
                    fuel_type = fuel_item.get("fuel", "Coal")

                    # Generate realistic percentages for coal (since biomass is excluded)
                    # Coal percentage should be higher since we're not including biomass cofiring
                    base_percentage = 95  # Coal typically 95% when biomass is excluded
                    variations = [0, -2, 1, -1, 2]  # Small year-to-year variations

                    years_percentage = {}
                    for i, year in enumerate(target_years):
                        percentage = base_percentage + variations[i]
                        years_percentage[year] = str(max(0, min(100, percentage)))  # Keep between 0-100

                    fuel_item["years_percentage"] = years_percentage
                    enhanced = True
                
                # If years_percentage exists but doesn't have enough years, expand it
                elif isinstance(years_percentage, dict) and len(years_percentage) < 4:
                    existing_years = list(years_percentage.keys())
                    existing_values = [float(v) for v in years_percentage.values() if v]
                    
                    if existing_values:
                        avg_percentage = sum(existing_values) / len(existing_values)
                        
                        for year in target_years:
                            if year not in existing_years:
                                # Add small variation around average
                                variation = 1 + (hash(year) % 10 - 5) / 100  # ±5% variation
                                new_percentage = avg_percentage * variation
                                years_percentage[year] = str(max(0, min(100, int(new_percentage))))
                        
                        enhanced = True

            # Update fuel_type with filtered coal-only entries
            extracted_data["fuel_type"] = coal_fuel_types

            return enhanced
            
        except Exception as e:
            logger.error(f"Error enhancing fuel years percentage: {str(e)}")
            return False
    
    def _clean_output_format(self, extracted_data: Dict):
        """Remove metadata fields from output"""
        try:
            # Clean time series fields
            time_series_fields = ["plf", "PAF", "auxiliary_power_consumed", "emission_factor", "gross_power_generation"]
            
            for field in time_series_fields:
                if field in extracted_data and isinstance(extracted_data[field], list):
                    for item in extracted_data[field]:
                        if isinstance(item, dict):
                            # Remove metadata fields
                            item.pop("_calculated", None)
                            item.pop("_method", None)
                            item.pop("_calculation_details", None)
                            
                            # Clean value format
                            if "value" in item:
                                value = item["value"]
                                if isinstance(value, str):
                                    # Remove unwanted text from values
                                    if "kg CO2e/kWh" in value:
                                        item["value"] = value.replace(" kg CO2e/kWh", "")
                                    elif "%" in value and field == "emission_factor":
                                        # Don't remove % from PLF, PAF, aux_power but remove from emission_factor
                                        item["value"] = value.replace("%", "")
                                    
        except Exception as e:
            logger.error(f"Error cleaning output format: {str(e)}")

    def _calculate_emission_factor_fallback(self, extracted_data: Dict, unit_context: Dict) -> Optional[List[Dict]]:
        """
        Calculate emission factor using CSV methods:
        Method 1: (Annual total emission by unit) / (Annual generation by unit) 
        Method 2: Engineering calculation from coal properties
        """
        try:
            # First try Method 1: Look for emission data directly
            emission_method1_data = self._search_for_emission_data(extracted_data)
            generation_data = extracted_data.get("gross_power_generation", [])
            
            if emission_method1_data and generation_data:
                print("🔧 USING METHOD 1: Direct emission data calculation")
                emission_data = []
                
                # Match emission data with generation data by year
                for emission_record in emission_method1_data:
                    emission_value = self._extract_numeric_value(emission_record["value"])
                    year = emission_record.get("year", "Unknown")
                    
                    # Find corresponding generation for same year
                    for gen_record in generation_data:
                        if gen_record.get("year") == year:
                            generation_mwh = self._extract_generation_value_with_unit_conversion(gen_record["value"])
                            if generation_mwh > 0 and emission_value > 0:
                                # Convert generation to kWh
                                generation_kwh = generation_mwh * 1000
                                
                                # Calculate emission factor
                                emission_factor = emission_value / generation_kwh
                                
                                emission_data.append({
                                    "value": f"{emission_factor:.3f}",
                                    "year": year,
                                    "_calculated": True,
                                    "_method": "Method 1: Direct emission/generation calculation"
                                })
                                break
                
                if emission_data:
                    return emission_data
            
            # Method 2: Engineering calculation (existing method)
            fuel_data = extracted_data.get("fuel_type", [])
            
            if not generation_data:
                return None
            
            # Extract coal type from fuel data (coal only, exclude biomass)
            coal_type = "bituminous"  # Default
            for fuel in fuel_data:
                if isinstance(fuel, dict):
                    fuel_name = fuel.get("fuel", "").lower()
                    fuel_type = fuel.get("type", "").lower()

                    # Only process coal fuel types, skip biomass
                    is_coal = "coal" in fuel_name or any(ct in fuel_type for ct in ["bituminous", "sub-bituminous", "lignite", "anthracite"])
                    is_biomass = "biomass" in fuel_name or any(bt in fuel_type for bt in ["wood", "pellet", "pks", "palm", "kernel", "husk", "shell"])

                    if is_coal and not is_biomass:
                        if "sub" in fuel_type and "bituminous" in fuel_type:
                            coal_type = "sub_bituminous"
                        elif "bituminous" in fuel_type:
                            coal_type = "bituminous"
                        elif "lignite" in fuel_type:
                            coal_type = "lignite"
                        elif "anthracite" in fuel_type:
                            coal_type = "anthracite"
            
            # Get technology and capacity
            technology = unit_context.get("technology", "subcritical")
            capacity = self._extract_numeric_value(unit_context.get("capacity", 0))
            
            emission_data = []
            for gen_record in generation_data:
                if isinstance(gen_record, dict) and "value" in gen_record:
                    generation_mwh = self._extract_generation_value_with_unit_conversion(gen_record["value"])
                    year = gen_record.get("year", "Unknown")
                    
                    if generation_mwh > 0:
                        # Convert MWh to kWh
                        generation_kwh = generation_mwh * 1000
                        
                        # Calculate emission factor
                        result = self.calculator.calculate_emission_factor_from_coal(
                            generation_kwh, coal_type, technology=technology
                        )
                        
                        emission_data.append({
                            "value": f"{result['emission_factor']} kg CO2e/kWh",
                            "year": year,
                            "_calculated": True,
                            "_method": f"Calculated from {coal_type} coal and {technology} technology",
                            "_calculation_details": result["calculation_details"]
                        })
            
            return emission_data if emission_data else None
            
        except Exception as e:
            logger.error(f"Error calculating emission factor fallback: {str(e)}")
            return None
    
    def _calculate_paf_fallback(self, extracted_data: Dict, unit_context: Dict) -> Optional[List[Dict]]:
        """
        Calculate PAF following CSV requirements:
        1. First try to find 'No.of hours plant was available' 
        2. If not found, set PAF = 1 for each year
        """
        try:
            # First check if we have any availability hours data from search
            availability_data = self._search_for_availability_hours(extracted_data)
            
            if availability_data:
                # Calculate PAF from availability hours
                paf_data = []
                for avail_record in availability_data:
                    available_hours = self._extract_numeric_value(avail_record["value"])
                    year = avail_record.get("year", "Unknown")
                    total_hours = 8760  # Total hours in a year
                    
                    paf_value = (available_hours / total_hours) * 100
                    paf_data.append({
                        "value": f"{paf_value:.1f}%",
                        "year": year,
                        "_calculated": True,
                        "_method": "Calculated from availability hours data"
                    })
                
                return paf_data
            
            # If no availability hours found, set PAF = 1 for each year as per requirement
            target_years = ["2024", "2023", "2022", "2021", "2020"]
            paf_data = []
            
            for year in target_years:
                paf_data.append({
                    "value": "1",  # Set PAF = 1 as per requirement
                    "year": year,
                    "_calculated": True,
                    "_method": "Default fallback (availability hours not found)"
                })
            
            return paf_data
            
        except Exception as e:
            logger.error(f"Error calculating PAF fallback: {str(e)}")
            return None
    
    def _calculate_generation_fallback(self, extracted_data: Dict, unit_context: Dict) -> Optional[List[Dict]]:
        """
        Calculate gross power generation using country-based industry standards
        Following CSV requirement: Use industry standard values based on plant country
        """
        try:
            capacity = self._extract_numeric_value(unit_context.get("capacity", 0))
            technology = unit_context.get("technology", "subcritical")
            country = unit_context.get("country", "default")
            
            if capacity <= 0:
                return None
            
            print(f"🔧 GENERATING POWER GENERATION DATA: {capacity}MW {technology} plant in {country}")
            
            # Calculate generation using country standards
            annual_generation_mwh = self.calculator.calculate_generation_from_country_standards(
                capacity, country, technology
            )
            
            # Generate multi-year data with slight variations
            target_years = ["2024", "2023", "2022", "2021", "2020"]
            variations = [0.98, 1.02, 0.97, 1.05, 0.99]  # Realistic year-to-year variations
            
            generation_data = []
            for i, year in enumerate(target_years):
                variation = variations[i % len(variations)]
                yearly_generation = annual_generation_mwh * variation
                
                generation_data.append({
                    "value": f"{yearly_generation:.0f}",
                    "year": year,
                    "_calculated": True,
                    "_method": f"Country standard for {country} {technology} plants"
                })
            
            print(f"✅ GENERATED POWER DATA: {len(generation_data)} years, avg {annual_generation_mwh:.0f} MWh/year")
            return generation_data
            
        except Exception as e:
            logger.error(f"Error calculating generation fallback: {str(e)}")
            return None
    
    def _search_for_availability_hours(self, extracted_data: Dict) -> Optional[List[Dict]]:
        """Search for availability hours data in extracted data"""
        # Look for availability-related data that might have been extracted
        availability_keywords = [
            "availability_hours", "available_hours", "operational_hours", 
            "plant_availability", "hours_available", "availability_factor"
        ]
        
        for keyword in availability_keywords:
            if keyword in extracted_data:
                data = extracted_data[keyword]
                if isinstance(data, list) and data:
                    return data
        
        return None
    
    def _search_for_emission_data(self, extracted_data: Dict) -> Optional[List[Dict]]:
        """Search for direct emission data in extracted data"""
        # Look for emission-related data that might have been extracted
        emission_keywords = [
            "annual_emissions", "total_emissions", "co2_emissions", 
            "carbon_emissions", "emission_data", "emissions", "co2_data"
        ]
        
        for keyword in emission_keywords:
            if keyword in extracted_data:
                data = extracted_data[keyword]
                if isinstance(data, list) and data:
                    return data
        
        return None
    
    def _normalize_data_types_and_ranges(self, enhanced_data: Dict, unit_context: Dict, session_id: str) -> List[str]:
        """
        Normalize data types and value ranges to match reference.json format
        
        Args:
            enhanced_data: Data to normalize
            unit_context: Unit context information
            session_id: Session identifier for logging
            
        Returns:
            List of normalization operations performed
        """
        normalizations_performed = []
        
        try:
            print(f"[Session {session_id}] 🔧 NORMALIZING DATA TYPES: Starting data type and range normalization")
            
            # 1. Normalize PLF values (percentage strings to decimal fractions)
            if "plf" in enhanced_data and enhanced_data["plf"]:
                plf_normalized = self._normalize_percentage_time_series(enhanced_data["plf"], "PLF", session_id)
                if plf_normalized:
                    enhanced_data["plf"] = plf_normalized
                    normalizations_performed.append("PLF Data Types")
            
            # 2. Normalize Auxiliary Power Consumed (percentage strings to decimal fractions)
            if "auxiliary_power_consumed" in enhanced_data and enhanced_data["auxiliary_power_consumed"]:
                aux_normalized = self._normalize_percentage_time_series(enhanced_data["auxiliary_power_consumed"], "Auxiliary Power", session_id)
                if aux_normalized:
                    enhanced_data["auxiliary_power_consumed"] = aux_normalized
                    normalizations_performed.append("Auxiliary Power Data Types")
            
            # 3. Normalize PAF values (string to numeric)
            if "PAF" in enhanced_data and enhanced_data["PAF"]:
                paf_normalized = self._normalize_numeric_time_series(enhanced_data["PAF"], "PAF", session_id)
                if paf_normalized:
                    enhanced_data["PAF"] = paf_normalized
                    normalizations_performed.append("PAF Data Types")
            
            # 4. Normalize Gross Power Generation (string to numeric, handle unit conversion)
            if "gross_power_generation" in enhanced_data and enhanced_data["gross_power_generation"]:
                gen_normalized = self._normalize_generation_data(enhanced_data["gross_power_generation"], unit_context, session_id)
                if gen_normalized:
                    enhanced_data["gross_power_generation"] = gen_normalized
                    normalizations_performed.append("Generation Data Types")
            
            # 5. Normalize Emission Factor (string to numeric)
            if "emission_factor" in enhanced_data and enhanced_data["emission_factor"]:
                emission_normalized = self._normalize_numeric_time_series(enhanced_data["emission_factor"], "Emission Factor", session_id)
                if emission_normalized:
                    enhanced_data["emission_factor"] = emission_normalized
                    normalizations_performed.append("Emission Factor Data Types")
            
            # 6. Normalize Coal Unit Efficiency (percentage string to decimal)
            if "coal_unit_efficiency" in enhanced_data and enhanced_data["coal_unit_efficiency"]:
                efficiency_normalized = self._normalize_percentage_value(enhanced_data["coal_unit_efficiency"], "Coal Unit Efficiency", session_id)
                if efficiency_normalized is not None:
                    enhanced_data["coal_unit_efficiency"] = efficiency_normalized
                    normalizations_performed.append("Efficiency Data Types")
            
            # 7. Normalize GCV values (string to numeric)
            gcv_fields = ["gcv_coal", "gcv_biomass", "gcv_natural_gas"]
            for field in gcv_fields:
                if field in enhanced_data and enhanced_data[field]:
                    gcv_normalized = self._normalize_numeric_value(enhanced_data[field], field, session_id)
                    if gcv_normalized is not None:
                        enhanced_data[field] = gcv_normalized
                        normalizations_performed.append(f"{field} Data Types")
            
            # 8. Normalize Fuel Years Percentage (align with reference format)
            if "fuel_type" in enhanced_data and enhanced_data["fuel_type"]:
                fuel_normalized = self._normalize_fuel_years_percentage(enhanced_data["fuel_type"], session_id)
                if fuel_normalized:
                    enhanced_data["fuel_type"] = fuel_normalized
                    normalizations_performed.append("Fuel Years Percentage Format")
            
            # 9. Normalize efficiency values to 0.x format (divide by 100 if percentage)
            efficiency_fields = [
                "open_cycle_gas_turbine_efficency",
                "closed_cylce_gas_turbine_efficency",
                "efficiency_loss_biomass_cofiring",
                "closed_cycle_efficiency",
                "closed_cycle_turbine_efficiency",
                "open_cycle_turbine_efficiency",
                "coal_unit_efficiency"  # Updated field name
            ]
            for field in efficiency_fields:
                if field in enhanced_data and enhanced_data[field]:
                    eff_normalized = self._normalize_efficiency_value(enhanced_data[field], field, session_id)
                    if eff_normalized is not None:
                        enhanced_data[field] = eff_normalized
                        normalizations_performed.append(f"{field} Efficiency Format")
            
            # 10. Normalize heat rate values
            heat_rate_fields = ["combined_cycle_heat_rate", "open_cycle_heat_rate"]
            for field in heat_rate_fields:
                if field in enhanced_data and enhanced_data[field]:
                    hr_normalized = self._normalize_numeric_value(enhanced_data[field], field, session_id)
                    if hr_normalized is not None:
                        enhanced_data[field] = hr_normalized
                        normalizations_performed.append(f"{field} Data Types")
            
            # 11. Normalize standalone numeric fields (EXCLUDE remaining_useful_life - it's a date string)
            standalone_numeric_fields = [
                "capacity", "unit_lifetime",
                "annual_operational_hours", "capex_required_retrofit_biomass",
                "capex_required_renovation_open_cycle", "capex_required_renovation_closed_cycle",
                "blending_percentage_of_biomass"  # Updated field name
            ]
            for field in standalone_numeric_fields:
                if field in enhanced_data and enhanced_data[field] and enhanced_data[field] != "default null":
                    numeric_normalized = self._normalize_numeric_value(enhanced_data[field], field, session_id)
                    if numeric_normalized is not None:
                        enhanced_data[field] = numeric_normalized
                        normalizations_performed.append(f"{field} Data Types")
            
            if normalizations_performed:
                print(f"[Session {session_id}] ✅ DATA TYPE NORMALIZATION COMPLETED: {', '.join(normalizations_performed)}")
            else:
                print(f"[Session {session_id}] ℹ️  No data type normalization needed")
            
            # 12. Calculate remaining useful life if missing
            remaining_life_calculated = self._calculate_remaining_useful_life(enhanced_data, session_id)
            if remaining_life_calculated:
                normalizations_performed.append("Remaining Useful Life Calculation")
                
        except Exception as e:
            logger.error(f"[Session {session_id}] Error in data type normalization: {str(e)}")
            print(f"[Session {session_id}] ❌ DATA TYPE NORMALIZATION ERROR: {str(e)}")
        
        return normalizations_performed
    
    def _normalize_percentage_time_series(self, data: List[Dict], field_name: str, session_id: str) -> Optional[List[Dict]]:
        """Convert percentage string time series to decimal fractions"""
        try:
            normalized_data = []
            changes_made = False
            
            for item in data:
                if isinstance(item, dict) and "value" in item:
                    original_value = item["value"]
                    original_year = item.get("year")
                    
                    normalized_item = item.copy()
                    
                    # Convert percentage string to decimal
                    if isinstance(original_value, str) and "%" in original_value:
                        # Remove % and convert to decimal
                        numeric_str = original_value.replace("%", "").strip()
                        try:
                            percentage_value = float(numeric_str)
                            decimal_value = percentage_value / 100.0  # Convert to decimal fraction
                            normalized_item["value"] = decimal_value
                            changes_made = True
                            print(f"[Session {session_id}] 🔧 {field_name} value: {original_value} → {decimal_value}")
                        except ValueError:
                            pass  # Keep original if conversion fails
                    
                    # Convert string year to integer
                    if isinstance(original_year, str):
                        try:
                            year_int = int(original_year)
                            normalized_item["year"] = year_int
                            changes_made = True
                            print(f"[Session {session_id}] 🔧 {field_name} year: '{original_year}' → {year_int}")
                        except ValueError:
                            pass  # Keep original if conversion fails
                    
                    normalized_data.append(normalized_item)
                else:
                    normalized_data.append(item)
            
            return normalized_data if changes_made else None
            
        except Exception as e:
            logger.error(f"Error normalizing {field_name} percentage time series: {str(e)}")
            return None
    
    def _normalize_numeric_time_series(self, data: List[Dict], field_name: str, session_id: str) -> Optional[List[Dict]]:
        """Convert string time series to numeric values"""
        try:
            normalized_data = []
            changes_made = False
            
            for item in data:
                if isinstance(item, dict) and "value" in item:
                    original_value = item["value"]
                    original_year = item.get("year")
                    
                    normalized_item = item.copy()
                    
                    # Convert string value to numeric
                    if isinstance(original_value, str):
                        try:
                            # Remove commas and whitespace
                            clean_value = original_value.replace(",", "").strip()
                            numeric_value = float(clean_value)
                            normalized_item["value"] = numeric_value
                            changes_made = True
                            print(f"[Session {session_id}] 🔧 {field_name} value: '{original_value}' → {numeric_value}")
                        except ValueError:
                            pass  # Keep original if conversion fails
                    
                    # Convert string year to integer
                    if isinstance(original_year, str):
                        try:
                            year_int = int(original_year)
                            normalized_item["year"] = year_int
                            changes_made = True
                            print(f"[Session {session_id}] 🔧 {field_name} year: '{original_year}' → {year_int}")
                        except ValueError:
                            pass  # Keep original if conversion fails
                    
                    normalized_data.append(normalized_item)
                else:
                    normalized_data.append(item)
            
            return normalized_data if changes_made else None
            
        except Exception as e:
            logger.error(f"Error normalizing {field_name} numeric time series: {str(e)}")
            return None
    
    def _normalize_generation_data(self, data: List[Dict], unit_context: Dict, session_id: str) -> Optional[List[Dict]]:
        """Convert generation data with unit conversion (kWh to MWh if needed)"""
        try:
            normalized_data = []
            changes_made = False
            capacity = self._extract_numeric_value(unit_context.get("capacity", 0))
            
            for item in data:
                if isinstance(item, dict) and "value" in item:
                    original_value = item["value"]
                    
                    if isinstance(original_value, str):
                        try:
                            numeric_value = float(original_value)
                            
                            # Check if value seems to be in kWh (too large for MWh)
                            # Compare with reference data ranges - if value > 10,000 MWh, likely in kWh
                            # Reference generation values are typically 2,000-6,000 MWh range
                            if numeric_value > 10000:
                                # Convert from kWh to MWh
                                converted_value = numeric_value / 1000
                                print(f"[Session {session_id}] 🔧 Generation: {original_value} kWh → {converted_value:.2f} MWh")
                                final_value = converted_value
                            else:
                                final_value = numeric_value
                            
                            normalized_item = item.copy()
                            normalized_item["value"] = final_value
                            normalized_data.append(normalized_item)
                            changes_made = True
                            
                        except ValueError:
                            normalized_data.append(item)  # Keep original if conversion fails
                    else:
                        normalized_data.append(item)  # Keep non-string values as-is
                else:
                    normalized_data.append(item)
            
            return normalized_data if changes_made else None
            
        except Exception as e:
            logger.error(f"Error normalizing generation data: {str(e)}")
            return None
    
    def _normalize_percentage_value(self, value: Any, field_name: str, session_id: str) -> Optional[float]:
        """Convert percentage string to decimal fraction"""
        try:
            if isinstance(value, str) and "%" in value:
                # Remove % and convert to decimal
                numeric_str = value.replace("%", "").strip()
                try:
                    percentage_value = float(numeric_str)
                    decimal_value = percentage_value / 100.0  # Convert to decimal fraction
                    
                    print(f"[Session {session_id}] 🔧 {field_name}: {value} → {decimal_value}")
                    return decimal_value
                except ValueError:
                    return None
            
            return None  # No change needed
            
        except Exception as e:
            logger.error(f"Error normalizing {field_name} percentage value: {str(e)}")
            return None
    
    def _normalize_numeric_value(self, value: Any, field_name: str, session_id: str) -> Optional[float]:
        """Convert string to numeric value, handling commas and various formats"""
        try:
            if isinstance(value, str):
                try:
                    # Remove commas and whitespace
                    clean_value = value.replace(",", "").strip()
                    numeric_value = float(clean_value)
                    print(f"[Session {session_id}] 🔧 {field_name}: '{value}' → {numeric_value}")
                    return numeric_value
                except ValueError:
                    return None
            
            return None  # No change needed
            
        except Exception as e:
            logger.error(f"Error normalizing {field_name} numeric value: {str(e)}")
            return None
    
    def _normalize_fuel_years_percentage(self, fuel_data: List[Dict], session_id: str) -> Optional[List[Dict]]:
        """Normalize fuel years percentage to match reference format"""
        try:
            normalized_data = []
            changes_made = False
            
            for fuel_item in fuel_data:
                if isinstance(fuel_item, dict) and "years_percentage" in fuel_item:
                    years_percentage = fuel_item["years_percentage"]
                    
                    if isinstance(years_percentage, dict):
                        normalized_years = {}
                        
                        for year, percentage in years_percentage.items():
                            if isinstance(percentage, str):
                                try:
                                    # Convert string percentage to decimal fraction
                                    numeric_percentage = float(percentage)
                                    decimal_fraction = numeric_percentage / 100.0
                                    normalized_years[year] = decimal_fraction
                                    changes_made = True
                                except ValueError:
                                    normalized_years[year] = percentage  # Keep original if conversion fails
                            else:
                                normalized_years[year] = percentage
                        
                        normalized_fuel = fuel_item.copy()
                        normalized_fuel["years_percentage"] = normalized_years
                        normalized_data.append(normalized_fuel)
                    else:
                        normalized_data.append(fuel_item)
                else:
                    normalized_data.append(fuel_item)
            
            if changes_made:
                print(f"[Session {session_id}] 🔧 Fuel Years Percentage: Converted string percentages to decimal fractions")
                return normalized_data
            
            return None  # No changes needed
            
        except Exception as e:
            logger.error(f"Error normalizing fuel years percentage: {str(e)}")
            return None
    
    def _calculate_remaining_useful_life(self, enhanced_data: Dict, session_id: str) -> bool:
        """
        Calculate remaining_useful_life for non-USA plants using country-specific retirement ages
        USA plants use Excel tool logic: Planned Retirement OR commencement_date + 50 years
        Non-USA plants use: commencement_date + country_specific_retirement_age

        Returns:
            bool: True if calculation was performed and data updated
        """
        try:
            # Get country and commencement_date
            country = enhanced_data.get("country", "default")
            commencement_date_str = enhanced_data.get("commencement_date")

            if not commencement_date_str:
                print(f"[Session {session_id}] ⚠️ No commencement_date found for remaining_useful_life calculation")
                return False

            # Skip if this is a USA plant (Excel tool handles it)
            if country and ("united states" in country.lower() or "usa" in country.lower() or "us" in country.lower()):
                print(f"[Session {session_id}] ✅ Skipping fallback remaining_useful_life calculation - USA plant uses Excel tool")
                return False

            # Parse commencement date
            from datetime import datetime, timedelta
            commencement_date = datetime.strptime(commencement_date_str, '%Y-%m-%dT%H:%M:%S.%fZ')

            # Get country-specific retirement age
            retirement_age_years = self.calculator.get_country_retirement_age(country)

            # Calculate retirement date
            retirement_date = commencement_date + timedelta(days=retirement_age_years * 365.25)

            # Format as ISO timestamp
            remaining_useful_life = retirement_date.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'

            # Update the data
            enhanced_data["remaining_useful_life"] = remaining_useful_life
            enhanced_data["unit_lifetime"] = float(retirement_age_years)

            print(f"[Session {session_id}] ✅ Calculated remaining_useful_life for {country}: {retirement_age_years} years -> {remaining_useful_life}")
            return True

        except Exception as e:
            logger.error(f"[Session {session_id}] Error calculating remaining_useful_life: {str(e)}")
            return False

    def _get_unit_operational_years(self, extracted_data: Dict, unit_context: Dict) -> List[str]:
        """
        Get the operational years for a unit based on commencement_date and retirement age
        Returns a list of year strings for fuel percentage generation
        """
        try:
            # Get commencement date
            commencement_date_str = extracted_data.get("commencement_date")
            if not commencement_date_str:
                # Fallback to recent years if no commencement date
                return ["2024", "2023", "2022", "2021", "2020"]

            # Parse commencement date
            from datetime import datetime
            commencement_date = datetime.strptime(commencement_date_str, '%Y-%m-%dT%H:%M:%S.%fZ')
            commencement_year = commencement_date.year

            # Get country and calculate retirement year
            country = unit_context.get("country", "default")
            retirement_age_years = self.calculator.get_country_retirement_age(country)
            retirement_year = commencement_year + retirement_age_years

            # Generate years from commencement to retirement
            # But limit to reasonable range (don't generate too many years)
            current_year = datetime.now().year
            start_year = max(commencement_year, current_year - 10)  # Start from max(commencement, current-10)
            end_year = min(retirement_year, current_year + 30)      # End at min(retirement, current+30)

            # Generate year strings in reverse order (newest first, like the original)
            operational_years = []
            for year in range(end_year, start_year - 1, -1):
                operational_years.append(str(year))

            # Ensure we have at least 5 years of data
            if len(operational_years) < 5:
                # Add recent years if needed
                for year in range(current_year, current_year - 5, -1):
                    year_str = str(year)
                    if year_str not in operational_years:
                        operational_years.append(year_str)

            return operational_years[:20]  # Limit to 20 years max

        except Exception as e:
            logger.error(f"Error calculating operational years: {str(e)}")
            # Fallback to recent years
            return ["2024", "2023", "2022", "2021", "2020"]


    def _normalize_efficiency_value(self, value, field: str, session_id: str):
        """
        Normalize efficiency values to 0.x decimal format (divide by 100 if percentage)

        Args:
            value: Input efficiency value (could be percentage or decimal)
            field: Field name for logging
            session_id: Session ID for logging

        Returns:
            Normalized efficiency value as decimal (0.x format) or None if invalid
        """
        try:
            if not value or value in ["", "default null", "Not available", "null"]:
                return None

            # Remove any non-numeric characters except decimal point and minus
            import re
            clean_value = re.sub(r'[^\d.-]', '', str(value))

            if not clean_value:
                return None

            numeric_value = float(clean_value)

            # If value is greater than 1, assume it's a percentage and divide by 100
            if numeric_value > 1:
                original_value = numeric_value
                numeric_value = numeric_value / 100
                print(f"[Session {session_id}] ⚡ Converted {field} from percentage: {original_value}% → {numeric_value:.4f}")
            else:
                print(f"[Session {session_id}] ✅ {field} already in decimal format: {numeric_value:.4f}")

            # Format to reasonable decimal places
            return round(numeric_value, 4)

        except (ValueError, TypeError) as e:
            logger.error(f"Error normalizing efficiency value for {field}: {str(e)}")
            return None


# Global fallback engine
FALLBACK_ENGINE = FallbackCalculationEngine()


def enhance_unit_with_calculations(extracted_data: Dict, unit_context: Dict, session_id: str = "unknown") -> Dict:
    """
    Convenience function to enhance unit data with fallback calculations
    
    Args:
        extracted_data: Data extracted from search
        unit_context: Unit context information
        session_id: Session identifier
        
    Returns:
        Enhanced data with calculated missing fields
    """
    return FALLBACK_ENGINE.enhance_unit_data(extracted_data, unit_context, session_id)