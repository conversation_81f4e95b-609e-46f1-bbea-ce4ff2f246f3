#!/usr/bin/env python3
"""
Gas Plant Calculation Engine
============================

Comprehensive gas plant calculations based on Natural Gas.xlsx formulas.
Supports all gas technologies: OCGT, CCGT, RICE/ICE, CHP, IGCC, Hydrogen-ready, Microturbines.

Key Features:
- Technology-specific efficiency averages
- Capacity-based auxiliary power (inversely proportional)
- Heat rate and efficiency calculations
- Emission factor calculations
- Fuel unit conversions (MCF/SCF to kg)
"""

import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

# Set up logging
logger = logging.getLogger(__name__)

class GasCalculationEngine:
    """
    Gas Plant Calculation Engine
    
    Implements all formulas from Natural Gas.xlsx:
    - Generation calculations (Cases 4a, 5a, 5b)
    - Heat rate and efficiency calculations
    - Emission factor calculations
    - Technology-specific parameters
    """
    
    def __init__(self):
        """Initialize gas calculation engine with reference data"""
        try:
            from agent.reference_data import CALCULATOR
            self.calculator = CALCULATOR
            self.gas_constants = self.calculator.ref_data.gas_constants
            self.gas_tech_params = self.calculator.ref_data.gas_technology_parameters
            logger.info("Gas calculation engine initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize gas calculation engine: {e}")
            # Fallback constants
            self.gas_constants = {
                'gcv_natural_gas': 13330,
                'gcv_unit': 'kCal/kg',
                'emission_factor': 2.6928,
                'gas_diesel_emission_factor': 3.1863,
                'mcf_to_kg': 20.29,
                'scf_to_kg': 0.02029
            }
            self.gas_tech_params = {}
    
    def calculate_gas_unit_parameters(self, plant_name: str, unit_data: Dict[str, Any], session_id: str = "unknown") -> Dict[str, Any]:
        """
        Main method for gas plant calculations
        
        Args:
            plant_name: Name of the gas plant
            unit_data: Unit data dictionary
            session_id: Session identifier for logging
            
        Returns:
            Enhanced unit data with gas-specific calculations
        """
        logger.info(f"[Session {session_id}] Calculating gas unit parameters for {plant_name}")
        
        try:
            enhanced_data = unit_data.copy()
            
            # Extract basic info
            capacity = self._extract_numeric_value(unit_data.get("capacity", 0))
            technology = unit_data.get("technology", "CCGT").upper()
            
            logger.info(f"[Session {session_id}] Gas plant: {capacity} MW {technology}")
            
            # Add gas-specific fields
            enhanced_data.update({
                'gcv_gas': self.gas_constants['gcv_natural_gas'],
                'gcv_gas_unit': self.gas_constants['gcv_unit'],
                'gas_unit_efficiency': self._get_technology_efficiency(technology),
                'emission_factor_gas': self.gas_constants['emission_factor']
            })
            
            # Calculate auxiliary power using gas-specific logic
            aux_power = self.calculator.estimate_auxiliary_power_consumption(capacity, technology)
            if 'auxiliary_power_consumed' in enhanced_data:
                # Update existing auxiliary power data
                aux_data = enhanced_data['auxiliary_power_consumed']
                if isinstance(aux_data, list) and aux_data:
                    aux_data[0]['value'] = aux_power / 100  # Convert percentage to decimal
            
            # Calculate heat rate if efficiency is available
            efficiency = enhanced_data.get('gas_unit_efficiency', 0)
            if efficiency > 0:
                heat_rate = self.calculate_gas_heat_rate(efficiency=efficiency)
                if 'heat_rate' in enhanced_data and isinstance(enhanced_data['heat_rate'], list):
                    if enhanced_data['heat_rate']:
                        enhanced_data['heat_rate'][0]['value'] = heat_rate
            
            logger.info(f"[Session {session_id}] Gas calculations completed successfully")
            return enhanced_data
            
        except Exception as e:
            logger.error(f"[Session {session_id}] Error in gas unit calculations: {e}")
            return unit_data
    
    def _get_technology_efficiency(self, technology: str) -> float:
        """
        Get average efficiency for gas technology
        
        Args:
            technology: Gas technology type (OCGT, CCGT, etc.)
            
        Returns:
            Average efficiency as decimal (e.g., 0.585 for 58.5%)
        """
        tech_params = self.gas_tech_params.get(technology.upper())
        if tech_params:
            return tech_params['efficiency_average']
        
        # Default efficiencies if not found
        defaults = {
            'OCGT': 0.34,
            'CCGT': 0.585,
            'RICE': 0.44,
            'ICE': 0.44,
            'CHP': 0.75,
            'COGENERATION': 0.75,
            'IGCC': 0.425,
            'HYDROGEN_READY': 0.55,
            'MICROTURBINES': 0.30
        }
        
        return defaults.get(technology.upper(), 0.35)  # Default 35%
    
    def calculate_gas_heat_rate(self, efficiency: Optional[float] = None, 
                               fuel_consumed_kg: Optional[float] = None, 
                               gross_generation_mwh: Optional[float] = None) -> Optional[float]:
        """
        Gas plant heat rate calculation using Natural Gas.xlsx formulas
        
        Args:
            efficiency: Plant efficiency (decimal, e.g., 0.585)
            fuel_consumed_kg: Fuel consumed in kg
            gross_generation_mwh: Gross generation in MWh
            
        Returns:
            Heat rate in kCal/kWh or None if calculation not possible
        """
        try:
            if efficiency and efficiency > 0:
                # Case 1.1: heat_rate = 860.42/plant_efficiency
                heat_rate = 860.42 / efficiency
                logger.debug(f"Heat rate from efficiency: {heat_rate:.2f} kCal/kWh")
                return heat_rate
                
            elif fuel_consumed_kg and gross_generation_mwh and gross_generation_mwh > 0:
                # Case 1.2: heat_rate = (fuel_consumed_kg * GCV) / Gross_Generation_MWh
                heat_rate = (fuel_consumed_kg * self.gas_constants['gcv_natural_gas']) / gross_generation_mwh
                logger.debug(f"Heat rate from fuel consumption: {heat_rate:.2f} kCal/kWh")
                return heat_rate
                
            return None
            
        except Exception as e:
            logger.error(f"Error calculating gas heat rate: {e}")
            return None
    
    def calculate_gas_efficiency(self, gross_generation_mwh: float, fuel_consumed_kg: float) -> Optional[float]:
        """
        Gas plant efficiency calculation
        
        Args:
            gross_generation_mwh: Gross generation in MWh
            fuel_consumed_kg: Fuel consumed in kg
            
        Returns:
            Efficiency as decimal (e.g., 0.585) or None if calculation not possible
        """
        try:
            if gross_generation_mwh <= 0 or fuel_consumed_kg <= 0:
                return None
                
            # Efficiency = (Gross_Generation_MWh * 860,420) / (GCV_fuel * Fuel_Consumed_kg * 1000)
            numerator = gross_generation_mwh * 860420
            denominator = self.gas_constants['gcv_natural_gas'] * fuel_consumed_kg * 1000
            
            efficiency = numerator / denominator
            logger.debug(f"Calculated gas efficiency: {efficiency:.3f} ({efficiency*100:.1f}%)")
            return efficiency
            
        except Exception as e:
            logger.error(f"Error calculating gas efficiency: {e}")
            return None
    
    def calculate_gas_generation(self, case: str, **kwargs) -> Optional[float]:
        """
        Gas plant generation calculations using Natural Gas.xlsx formulas
        
        Args:
            case: Calculation case ('4a', '5a', '5b')
            **kwargs: Parameters for calculation
            
        Returns:
            Gross generation in MWh or None if calculation not possible
        """
        try:
            if case == '4a':  # From net generation
                net_generation = kwargs.get('net_generation')
                aux_power_percent = kwargs.get('aux_power_percent', 0.03)
                
                if net_generation and aux_power_percent < 1:
                    gross_generation = net_generation / (1 - aux_power_percent)
                    logger.debug(f"Case 4a - Gross generation: {gross_generation:.2f} MWh")
                    return gross_generation
                    
            elif case == '5a':  # From fuel + efficiency
                fuel_kg = kwargs.get('fuel_kg')
                efficiency = kwargs.get('efficiency')
                
                if fuel_kg and efficiency:
                    gross_generation = (fuel_kg * self.gas_constants['gcv_natural_gas'] * efficiency) / (860 * 1000)
                    logger.debug(f"Case 5a - Gross generation: {gross_generation:.2f} MWh")
                    return gross_generation
                    
            elif case == '5b':  # From fuel + heat rate
                fuel_kg = kwargs.get('fuel_kg')
                heat_rate = kwargs.get('heat_rate')
                
                if fuel_kg and heat_rate:
                    gross_generation = (fuel_kg * self.gas_constants['gcv_natural_gas']) / (heat_rate * 1000)
                    logger.debug(f"Case 5b - Gross generation: {gross_generation:.2f} MWh")
                    return gross_generation
            
            return None
            
        except Exception as e:
            logger.error(f"Error calculating gas generation (case {case}): {e}")
            return None
    
    def calculate_gas_emission_factor(self, fuel_quantity: float, fuel_unit: str, 
                                    gross_generation_mwh: float) -> Optional[float]:
        """
        Calculate emission factor for gas plants
        
        Args:
            fuel_quantity: Fuel quantity
            fuel_unit: Fuel unit (MCF, SCF, kg)
            gross_generation_mwh: Gross generation in MWh
            
        Returns:
            Emission factor in kgCO2e/kWh or None if calculation not possible
        """
        try:
            # Convert fuel to kg
            if fuel_unit.upper() == 'MCF':
                fuel_kg = fuel_quantity * self.gas_constants['mcf_to_kg']
            elif fuel_unit.upper() == 'SCF':
                fuel_kg = fuel_quantity * self.gas_constants['scf_to_kg']
            elif fuel_unit.upper() == 'KG':
                fuel_kg = fuel_quantity
            else:
                logger.warning(f"Unknown fuel unit: {fuel_unit}, assuming kg")
                fuel_kg = fuel_quantity
            
            # Calculate emissions
            total_emissions_kg = fuel_kg * self.gas_constants['emission_factor']
            emission_factor = total_emissions_kg / (gross_generation_mwh * 1000)  # kgCO2e/kWh
            
            logger.debug(f"Gas emission factor: {emission_factor:.6f} kgCO2e/kWh")
            return emission_factor
            
        except Exception as e:
            logger.error(f"Error calculating gas emission factor: {e}")
            return None
    
    def convert_fuel_units(self, fuel_quantity: float, from_unit: str, to_unit: str = 'kg') -> float:
        """
        Convert fuel units for gas plants
        
        Args:
            fuel_quantity: Fuel quantity
            from_unit: Source unit (MCF, SCF, kg)
            to_unit: Target unit (default: kg)
            
        Returns:
            Converted fuel quantity
        """
        try:
            # Convert to kg first
            if from_unit.upper() == 'MCF':
                fuel_kg = fuel_quantity * self.gas_constants['mcf_to_kg']
            elif from_unit.upper() == 'SCF':
                fuel_kg = fuel_quantity * self.gas_constants['scf_to_kg']
            elif from_unit.upper() == 'KG':
                fuel_kg = fuel_quantity
            else:
                logger.warning(f"Unknown source unit: {from_unit}, assuming kg")
                fuel_kg = fuel_quantity
            
            # Convert from kg to target unit
            if to_unit.upper() == 'KG':
                return fuel_kg
            elif to_unit.upper() == 'MCF':
                return fuel_kg / self.gas_constants['mcf_to_kg']
            elif to_unit.upper() == 'SCF':
                return fuel_kg / self.gas_constants['scf_to_kg']
            else:
                logger.warning(f"Unknown target unit: {to_unit}, returning kg")
                return fuel_kg
                
        except Exception as e:
            logger.error(f"Error converting fuel units: {e}")
            return fuel_quantity
    
    def _extract_numeric_value(self, value: Union[str, int, float]) -> float:
        """Extract numeric value from various input formats"""
        try:
            if isinstance(value, (int, float)):
                return float(value)
            elif isinstance(value, str):
                # Remove common suffixes and separators
                cleaned = value.replace("MW", "").replace(",", "").strip()
                return float(cleaned)
            else:
                return 0.0
        except (ValueError, TypeError):
            return 0.0
