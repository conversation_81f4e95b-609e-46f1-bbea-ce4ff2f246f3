#!/usr/bin/env python3
"""
Test the new USA Excel calculation engine with Antelope Valley Station
"""

import sys
import json
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH')

from usa_excel_calculation_engine import create_usa_excel_calculation_engine

def test_usa_excel_engine():
    """Test the USA Excel calculation engine with Antelope Valley Station"""
    
    print("🧪 TESTING USA EXCEL CALCULATION ENGINE")
    print("=" * 60)
    
    # Create the USA Excel calculation engine
    usa_engine = create_usa_excel_calculation_engine()
    
    # Test with Antelope Valley data (correct name from Excel)
    plant_name = "Antelope Valley"
    
    # Unit data for Antelope Valley Station Unit 2
    unit_data = {
        'capacity': 238.5,  # MW (Unit 2 capacity)
        'unit_number': '2',
        'technology': 'subcritical',
        'coal_type': 'lignite'
    }
    
    print(f"📊 TESTING: {plant_name}")
    print(f"  • Unit: {unit_data['unit_number']}")
    print(f"  • Capacity: {unit_data['capacity']} MW")
    print(f"  • Technology: {unit_data['technology']}")
    print()
    
    # Test data extraction first
    print("🔍 STEP 1: EXTRACTING USA EXCEL DATA")
    print("-" * 40)
    
    plant_excel_data = usa_engine.extract_plant_data_from_usa_excel(plant_name)
    
    if plant_excel_data:
        print("✅ USA Excel data extracted successfully!")
        print(f"📅 Years with data: {list(plant_excel_data.keys())}")
        
        for year, year_data in plant_excel_data.items():
            print(f"\n  📅 {year}:")
            total_generation = sum(unit['net_generation_mwh'] for unit in year_data)
            total_fuel = sum(unit['fuel_consumption_quantity'] for unit in year_data)
            print(f"    • Records: {len(year_data)}")
            print(f"    • Total Net Generation: {total_generation:,.0f} MWh")
            print(f"    • Total Fuel Consumption: {total_fuel:,.0f} units")
            
            # Show individual unit data
            for i, unit in enumerate(year_data):
                if unit['net_generation_mwh'] > 0:
                    print(f"    • Unit {i+1}: {unit['net_generation_mwh']:,.0f} MWh, {unit['fuel_consumption_quantity']:,.0f} fuel units")
    else:
        print("❌ No USA Excel data found!")
        return
    
    # Test PLF calculations
    print(f"\n🔧 STEP 2: CALCULATING PLF USING USA DATA")
    print("-" * 40)
    
    # Test Case 1: Plant Level PLF
    print("📊 PLF Case 1 (Plant Level):")
    plant_capacity_mw = unit_data['capacity'] * 2  # Assume 2 units
    plf_case1_results = usa_engine.calculate_plf_case1_plant_level(plant_excel_data, plant_capacity_mw)
    
    if plf_case1_results:
        for year, result in plf_case1_results.items():
            print(f"  📅 {year}: PLF = {result['plf']:.1%}, Gross Gen = {result['gross_generation_mwh']:,.0f} MWh")
    else:
        print("  ❌ No Case 1 results")
    
    # Test Case 4: Unit Level PLF
    print("\n📊 PLF Case 4 (Unit Level):")
    plf_case4_results = usa_engine.calculate_plf_case4_unit_level(plant_excel_data, unit_data['capacity'], unit_data['unit_number'])
    
    if plf_case4_results:
        for year, result in plf_case4_results.items():
            print(f"  📅 {year}: PLF = {result['plf']:.1%}, Gross Gen = {result['gross_generation_mwh']:,.0f} MWh")
    else:
        print("  ❌ No Case 4 results")
    
    # Test complete unit parameter calculation
    print(f"\n🎯 STEP 3: COMPLETE UNIT PARAMETER CALCULATION")
    print("-" * 50)
    
    results = usa_engine.calculate_unit_parameters_usa(plant_name, unit_data)
    
    if results:
        print("✅ CALCULATION SUCCESSFUL!")
        
        # Display key results
        key_fields = [
            'plf_unit',
            'auxiliary_power_percent',
            'calculation_method',
            'data_source',
            'years_with_data'
        ]
        
        print("\n📋 KEY RESULTS:")
        for field in key_fields:
            if field in results:
                value = results[field]
                if field == 'plf_unit':
                    print(f"  ✅ {field}: {value:.1%}")
                elif field == 'auxiliary_power_percent':
                    print(f"  ✅ {field}: {value:.1%}")
                else:
                    print(f"  ✅ {field}: {value}")
        
        # Display time series data
        print("\n📊 TIME SERIES DATA:")
        if 'time_series_plf' in results:
            print("  PLF by year:")
            for year, plf in sorted(results['time_series_plf'].items()):
                print(f"    📅 {year}: {plf:.1%}")
        
        if 'time_series_gross_generation' in results:
            print("  Gross Generation by year:")
            for year, gen in sorted(results['time_series_gross_generation'].items()):
                print(f"    📅 {year}: {gen:,.0f} MWh")
        
        # Test JSON structure compatibility
        print(f"\n🔧 STEP 4: JSON STRUCTURE COMPATIBILITY")
        print("-" * 40)
        
        # Transform to expected JSON structure
        json_structure = {
            "unit_number": unit_data['unit_number'],
            "capacity": unit_data['capacity'],
            "technology": unit_data['technology'],
            "coal_type": unit_data['coal_type'],
            "plf": [],
            "auxiliary_power_consumed": [],
            "gross_power_generation": [],
            "calculation_method": results.get('calculation_method', 'Unknown'),
            "data_source": results.get('data_source', 'Unknown')
        }
        
        # Populate PLF array
        if 'time_series_plf' in results:
            for year, value in sorted(results['time_series_plf'].items()):
                json_structure['plf'].append({
                    "year": str(year),
                    "value": f"{value:.3f}"
                })
        
        # Populate auxiliary power array
        if 'time_series_auxiliary_power' in results:
            for year, value in sorted(results['time_series_auxiliary_power'].items()):
                json_structure['auxiliary_power_consumed'].append({
                    "year": str(year),
                    "value": f"{value * 100:.1f}"  # Convert to percentage
                })
        
        # Populate gross power generation array
        if 'time_series_gross_generation' in results:
            for year, value in sorted(results['time_series_gross_generation'].items()):
                json_structure['gross_power_generation'].append({
                    "year": str(year),
                    "value": f"{value:.0f}"
                })
        
        print("✅ JSON STRUCTURE CREATED:")
        print(json.dumps(json_structure, indent=2))
        
        # Check if arrays are populated
        print("\n🔍 ARRAY POPULATION CHECK:")
        arrays_to_check = ['plf', 'auxiliary_power_consumed', 'gross_power_generation']
        for array_name in arrays_to_check:
            array_data = json_structure[array_name]
            if array_data:
                print(f"  ✅ {array_name}: {len(array_data)} entries")
            else:
                print(f"  ❌ {array_name}: Empty")
    
    else:
        print("❌ CALCULATION FAILED!")

if __name__ == "__main__":
    test_usa_excel_engine()
    
    print("\n" + "=" * 60)
    print("🎯 SUMMARY")
    print("=" * 60)
    print()
    print("✅ Created USA Excel calculation engine using real data")
    print("✅ Implemented PLF Case 1 (Plant Level) and Case 4 (Unit Level)")
    print("✅ Used actual Net Generation and Fuel Consumption from Coal yearly sheets")
    print("✅ Applied auxiliary power calculations from Assumptions sheet")
    print("✅ Generated time series data for PLF, auxiliary power, and gross generation")
    print()
    print("🚀 NEXT STEPS:")
    print("1. Integrate this engine into the unit extraction pipeline")
    print("2. Test with both Antelope Valley units")
    print("3. Verify the calculated values match expected results")
    print("4. Replace the old calculation system with this USA-specific engine")
